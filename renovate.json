{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended"], "labels": ["🤖 Dependencies"], "commitMessagePrefix": "chore(deps): ", "lockFileMaintenance": {"enabled": true, "automerge": true}, "platformAutomerge": true, "postUpdateOptions": ["gomodTidy", "gomodUpdateImportPaths", "pnpmDedupe"], "packageRules": [{"groupName": "non-major dependencies", "matchUpdateTypes": ["digest", "pin", "patch", "minor"], "automerge": true}], "ignoreDeps": ["github.com/libdns/libdns", "github.com/libdns/cloudflare", "github.com/libdns/tencentcloud", "github.com/libdns/duckdns", "github.com/libdns/gcore"]}