module github.com/tnb-labs/panel

go 1.24.0

require (
	github.com/bddjr/hlfhr v1.3.8
	github.com/beevik/ntp v1.4.3
	github.com/creack/pty v1.1.24
	github.com/devhaozi/westcn v0.0.0-20250407083704-b49375823872
	github.com/expr-lang/expr v1.17.2
	github.com/go-chi/chi/v5 v5.2.1
	github.com/go-gormigrate/gormigrate/v2 v2.1.4
	github.com/go-rat/chix v1.2.0
	github.com/go-rat/gormstore v1.0.6
	github.com/go-rat/sessions v1.1.0
	github.com/go-rat/utils v1.1.5
	github.com/go-resty/resty/v2 v2.16.5
	github.com/go-sql-driver/mysql v1.9.2
	github.com/golang-cz/httplog v0.0.0-20241002114323-98e09d6f537a
	github.com/gomodule/redigo v1.9.2
	github.com/google/wire v0.6.0
	github.com/gookit/color v1.5.4
	github.com/gookit/validate v1.5.4
	github.com/gorilla/websocket v1.5.3
	github.com/hashicorp/go-version v1.7.0
	github.com/knadh/koanf/parsers/yaml v1.0.0
	github.com/knadh/koanf/providers/file v1.2.0
	github.com/knadh/koanf/v2 v2.2.0
	github.com/leonelquinteros/gotext v1.7.1
	github.com/lib/pq v1.10.9
	github.com/libdns/alidns v1.0.4
	github.com/libdns/cloudflare v0.1.3
	github.com/libdns/cloudns v1.0.0
	github.com/libdns/duckdns v0.2.0
	github.com/libdns/gcore v0.0.0-20250127070537-4a9d185c9d20
	github.com/libdns/godaddy v1.0.3
	github.com/libdns/hetzner v0.0.1
	github.com/libdns/huaweicloud v0.3.5
	github.com/libdns/libdns v0.2.3
	github.com/libdns/linode v0.4.1
	github.com/libdns/namecheap v0.0.0-20250228022813-d8b4b66c5072
	github.com/libdns/namedotcom v0.3.3
	github.com/libdns/namesilo v0.1.2
	github.com/libdns/porkbun v0.2.0
	github.com/libdns/tencentcloud v1.2.0
	github.com/libdns/vercel v0.0.2
	github.com/mholt/acmez/v3 v3.1.2
	github.com/ncruces/go-sqlite3 v0.25.2
	github.com/ncruces/go-sqlite3/gormlite v0.24.0
	github.com/orandin/slog-gorm v1.4.0
	github.com/pquerna/otp v1.4.1-0.20241231231527-5971b1ef1d66
	github.com/robfig/cron/v3 v3.0.1
	github.com/samber/lo v1.50.0
	github.com/sethvargo/go-limiter v1.0.1-0.20250412144437-fa26982c7e1a
	github.com/shirou/gopsutil v3.21.11+incompatible
	github.com/spf13/cast v1.8.0
	github.com/stretchr/testify v1.10.0
	github.com/tufanbarisyildirim/gonginx v0.0.0-20250429180229-7e931b1d4276
	github.com/urfave/cli/v3 v3.3.3
	golang.org/x/crypto v0.38.0
	golang.org/x/net v0.40.0
	gopkg.in/natefinch/lumberjack.v2 v2.2.1
	gopkg.in/yaml.v3 v3.0.1
	gorm.io/gorm v1.26.1
)

require (
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/G-Core/gcore-dns-sdk-go v0.2.9 // indirect
	github.com/boombuler/barcode v1.0.2 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/fsnotify/fsnotify v1.9.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.8 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-rat/securecookie v1.1.0 // indirect
	github.com/go-viper/mapstructure/v2 v2.2.1 // indirect
	github.com/gofiber/schema v1.3.0 // indirect
	github.com/google/go-querystring v1.1.0 // indirect
	github.com/gookit/filter v1.2.2 // indirect
	github.com/gookit/goutil v0.6.18 // indirect
	github.com/imega/luaformatter v0.0.0-20211025140405-86b0a68d6bef // indirect
	github.com/jaevor/go-nanoid v1.4.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/knadh/koanf/maps v0.1.2 // indirect
	github.com/linode/linodego v1.23.0 // indirect
	github.com/miekg/dns v1.1.40 // indirect
	github.com/mitchellh/copystructure v1.2.0 // indirect
	github.com/mitchellh/reflectwalk v1.0.2 // indirect
	github.com/ncruces/julianday v1.0.0 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.1096 // indirect
	github.com/tetratelabs/wazero v1.9.0 // indirect
	github.com/timtadh/data-structures v0.5.3 // indirect
	github.com/timtadh/lexmachine v0.2.2 // indirect
	github.com/tklauser/go-sysconf v0.3.14 // indirect
	github.com/tklauser/numcpus v0.9.0 // indirect
	github.com/xo/terminfo v0.0.0-20220910002029-abceb7e1c41e // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	golang.org/x/exp v0.0.0-20250305212735-054e65f0b394 // indirect
	golang.org/x/sync v0.14.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.25.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
)

replace github.com/mholt/acmez/v3 => github.com/tnb-labs/acmez/v3 v3.0.0-20250416074048-0e9f6b5f8ec5
