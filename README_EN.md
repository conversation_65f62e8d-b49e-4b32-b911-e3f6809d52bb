<p align="right">
[<a href="README.md">简体中文</a>] | [English]
</p>

<h1 align="center" style="font-size: 40px">Rat Panel</h1>

<div align="center">

[![Go](https://img.shields.io/github/go-mod/go-version/tnb-labs/panel)](https://go.dev/)
[![Release](https://img.shields.io/github/release/tnb-labs/panel.svg)](https://github.com/tnb-labs/panel/releases)
[![Test](https://github.com/tnb-labs/panel/actions/workflows/test.yml/badge.svg)](https://github.com/tnb-labs/panel/actions)
[![Report Card](https://goreportcard.com/badge/github.com/tnb-labs/panel)](https://goreportcard.com/report/github.com/tnb-labs/panel)
[![Stars](https://img.shields.io/github/stars/tnb-labs/panel?style=flat)](https://github.com/tnb-labs/panel)
[![License](https://img.shields.io/github/license/tnb-labs/panel)](https://www.gnu.org/licenses/agpl-3.0.html)

</div>

A new generation of all-in-one server operation and maintenance management panel. Simple and lightweight, efficient operation and maintenance.

Website: [panel.haozi.net](https://panel.haozi.net) | QQ group: [12370907](https://jq.qq.com/?_wv=1027&k=I1oJKSTH) | WeChat group: [Copy this link](https://work.weixin.qq.com/gm/d8ebf618553398d454e3378695c858b6)

## Advantages

1. **Extremely low occupancy:** Developed in Go language, small installation package, low occupancy, single file operation, will not affect system performance
2. **Low Destructiveness:** Designed to minimize additional modifications to the system, we make the fewest modifications to the system among similar products
3. **Follow the Times:** The overall design is at the forefront of the times, with good compatibility with new systems, leading in the same type of products
4. **Efficient Operation and Maintenance:** Complete functions, strong customization capabilities, can quickly deploy small websites, and deploy complex applications based on customized requirements
5. **Offline Operation:** Support offline mode, and even stop the panel process after deployment, without affecting any existing services
6. **Safe and Stable:** The panel adopts a variety of industry technologies to ensure the security of the body, and has been running stably in multiple survival environments for a long time
7. **Fully Open Source:** Few fully open source panels, you can freely modify and develop the panel on the premise of complying with the open source agreement
8. **Permanently free:** Commit to the panel body will not introduce any charging/authorization functions in the future, and will be permanently free to use

## UI Screenshots

![UI Screenshots](.github/assets/ui.png)

## Quick Install

[https://ratpanel.github.io/quickstart/install.html](https://ratpanel.github.io/quickstart/install.html)

## Partners

If the Rat Panel is helpful to you, welcome to sponsor us, also thanks to the following supporters/sponsors:

![Alipay](https://github.com/user-attachments/assets/d000b147-6da1-467a-9d80-9a3e8078602a) ![WeChat](https://github.com/user-attachments/assets/a53ff212-7076-487e-88bd-c93f6e98df1d)

<a href="https://www.weixiaoduo.com/">
  <img height="80" src=".github/assets/wxd.png" alt="微晓朵">
</a>
<a href="https://www.dkdun.cn/aff/MQZZNVHQ">
  <img height="80" src=".github/assets/dk.png" alt="林枫云">
</a>
<a href="https://su.sctes.com/register?code=8st689ujpmm2p">
  <img height="80" src=".github/assets/sctes.png" alt="无畏云加速">
</a>
<a href="https://su.sctes.com/register?code=8st689ujpmm2p">
  <img height="80" src=".github/assets/wafpro.png" alt="WAFPRO">
</a>
<a href="https://scdn.ddunyun.com/">
  <img height="80" src=".github/assets/ddunyun.png" alt="盾云SCDN">
</a>
<a href="https://hongtoutong.com">
  <img height="80" src=".github/assets/htt.png" alt="红透通">
</a>
<a href="https://1ms.run">
  <img height="80" src=".github/assets/1ms.svg" alt="毫秒镜像提供经过审核的 Docker 镜像加速服务">
</a>

<p align="center">
  <a target="_blank" href="https://afdian.com/a/tnblabs">
    <img alt="sponsors" src="https://github.com/tnb-labs/sponsor/blob/main/sponsors.svg?raw=true"/>
  </a>
</p>

## Star History

<a href="https://star-history.com/#tnb-labs/panel&Date">
 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=tnb-labs/panel&type=Date&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=tnb-labs/panel&type=Date" />
   <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=tnb-labs/panel&type=Date" />
 </picture>
</a>
