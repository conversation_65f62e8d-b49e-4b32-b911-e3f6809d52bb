# 服务器运维面板项目文档

## 项目概述

服务器运维面板是一个轻量级、高效的服务器管理工具，专注于提供核心的服务器运维功能，采用现代化的技术栈开发，确保可商用性。

## 文档目录

1. [项目规划](project_plan.md) - 项目整体规划、功能模块和开发路线图
2. [技术实现细节](technical_implementation.md) - 后端和前端的技术实现方案
3. [数据库设计](database_design.md) - 数据库表结构和模型设计
4. [API设计](api_design.md) - RESTful API接口设计和WebSocket接口

## 技术栈

### 后端
- **语言**: Go
- **HTTP框架**: Hertz (字节跳动开源的高性能HTTP框架)
- **ORM**: GORM
- **数据库**: SQLite

### 前端
- **框架**: Vue 3 + Vite
- **UI组件库**: TDesign-Vue (腾讯开源的企业级设计系统)
- **状态管理**: Pinia
- **HTTP客户端**: Axios

## 核心功能

- **系统监控**: CPU、内存、磁盘、网络等实时监控
- **服务管理**: 基于systemctl的服务启停、状态查看
- **网站管理**: 多站点、多域名、SSL证书、Nginx配置
- **数据库管理**: MySQL/PostgreSQL数据库创建、用户管理
- **文件管理**: 文件浏览、上传下载、权限修改
- **定时任务**: Cron任务创建和管理
- **容器管理**: Docker/Podman容器创建、管理、监控
- **安全管理**: 用户认证、防火墙配置

## 快速开始

### 环境要求

- Go 1.20+
- Node.js 18+
- npm 9+

### 后端开发

1. 克隆仓库
   ```bash
   git clone https://github.com/yourusername/server-panel.git
   cd server-panel/server
   ```

2. 安装依赖
   ```bash
   go mod tidy
   ```

3. 运行开发服务器
   ```bash
   go run cmd/server/main.go
   ```

### 前端开发

1. 进入前端目录
   ```bash
   cd ../client
   ```

2. 安装依赖
   ```bash
   npm install
   ```

3. 运行开发服务器
   ```bash
   npm run dev
   ```

## 部署指南

### 构建

1. 构建后端
   ```bash
   cd server
   go build -o panel cmd/server/main.go
   ```

2. 构建前端
   ```bash
   cd client
   npm run build
   ```

### 部署

1. 将编译好的后端二进制文件和前端静态文件部署到服务器
2. 配置服务器环境
3. 启动服务
   ```bash
   ./panel
   ```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详情请参阅 [LICENSE](../LICENSE) 文件
