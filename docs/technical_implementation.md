# 服务器运维面板技术实现细节

## 一、后端实现

### 1. Hertz框架路由注册

```go
// router/router.go
package router

import (
    "github.com/cloudwego/hertz/pkg/app/server"
    "github.com/cloudwego/hertz/pkg/route"
    "gorm.io/gorm"

    "yourproject/biz/handler"
    "yourproject/infra/middleware"
)

func Register(h *server.Hertz, db *gorm.DB) {
    // 全局中间件
    h.Use(middleware.Cors())
    h.Use(middleware.RequestID())
    h.Use(middleware.Logger())
    h.Use(middleware.Recovery())

    // API路由组
    api := h.Group("/api")
    {
        // 认证相关
        auth := api.Group("/auth")
        {
            auth.POST("/login", handler.NewAuthHandler(db).Login)
            auth.POST("/logout", middleware.Auth(), handler.NewAuthHandler(db).Logout)
            auth.GET("/profile", middleware.Auth(), handler.NewAuthHandler(db).GetProfile)
        }

        // 系统管理
        system := api.Group("/system", middleware.Auth())
        {
            system.GET("/info", handler.NewSystemHandler(db).GetInfo)
            system.GET("/status", handler.NewSystemHandler(db).GetStatus)
            // 服务管理
            service := system.Group("/service")
            {
                service.GET("/list", handler.NewServiceHandler(db).List)
                service.POST("/start", handler.NewServiceHandler(db).Start)
                service.POST("/stop", handler.NewServiceHandler(db).Stop)
                service.POST("/restart", handler.NewServiceHandler(db).Restart)
            }
            // 防火墙管理
            firewall := system.Group("/firewall")
            {
                firewall.GET("/status", handler.NewFirewallHandler(db).GetStatus)
                firewall.POST("/enable", handler.NewFirewallHandler(db).Enable)
                firewall.POST("/disable", handler.NewFirewallHandler(db).Disable)
                firewall.GET("/rules", handler.NewFirewallHandler(db).GetRules)
                firewall.POST("/rule", handler.NewFirewallHandler(db).AddRule)
                firewall.DELETE("/rule", handler.NewFirewallHandler(db).DeleteRule)
            }
        }

        // 网站管理
        website := api.Group("/website", middleware.Auth())
        {
            website.GET("", handler.NewWebsiteHandler(db).List)
            website.POST("", handler.NewWebsiteHandler(db).Create)
            website.GET("/:id", handler.NewWebsiteHandler(db).Get)
            website.PUT("/:id", handler.NewWebsiteHandler(db).Update)
            website.DELETE("/:id", handler.NewWebsiteHandler(db).Delete)
            website.POST("/:id/status", handler.NewWebsiteHandler(db).UpdateStatus)
            website.GET("/rewrites", handler.NewWebsiteHandler(db).GetRewrites)
        }

        // 数据库管理
        database := api.Group("/database", middleware.Auth())
        {
            database.GET("", handler.NewDatabaseHandler(db).List)
            database.POST("", handler.NewDatabaseHandler(db).Create)
            database.DELETE("", handler.NewDatabaseHandler(db).Delete)
        }

        // 文件管理
        file := api.Group("/file", middleware.Auth())
        {
            file.GET("/list", handler.NewFileHandler(db).List)
            file.POST("/upload", handler.NewFileHandler(db).Upload)
            file.GET("/download", handler.NewFileHandler(db).Download)
            file.POST("/mkdir", handler.NewFileHandler(db).MakeDir)
            file.DELETE("", handler.NewFileHandler(db).Delete)
            file.PUT("/chmod", handler.NewFileHandler(db).Chmod)
            file.PUT("/rename", handler.NewFileHandler(db).Rename)
        }

        // 定时任务
        cron := api.Group("/cron", middleware.Auth())
        {
            cron.GET("", handler.NewCronHandler(db).List)
            cron.POST("", handler.NewCronHandler(db).Create)
            cron.GET("/:id", handler.NewCronHandler(db).Get)
            cron.PUT("/:id", handler.NewCronHandler(db).Update)
            cron.DELETE("/:id", handler.NewCronHandler(db).Delete)
            cron.POST("/:id/status", handler.NewCronHandler(db).UpdateStatus)
        }

        // 容器管理
        container := api.Group("/container", middleware.Auth())
        {
            container.GET("", handler.NewContainerHandler(db).List)
            container.POST("", handler.NewContainerHandler(db).Create)
            container.POST("/start", handler.NewContainerHandler(db).Start)
            container.POST("/stop", handler.NewContainerHandler(db).Stop)
            container.POST("/restart", handler.NewContainerHandler(db).Restart)
            container.DELETE("", handler.NewContainerHandler(db).Remove)
            container.GET("/:id/logs", handler.NewContainerHandler(db).Logs)
            container.GET("/:id/stats", handler.NewContainerHandler(db).Stats)

            // 镜像管理
            image := container.Group("/image")
            {
                image.GET("", handler.NewContainerHandler(db).ListImages)
                image.POST("/pull", handler.NewContainerHandler(db).PullImage)
                image.DELETE("", handler.NewContainerHandler(db).RemoveImage)
            }

            // 网络管理
            network := container.Group("/network")
            {
                network.GET("", handler.NewContainerHandler(db).ListNetworks)
                network.POST("", handler.NewContainerHandler(db).CreateNetwork)
                network.DELETE("", handler.NewContainerHandler(db).RemoveNetwork)
            }

            // 数据卷管理
            volume := container.Group("/volume")
            {
                volume.GET("", handler.NewContainerHandler(db).ListVolumes)
                volume.POST("", handler.NewContainerHandler(db).CreateVolume)
                volume.DELETE("", handler.NewContainerHandler(db).RemoveVolume)
            }
        }
    }

    // WebSocket路由
    ws := h.Group("/ws", middleware.Auth())
    {
        ws.GET("/terminal", handler.NewWebSocketHandler(db).Terminal)
        ws.GET("/stats", handler.NewWebSocketHandler(db).Stats)
    }
}
```

### 2. 系统服务管理模块实现

```go
// pkg/systemctl/service.go
package systemctl

import (
    "time"
    "strings"

    "yourproject/pkg/shell"
)

// Status 获取服务状态
func Status(name string) (bool, error) {
    output, _ := shell.Execf("systemctl is-active '%s'", name)
    return output == "active", nil
}

// IsEnabled 服务是否启用
func IsEnabled(name string) (bool, error) {
    out, _ := shell.Execf("systemctl is-enabled '%s'", name)
    return out == "enabled" || out == "static" || out == "indirect", nil
}

// Start 启动服务
func Start(name string) error {
    _, err := shell.ExecfWithTimeout(2*time.Minute, "systemctl start '%s'", name)
    return err
}

// Stop 停止服务
func Stop(name string) error {
    _, err := shell.ExecfWithTimeout(2*time.Minute, "systemctl stop '%s'", name)
    return err
}

// Restart 重启服务
func Restart(name string) error {
    _, err := shell.ExecfWithTimeout(2*time.Minute, "systemctl restart '%s'", name)
    return err
}

// ListServices 获取服务列表
func ListServices(filter string) ([]Service, error) {
    cmd := "systemctl list-units --type=service --all --no-legend"
    if filter != "" {
        cmd += " | grep " + filter
    }

    output, err := shell.Execf(cmd)
    if err != nil {
        return nil, err
    }

    var services []Service
    lines := strings.Split(output, "\n")
    for _, line := range lines {
        if line == "" {
            continue
        }

        fields := strings.Fields(line)
        if len(fields) < 4 {
            continue
        }

        name := strings.TrimSuffix(fields[0], ".service")
        services = append(services, Service{
            Name:        name,
            LoadState:   fields[1],
            ActiveState: fields[2],
            SubState:    fields[3],
        })
    }

    return services, nil
}

type Service struct {
    Name        string `json:"name"`
    LoadState   string `json:"load_state"`
    ActiveState string `json:"active_state"`
    SubState    string `json:"sub_state"`
}
```

### 3. 认证中间件实现

```go
// infra/middleware/auth.go
package middleware

import (
    "context"

    "github.com/cloudwego/hertz/pkg/app"
    "github.com/cloudwego/hertz/pkg/protocol/consts"
    "github.com/golang-jwt/jwt/v4"

    "yourproject/biz/model"
    "yourproject/config"
)

func Auth() app.HandlerFunc {
    return func(ctx context.Context, c *app.RequestContext) {
        token := c.Request.Header.Get("Authorization")
        if token == "" {
            c.JSON(consts.StatusUnauthorized, model.Response{
                Code:    consts.StatusUnauthorized,
                Message: "Unauthorized",
            })
            c.Abort()
            return
        }

        // 去掉Bearer前缀
        if len(token) > 7 && token[:7] == "Bearer " {
            token = token[7:]
        }

        // 验证JWT
        claims := &model.Claims{}
        tkn, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
            return []byte(config.Get().JWT.Secret), nil
        })

        if err != nil || !tkn.Valid {
            c.JSON(consts.StatusUnauthorized, model.Response{
                Code:    consts.StatusUnauthorized,
                Message: "Invalid or expired token",
            })
            c.Abort()
            return
        }

        // 将用户信息存入上下文
        c.Set("user_id", claims.UserID)
        c.Set("username", claims.Username)

        c.Next(ctx)
    }
}
```

## 二、前端实现

### 1. API请求封装

```typescript
// src/api/request.ts
import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { MessagePlugin } from 'tdesign-vue-next';
import { useUserStore } from '@/store/modules/user';

// 创建axios实例
const request = axios.create({
  baseURL: '/api',
  timeout: 30000,
});

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const userStore = useUserStore();
    const token = userStore.token;

    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response;

    // 根据后端API约定处理响应
    if (data.code === 0) {
      return data.data;
    }

    MessagePlugin.error(data.message || '请求失败');
    return Promise.reject(new Error(data.message || '请求失败'));
  },
  (error: AxiosError) => {
    if (error.response?.status === 401) {
      const userStore = useUserStore();
      userStore.logout();
      window.location.href = '/login';
      return Promise.reject(error);
    }

    MessagePlugin.error(error.message || '网络错误');
    return Promise.reject(error);
  }
);

export default request;
```

### 2. 用户状态管理

```typescript
// src/store/modules/user.ts
import { defineStore } from 'pinia';
import { login, logout, getUserInfo } from '@/api/modules/auth';

interface UserState {
  token: string;
  userInfo: {
    id: number;
    name: string;
    email: string;
    avatar: string;
    roles: string[];
  };
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: localStorage.getItem('token') || '',
    userInfo: {
      id: 0,
      name: '',
      email: '',
      avatar: '',
      roles: [],
    },
  }),

  actions: {
    async login(username: string, password: string) {
      try {
        const data = await login(username, password);
        this.token = data.token;
        localStorage.setItem('token', data.token);
        return data;
      } catch (error) {
        return Promise.reject(error);
      }
    },

    async getUserInfo() {
      try {
        const data = await getUserInfo();
        this.userInfo = data;
        return data;
      } catch (error) {
        return Promise.reject(error);
      }
    },

    async logout() {
      try {
        await logout();
      } finally {
        this.token = '';
        this.userInfo = {
          id: 0,
          name: '',
          email: '',
          avatar: '',
          roles: [],
        };
        localStorage.removeItem('token');
      }
    },
  },
});
```

### 3. 容器管理模块实现

#### 3.1 容器操作封装

```go
// pkg/container/docker/container.go
package docker

import (
    "context"
    "encoding/json"
    "fmt"
    "strings"
    "time"

    "yourproject/pkg/shell"
)

// Container 容器信息结构体
type Container struct {
    ID         string            `json:"id"`
    Name       string            `json:"name"`
    Image      string            `json:"image"`
    Command    string            `json:"command"`
    Created    time.Time         `json:"created"`
    Status     string            `json:"status"`
    Ports      []Port            `json:"ports"`
    State      string            `json:"state"`
    Size       string            `json:"size"`
    Networks   []string          `json:"networks"`
    Mounts     []Mount           `json:"mounts"`
    Labels     map[string]string `json:"labels"`
    IsRunning  bool              `json:"is_running"`
}

// Port 端口映射结构体
type Port struct {
    IP          string `json:"ip"`
    PrivatePort uint16 `json:"private_port"`
    PublicPort  uint16 `json:"public_port"`
    Type        string `json:"type"`
}

// Mount 挂载点结构体
type Mount struct {
    Type        string `json:"type"`
    Source      string `json:"source"`
    Destination string `json:"destination"`
    Mode        string `json:"mode"`
    RW          bool   `json:"rw"`
}

// ListContainers 获取容器列表
func ListContainers(all bool) ([]Container, error) {
    cmd := "docker ps --format '{{json .}}'"
    if all {
        cmd = "docker ps -a --format '{{json .}}'"
    }

    output, err := shell.Execf(cmd)
    if err != nil {
        return nil, err
    }

    var containers []Container
    lines := strings.Split(output, "\n")
    for _, line := range lines {
        if line == "" {
            continue
        }

        var container Container
        if err := json.Unmarshal([]byte(line), &container); err != nil {
            continue
        }

        // 获取容器详细信息
        details, err := InspectContainer(container.ID)
        if err == nil {
            container.Networks = details.Networks
            container.Mounts = details.Mounts
            container.Labels = details.Labels
            container.IsRunning = details.State.Running
        }

        containers = append(containers, container)
    }

    return containers, nil
}

// StartContainer 启动容器
func StartContainer(id string) error {
    _, err := shell.Execf("docker start %s", id)
    return err
}

// StopContainer 停止容器
func StopContainer(id string, timeout int) error {
    if timeout > 0 {
        _, err := shell.Execf("docker stop --time %d %s", timeout, id)
        return err
    }

    _, err := shell.Execf("docker stop %s", id)
    return err
}

// RestartContainer 重启容器
func RestartContainer(id string, timeout int) error {
    if timeout > 0 {
        _, err := shell.Execf("docker restart --time %d %s", timeout, id)
        return err
    }

    _, err := shell.Execf("docker restart %s", id)
    return err
}

// RemoveContainer 删除容器
func RemoveContainer(id string, force bool, volumes bool) error {
    cmd := "docker rm"
    if force {
        cmd += " -f"
    }
    if volumes {
        cmd += " -v"
    }

    _, err := shell.Execf("%s %s", cmd, id)
    return err
}

// CreateContainer 创建容器
func CreateContainer(options CreateContainerOptions) (string, error) {
    cmd := "docker run -d"

    // 添加名称
    if options.Name != "" {
        cmd += fmt.Sprintf(" --name %s", options.Name)
    }

    // 添加端口映射
    for _, port := range options.Ports {
        if port.IP != "" {
            cmd += fmt.Sprintf(" -p %s:%d:%d/%s", port.IP, port.PublicPort, port.PrivatePort, port.Type)
        } else {
            cmd += fmt.Sprintf(" -p %d:%d/%s", port.PublicPort, port.PrivatePort, port.Type)
        }
    }

    // 添加环境变量
    for key, value := range options.Env {
        cmd += fmt.Sprintf(" -e %s=%s", key, value)
    }

    // 添加数据卷
    for _, mount := range options.Mounts {
        if mount.Type == "volume" {
            cmd += fmt.Sprintf(" -v %s:%s", mount.Source, mount.Destination)
            if !mount.RW {
                cmd += ":ro"
            }
        } else if mount.Type == "bind" {
            cmd += fmt.Sprintf(" --mount type=bind,source=%s,target=%s", mount.Source, mount.Destination)
            if !mount.RW {
                cmd += ",readonly=true"
            }
        }
    }

    // 添加网络
    if options.Network != "" {
        cmd += fmt.Sprintf(" --network %s", options.Network)
    }

    // 添加重启策略
    if options.RestartPolicy != "" {
        cmd += fmt.Sprintf(" --restart %s", options.RestartPolicy)
    }

    // 添加镜像和命令
    cmd += fmt.Sprintf(" %s", options.Image)
    if options.Command != "" {
        cmd += fmt.Sprintf(" %s", options.Command)
    }

    output, err := shell.Execf(cmd)
    if err != nil {
        return "", err
    }

    return strings.TrimSpace(output), nil
}

// CreateContainerOptions 创建容器选项
type CreateContainerOptions struct {
    Name          string            `json:"name"`
    Image         string            `json:"image"`
    Command       string            `json:"command"`
    Ports         []Port            `json:"ports"`
    Env           map[string]string `json:"env"`
    Mounts        []Mount           `json:"mounts"`
    Network       string            `json:"network"`
    RestartPolicy string            `json:"restart_policy"`
}
```

#### 3.2 镜像管理实现

```go
// pkg/container/docker/image.go
package docker

import (
    "encoding/json"
    "strings"
    "time"

    "yourproject/pkg/shell"
)

// Image 镜像信息结构体
type Image struct {
    ID          string    `json:"id"`
    Repository  string    `json:"repository"`
    Tag         string    `json:"tag"`
    Digest      string    `json:"digest"`
    CreatedAt   time.Time `json:"created_at"`
    Size        int64     `json:"size"`
    SharedSize  int64     `json:"shared_size"`
    VirtualSize int64     `json:"virtual_size"`
    Labels      map[string]string `json:"labels"`
}

// ListImages 获取镜像列表
func ListImages() ([]Image, error) {
    cmd := "docker images --format '{{json .}}'"

    output, err := shell.Execf(cmd)
    if err != nil {
        return nil, err
    }

    var images []Image
    lines := strings.Split(output, "\n")
    for _, line := range lines {
        if line == "" {
            continue
        }

        var image Image
        if err := json.Unmarshal([]byte(line), &image); err != nil {
            continue
        }

        images = append(images, image)
    }

    return images, nil
}

// PullImage 拉取镜像
func PullImage(name string, tag string) error {
    if tag == "" {
        tag = "latest"
    }

    _, err := shell.ExecfWithTimeout(10*time.Minute, "docker pull %s:%s", name, tag)
    return err
}

// RemoveImage 删除镜像
func RemoveImage(id string, force bool) error {
    cmd := "docker rmi"
    if force {
        cmd += " -f"
    }

    _, err := shell.Execf("%s %s", cmd, id)
    return err
}

// BuildImage 构建镜像
func BuildImage(path string, tag string, dockerfile string) error {
    cmd := "docker build"

    if tag != "" {
        cmd += fmt.Sprintf(" -t %s", tag)
    }

    if dockerfile != "" && dockerfile != "Dockerfile" {
        cmd += fmt.Sprintf(" -f %s", dockerfile)
    }

    cmd += fmt.Sprintf(" %s", path)

    _, err := shell.ExecfWithTimeout(30*time.Minute, cmd)
    return err
}
```

#### 3.3 容器管理API实现

```go
// biz/handler/container.go
package handler

import (
    "context"
    "strconv"

    "github.com/cloudwego/hertz/pkg/app"
    "github.com/cloudwego/hertz/pkg/protocol/consts"
    "gorm.io/gorm"

    "yourproject/biz/model"
    "yourproject/pkg/container/docker"
)

type ContainerHandler struct {
    db *gorm.DB
}

func NewContainerHandler(db *gorm.DB) *ContainerHandler {
    return &ContainerHandler{db: db}
}

// List 获取容器列表
func (h *ContainerHandler) List(ctx context.Context, c *app.RequestContext) {
    all := c.Query("all") == "true"

    containers, err := docker.ListContainers(all)
    if err != nil {
        c.JSON(consts.StatusInternalServerError, model.Response{
            Code:    consts.StatusInternalServerError,
            Message: "Failed to get container list: " + err.Error(),
        })
        return
    }

    c.JSON(consts.StatusOK, model.Response{
        Code: consts.StatusOK,
        Data: containers,
    })
}

// Start 启动容器
func (h *ContainerHandler) Start(ctx context.Context, c *app.RequestContext) {
    var req struct {
        ID string `json:"id" vd:"len($) > 0"`
    }

    if err := c.BindAndValidate(&req); err != nil {
        c.JSON(consts.StatusBadRequest, model.Response{
            Code:    consts.StatusBadRequest,
            Message: err.Error(),
        })
        return
    }

    if err := docker.StartContainer(req.ID); err != nil {
        c.JSON(consts.StatusInternalServerError, model.Response{
            Code:    consts.StatusInternalServerError,
            Message: "Failed to start container: " + err.Error(),
        })
        return
    }

    c.JSON(consts.StatusOK, model.Response{
        Code:    consts.StatusOK,
        Message: "Container started successfully",
    })
}

// Stop 停止容器
func (h *ContainerHandler) Stop(ctx context.Context, c *app.RequestContext) {
    var req struct {
        ID      string `json:"id" vd:"len($) > 0"`
        Timeout int    `json:"timeout"`
    }

    if err := c.BindAndValidate(&req); err != nil {
        c.JSON(consts.StatusBadRequest, model.Response{
            Code:    consts.StatusBadRequest,
            Message: err.Error(),
        })
        return
    }

    if err := docker.StopContainer(req.ID, req.Timeout); err != nil {
        c.JSON(consts.StatusInternalServerError, model.Response{
            Code:    consts.StatusInternalServerError,
            Message: "Failed to stop container: " + err.Error(),
        })
        return
    }

    c.JSON(consts.StatusOK, model.Response{
        Code:    consts.StatusOK,
        Message: "Container stopped successfully",
    })
}

// Create 创建容器
func (h *ContainerHandler) Create(ctx context.Context, c *app.RequestContext) {
    var req docker.CreateContainerOptions

    if err := c.BindAndValidate(&req); err != nil {
        c.JSON(consts.StatusBadRequest, model.Response{
            Code:    consts.StatusBadRequest,
            Message: err.Error(),
        })
        return
    }

    id, err := docker.CreateContainer(req)
    if err != nil {
        c.JSON(consts.StatusInternalServerError, model.Response{
            Code:    consts.StatusInternalServerError,
            Message: "Failed to create container: " + err.Error(),
        })
        return
    }

    c.JSON(consts.StatusOK, model.Response{
        Code: consts.StatusOK,
        Data: map[string]string{
            "id": id,
        },
    })
}
```

#### 3.4 前端容器管理API封装

```typescript
// src/api/modules/container.ts
import request from '@/api/request';

export interface Container {
  id: string;
  name: string;
  image: string;
  command: string;
  created: string;
  status: string;
  ports: Port[];
  state: string;
  size: string;
  networks: string[];
  mounts: Mount[];
  labels: Record<string, string>;
  is_running: boolean;
}

export interface Port {
  ip: string;
  private_port: number;
  public_port: number;
  type: string;
}

export interface Mount {
  type: string;
  source: string;
  destination: string;
  mode: string;
  rw: boolean;
}

export interface CreateContainerOptions {
  name: string;
  image: string;
  command?: string;
  ports?: Port[];
  env?: Record<string, string>;
  mounts?: Mount[];
  network?: string;
  restart_policy?: string;
}

export default {
  // 获取容器列表
  list: (all: boolean = false) => request.get('/container', { params: { all } }),

  // 启动容器
  start: (id: string) => request.post('/container/start', { id }),

  // 停止容器
  stop: (id: string, timeout: number = 10) => request.post('/container/stop', { id, timeout }),

  // 重启容器
  restart: (id: string, timeout: number = 10) => request.post('/container/restart', { id, timeout }),

  // 删除容器
  remove: (id: string, force: boolean = false, volumes: boolean = false) =>
    request.delete('/container', { data: { id, force, volumes } }),

  // 创建容器
  create: (options: CreateContainerOptions) => request.post('/container', options),

  // 获取容器日志
  logs: (id: string, tail: number = 100) => request.get(`/container/${id}/logs`, { params: { tail } }),

  // 获取容器统计信息
  stats: (id: string) => request.get(`/container/${id}/stats`),

  // 获取镜像列表
  listImages: () => request.get('/container/image'),

  // 拉取镜像
  pullImage: (name: string, tag: string = 'latest') => request.post('/container/image/pull', { name, tag }),

  // 删除镜像
  removeImage: (id: string, force: boolean = false) => request.delete('/container/image', { data: { id, force } }),

  // 获取网络列表
  listNetworks: () => request.get('/container/network'),

  // 创建网络
  createNetwork: (name: string, driver: string = 'bridge') =>
    request.post('/container/network', { name, driver }),

  // 删除网络
  removeNetwork: (id: string) => request.delete('/container/network', { data: { id } }),

  // 获取数据卷列表
  listVolumes: () => request.get('/container/volume'),

  // 创建数据卷
  createVolume: (name: string) => request.post('/container/volume', { name }),

  // 删除数据卷
  removeVolume: (name: string, force: boolean = false) =>
    request.delete('/container/volume', { data: { name, force } }),
};
```
