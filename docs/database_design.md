# 服务器运维面板数据库设计

## 一、数据库选择

本项目选择使用SQLite作为数据库，主要考虑因素：

1. **轻量级**：SQLite是一个嵌入式数据库，不需要单独的服务器进程，减少了部署复杂度
2. **零配置**：不需要安装和配置，简化了用户的使用体验
3. **可靠性**：SQLite经过广泛测试，稳定可靠
4. **性能**：对于本项目的数据规模和并发量，SQLite性能完全满足需求
5. **单文件存储**：便于备份和迁移

## 二、数据表设计

### 1. 用户表 (users)

存储系统用户信息。

```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    name VARCHAR(100),
    avatar VARCHAR(255),
    is_admin BOOLEAN DEFAULT 0,
    two_factor_secret VARCHAR(100),
    two_factor_enabled BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 用户令牌表 (user_tokens)

存储用户的API令牌，用于API访问认证。

```sql
CREATE TABLE user_tokens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    last_used_at TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 3. 网站表 (websites)

存储网站配置信息。

```sql
CREATE TABLE websites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    type VARCHAR(20) NOT NULL,
    path VARCHAR(255) NOT NULL,
    domains TEXT NOT NULL,
    https BOOLEAN DEFAULT 0,
    status BOOLEAN DEFAULT 1,
    php VARCHAR(10),
    remark TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. 数据库服务器表 (database_servers)

存储数据库服务器信息。

```sql
CREATE TABLE database_servers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    remark TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5. 数据库表 (databases)

存储数据库信息。

```sql
CREATE TABLE databases (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    charset VARCHAR(20) DEFAULT 'utf8mb4',
    collation VARCHAR(20) DEFAULT 'utf8mb4_general_ci',
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (server_id) REFERENCES database_servers(id) ON DELETE CASCADE,
    UNIQUE(server_id, name)
);
```

### 6. 证书表 (certificates)

存储SSL证书信息。

```sql
CREATE TABLE certificates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    domains TEXT NOT NULL,
    cert_file VARCHAR(255) NOT NULL,
    key_file VARCHAR(255) NOT NULL,
    issue_time TIMESTAMP,
    expire_time TIMESTAMP,
    auto_renew BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7. 定时任务表 (cron_jobs)

存储定时任务信息。

```sql
CREATE TABLE cron_jobs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    command TEXT NOT NULL,
    schedule VARCHAR(100) NOT NULL,
    status BOOLEAN DEFAULT 1,
    log_file VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 8. 备份表 (backups)

存储备份信息。

```sql
CREATE TABLE backups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL,
    target_id INTEGER NOT NULL,
    path VARCHAR(255) NOT NULL,
    size BIGINT,
    status VARCHAR(20) DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 9. 操作日志表 (operation_logs)

记录用户操作日志。

```sql
CREATE TABLE operation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50),
    target_id INTEGER,
    ip_address VARCHAR(50),
    user_agent TEXT,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
```

### 10. 系统设置表 (settings)

存储系统设置。

```sql
CREATE TABLE settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key VARCHAR(100) NOT NULL UNIQUE,
    value TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 11. 容器模板表 (container_templates)

存储容器模板信息，用于快速创建常用容器。

```sql
CREATE TABLE container_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image VARCHAR(255) NOT NULL,
    command TEXT,
    ports TEXT,
    environment TEXT,
    volumes TEXT,
    network VARCHAR(100),
    restart_policy VARCHAR(50) DEFAULT 'no',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 12. 容器组表 (container_groups)

存储容器组信息，用于管理Docker Compose项目。

```sql
CREATE TABLE container_groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    compose_file TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'stopped',
    path VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 三、数据模型设计

### 1. 用户模型 (User)

```go
// biz/model/user.go
package model

import (
    "time"
)

type User struct {
    ID               uint      `gorm:"primaryKey" json:"id"`
    Username         string    `gorm:"size:50;uniqueIndex;not null" json:"username"`
    Password         string    `gorm:"size:255;not null" json:"-"`
    Email            string    `gorm:"size:100" json:"email"`
    Name             string    `gorm:"size:100" json:"name"`
    Avatar           string    `gorm:"size:255" json:"avatar"`
    IsAdmin          bool      `gorm:"default:false" json:"is_admin"`
    TwoFactorSecret  string    `gorm:"size:100" json:"-"`
    TwoFactorEnabled bool      `gorm:"default:false" json:"two_factor_enabled"`
    CreatedAt        time.Time `json:"created_at"`
    UpdatedAt        time.Time `json:"updated_at"`
}
```

### 2. 网站模型 (Website)

```go
// biz/model/website.go
package model

import (
    "time"
)

type Website struct {
    ID        uint      `gorm:"primaryKey" json:"id"`
    Name      string    `gorm:"size:100;uniqueIndex;not null" json:"name"`
    Type      string    `gorm:"size:20;not null" json:"type"`
    Path      string    `gorm:"size:255;not null" json:"path"`
    Domains   string    `gorm:"type:text;not null" json:"domains"`
    Https     bool      `gorm:"default:false" json:"https"`
    Status    bool      `gorm:"default:true" json:"status"`
    PHP       string    `gorm:"size:10" json:"php"`
    Remark    string    `gorm:"type:text" json:"remark"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}

// GetDomainList 获取域名列表
func (w *Website) GetDomainList() []string {
    if w.Domains == "" {
        return []string{}
    }
    return strings.Split(w.Domains, ",")
}
```

### 3. 数据库服务器模型 (DatabaseServer)

```go
// biz/model/database_server.go
package model

import (
    "time"
)

type DatabaseServer struct {
    ID        uint      `gorm:"primaryKey" json:"id"`
    Name      string    `gorm:"size:100;not null" json:"name"`
    Type      string    `gorm:"size:20;not null" json:"type"`
    Host      string    `gorm:"size:255;not null" json:"host"`
    Port      int       `gorm:"not null" json:"port"`
    Username  string    `gorm:"size:100;not null" json:"username"`
    Password  string    `gorm:"size:255;not null" json:"-"`
    Remark    string    `gorm:"type:text" json:"remark"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

### 4. 容器模板模型 (ContainerTemplate)

```go
// biz/model/container_template.go
package model

import (
    "encoding/json"
    "time"
)

type ContainerTemplate struct {
    ID            uint      `gorm:"primaryKey" json:"id"`
    Name          string    `gorm:"size:100;not null" json:"name"`
    Description   string    `gorm:"type:text" json:"description"`
    Image         string    `gorm:"size:255;not null" json:"image"`
    Command       string    `gorm:"type:text" json:"command"`
    Ports         string    `gorm:"type:text" json:"ports_json"`
    Environment   string    `gorm:"type:text" json:"environment_json"`
    Volumes       string    `gorm:"type:text" json:"volumes_json"`
    Network       string    `gorm:"size:100" json:"network"`
    RestartPolicy string    `gorm:"size:50;default:'no'" json:"restart_policy"`
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
}

// GetPorts 获取端口映射
func (t *ContainerTemplate) GetPorts() []Port {
    if t.Ports == "" {
        return []Port{}
    }

    var ports []Port
    _ = json.Unmarshal([]byte(t.Ports), &ports)
    return ports
}

// SetPorts 设置端口映射
func (t *ContainerTemplate) SetPorts(ports []Port) {
    data, _ := json.Marshal(ports)
    t.Ports = string(data)
}

// GetEnvironment 获取环境变量
func (t *ContainerTemplate) GetEnvironment() map[string]string {
    if t.Environment == "" {
        return map[string]string{}
    }

    var env map[string]string
    _ = json.Unmarshal([]byte(t.Environment), &env)
    return env
}

// SetEnvironment 设置环境变量
func (t *ContainerTemplate) SetEnvironment(env map[string]string) {
    data, _ := json.Marshal(env)
    t.Environment = string(data)
}

// GetVolumes 获取数据卷
func (t *ContainerTemplate) GetVolumes() []Mount {
    if t.Volumes == "" {
        return []Mount{}
    }

    var volumes []Mount
    _ = json.Unmarshal([]byte(t.Volumes), &volumes)
    return volumes
}

// SetVolumes 设置数据卷
func (t *ContainerTemplate) SetVolumes(volumes []Mount) {
    data, _ := json.Marshal(volumes)
    t.Volumes = string(data)
}

// Port 端口映射结构体
type Port struct {
    IP          string `json:"ip"`
    PrivatePort uint16 `json:"private_port"`
    PublicPort  uint16 `json:"public_port"`
    Type        string `json:"type"`
}

// Mount 挂载点结构体
type Mount struct {
    Type        string `json:"type"`
    Source      string `json:"source"`
    Destination string `json:"destination"`
    Mode        string `json:"mode"`
    RW          bool   `json:"rw"`
}
```

### 5. 容器组模型 (ContainerGroup)

```go
// biz/model/container_group.go
package model

import (
    "time"
)

type ContainerGroup struct {
    ID          uint      `gorm:"primaryKey" json:"id"`
    Name        string    `gorm:"size:100;uniqueIndex;not null" json:"name"`
    Description string    `gorm:"type:text" json:"description"`
    ComposeFile string    `gorm:"type:text;not null" json:"compose_file"`
    Status      string    `gorm:"size:20;default:'stopped'" json:"status"`
    Path        string    `gorm:"size:255;not null" json:"path"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

## 四、数据库初始化

```go
// infra/database/init.go
package database

import (
    "fmt"
    "path/filepath"

    "gorm.io/driver/sqlite"
    "gorm.io/gorm"
    "gorm.io/gorm/logger"

    "yourproject/biz/model"
    "yourproject/config"
)

func Init(cfg *config.Config) (*gorm.DB, error) {
    dbPath := filepath.Join(cfg.Data.Path, "panel.db")

    db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
        Logger: logger.Default.LogMode(logger.Info),
    })
    if err != nil {
        return nil, fmt.Errorf("failed to connect to database: %w", err)
    }

    // 自动迁移表结构
    if err := db.AutoMigrate(
        &model.User{},
        &model.UserToken{},
        &model.Website{},
        &model.DatabaseServer{},
        &model.Database{},
        &model.Certificate{},
        &model.CronJob{},
        &model.Backup{},
        &model.OperationLog{},
        &model.Setting{},
        &model.ContainerTemplate{},
        &model.ContainerGroup{},
    ); err != nil {
        return nil, fmt.Errorf("failed to migrate database: %w", err)
    }

    // 初始化默认数据
    if err := initDefaultData(db); err != nil {
        return nil, fmt.Errorf("failed to initialize default data: %w", err)
    }

    return db, nil
}

func initDefaultData(db *gorm.DB) error {
    // 检查是否已有管理员用户
    var count int64
    db.Model(&model.User{}).Count(&count)

    // 如果没有用户，创建默认管理员
    if count == 0 {
        admin := model.User{
            Username: "admin",
            Password: "bcrypt_hashed_password_here", // 实际应用中需要哈希处理
            Name:     "Administrator",
            IsAdmin:  true,
        }

        if err := db.Create(&admin).Error; err != nil {
            return err
        }
    }

    return nil
}
```
