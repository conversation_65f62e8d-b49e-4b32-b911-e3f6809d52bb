# 服务器运维面板API设计

## 一、API规范

### 1. 基本规范

- 基础路径：`/api`
- 请求方法：
  - `GET`：获取资源
  - `POST`：创建资源
  - `PUT`：更新资源
  - `DELETE`：删除资源
- 响应格式：统一使用JSON格式
- 状态码：
  - `200 OK`：请求成功
  - `400 Bad Request`：请求参数错误
  - `401 Unauthorized`：未授权
  - `403 Forbidden`：权限不足
  - `404 Not Found`：资源不存在
  - `500 Internal Server Error`：服务器内部错误

### 2. 响应格式

```json
{
  "code": 0,           // 状态码，0表示成功，非0表示错误
  "message": "success", // 状态消息
  "data": {}           // 响应数据
}
```

### 3. 认证方式

使用JWT（JSON Web Token）进行认证，在请求头中添加`Authorization`字段：

```
Authorization: Bearer {token}
```

## 二、API接口设计

### 1. 认证相关接口

#### 1.1 用户登录

- **URL**: `/api/auth/login`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "username": "admin",
    "password": "password",
    "remember_me": true
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_at": "2023-12-31T23:59:59Z"
    }
  }
  ```

#### 1.2 用户登出

- **URL**: `/api/auth/logout`
- **方法**: `POST`
- **请求头**: 需要认证
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": null
  }
  ```

#### 1.3 获取用户信息

- **URL**: `/api/auth/profile`
- **方法**: `GET`
- **请求头**: 需要认证
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "name": "Administrator",
      "avatar": "https://example.com/avatar.png",
      "is_admin": true,
      "two_factor_enabled": false,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

### 2. 系统管理接口

#### 2.1 获取系统信息

- **URL**: `/api/system/info`
- **方法**: `GET`
- **请求头**: 需要认证
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "hostname": "server1",
      "os": "Ubuntu 22.04.1 LTS",
      "kernel": "5.15.0-56-generic",
      "cpu": {
        "model": "Intel(R) Xeon(R) CPU E5-2680 v4 @ 2.40GHz",
        "cores": 4,
        "threads": 8
      },
      "memory": {
        "total": 8589934592,
        "used": 4294967296
      },
      "disk": {
        "total": 107374182400,
        "used": 53687091200
      },
      "uptime": 1234567
    }
  }
  ```

#### 2.2 获取系统状态

- **URL**: `/api/system/status`
- **方法**: `GET`
- **请求头**: 需要认证
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "cpu_usage": 25.5,
      "memory_usage": 50.0,
      "disk_usage": 50.0,
      "load_average": [1.5, 1.2, 1.0],
      "network": [
        {
          "interface": "eth0",
          "rx_bytes": 1024000,
          "tx_bytes": 512000,
          "rx_packets": 1000,
          "tx_packets": 500
        }
      ]
    }
  }
  ```

#### 2.3 获取服务列表

- **URL**: `/api/system/service/list`
- **方法**: `GET`
- **请求头**: 需要认证
- **请求参数**:
  - `name`: 服务名称过滤（可选）
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": [
      {
        "name": "nginx",
        "load_state": "loaded",
        "active_state": "active",
        "sub_state": "running"
      },
      {
        "name": "mysql",
        "load_state": "loaded",
        "active_state": "active",
        "sub_state": "running"
      }
    ]
  }
  ```

#### 2.4 启动服务

- **URL**: `/api/system/service/start`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "name": "nginx"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": null
  }
  ```

#### 2.5 停止服务

- **URL**: `/api/system/service/stop`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "name": "nginx"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": null
  }
  ```

### 3. 网站管理接口

#### 3.1 获取网站列表

- **URL**: `/api/website`
- **方法**: `GET`
- **请求头**: 需要认证
- **请求参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认20）
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "total": 2,
      "items": [
        {
          "id": 1,
          "name": "example.com",
          "type": "static",
          "path": "/var/www/example.com",
          "domains": "example.com,www.example.com",
          "https": true,
          "status": true,
          "php": "8.1",
          "remark": "Example website",
          "created_at": "2023-01-01T00:00:00Z",
          "updated_at": "2023-01-01T00:00:00Z"
        },
        {
          "id": 2,
          "name": "blog.example.com",
          "type": "php",
          "path": "/var/www/blog",
          "domains": "blog.example.com",
          "https": false,
          "status": true,
          "php": "8.1",
          "remark": "Blog website",
          "created_at": "2023-01-01T00:00:00Z",
          "updated_at": "2023-01-01T00:00:00Z"
        }
      ]
    }
  }
  ```

#### 3.2 创建网站

- **URL**: `/api/website`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "name": "example.com",
    "type": "static",
    "path": "/var/www/example.com",
    "domains": "example.com,www.example.com",
    "https": false,
    "php": "8.1",
    "remark": "Example website"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1
    }
  }
  ```

#### 3.3 获取网站详情

- **URL**: `/api/website/{id}`
- **方法**: `GET`
- **请求头**: 需要认证
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1,
      "name": "example.com",
      "type": "static",
      "path": "/var/www/example.com",
      "domains": "example.com,www.example.com",
      "https": false,
      "status": true,
      "php": "8.1",
      "remark": "Example website",
      "config": {
        "server_name": "example.com www.example.com",
        "root": "/var/www/example.com",
        "index": "index.html index.htm",
        "error_page": "404 /404.html",
        "access_log": "/var/log/nginx/example.com.access.log",
        "error_log": "/var/log/nginx/example.com.error.log"
      },
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

### 4. 数据库管理接口

#### 4.1 获取数据库列表

- **URL**: `/api/database`
- **方法**: `GET`
- **请求头**: 需要认证
- **请求参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认20）
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "total": 2,
      "items": [
        {
          "id": 1,
          "server_id": 1,
          "name": "wordpress",
          "username": "wp_user",
          "charset": "utf8mb4",
          "collation": "utf8mb4_general_ci",
          "comment": "WordPress database",
          "created_at": "2023-01-01T00:00:00Z",
          "updated_at": "2023-01-01T00:00:00Z",
          "server": {
            "id": 1,
            "name": "Local MySQL",
            "type": "mysql",
            "host": "localhost"
          }
        },
        {
          "id": 2,
          "server_id": 1,
          "name": "nextcloud",
          "username": "nc_user",
          "charset": "utf8mb4",
          "collation": "utf8mb4_general_ci",
          "comment": "Nextcloud database",
          "created_at": "2023-01-01T00:00:00Z",
          "updated_at": "2023-01-01T00:00:00Z",
          "server": {
            "id": 1,
            "name": "Local MySQL",
            "type": "mysql",
            "host": "localhost"
          }
        }
      ]
    }
  }
  ```

#### 4.2 创建数据库

- **URL**: `/api/database`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "server_id": 1,
    "name": "wordpress",
    "username": "wp_user",
    "password": "password123",
    "charset": "utf8mb4",
    "collation": "utf8mb4_general_ci",
    "comment": "WordPress database"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1
    }
  }
  ```

#### 4.3 删除数据库

- **URL**: `/api/database`
- **方法**: `DELETE`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "server_id": 1,
    "name": "wordpress"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": null
  }
  ```

### 5. 文件管理接口

#### 5.1 获取文件列表

- **URL**: `/api/file/list`
- **方法**: `GET`
- **请求头**: 需要认证
- **请求参数**:
  - `path`: 目录路径
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "path": "/var/www/example.com",
      "files": [
        {
          "name": "index.html",
          "type": "file",
          "size": 1024,
          "mode": "0644",
          "owner": "www-data",
          "group": "www-data",
          "modified_at": "2023-01-01T00:00:00Z",
          "is_hidden": false
        },
        {
          "name": "images",
          "type": "directory",
          "size": 4096,
          "mode": "0755",
          "owner": "www-data",
          "group": "www-data",
          "modified_at": "2023-01-01T00:00:00Z",
          "is_hidden": false
        }
      ]
    }
  }
  ```

#### 5.2 上传文件

- **URL**: `/api/file/upload`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**: multipart/form-data
  - `path`: 目标目录路径
  - `file`: 文件数据
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "name": "uploaded.txt",
      "size": 1024,
      "path": "/var/www/example.com/uploaded.txt"
    }
  }
  ```

#### 5.3 下载文件

- **URL**: `/api/file/download`
- **方法**: `GET`
- **请求头**: 需要认证
- **请求参数**:
  - `path`: 文件路径
- **响应**: 文件内容（二进制流）

#### 5.4 创建目录

- **URL**: `/api/file/mkdir`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "path": "/var/www/example.com/newdir"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": null
  }
  ```

#### 5.5 删除文件/目录

- **URL**: `/api/file`
- **方法**: `DELETE`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "path": "/var/www/example.com/file.txt"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": null
  }
  ```

### 6. 定时任务接口

#### 6.1 获取定时任务列表

- **URL**: `/api/cron`
- **方法**: `GET`
- **请求头**: 需要认证
- **请求参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认20）
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "total": 2,
      "items": [
        {
          "id": 1,
          "name": "Backup website",
          "command": "tar -czf /backup/website.tar.gz /var/www/example.com",
          "schedule": "0 0 * * *",
          "status": true,
          "log_file": "/var/log/cron/backup.log",
          "created_at": "2023-01-01T00:00:00Z",
          "updated_at": "2023-01-01T00:00:00Z"
        },
        {
          "id": 2,
          "name": "Clean logs",
          "command": "find /var/log -type f -name \"*.log\" -mtime +7 -delete",
          "schedule": "0 1 * * 0",
          "status": true,
          "log_file": "/var/log/cron/clean.log",
          "created_at": "2023-01-01T00:00:00Z",
          "updated_at": "2023-01-01T00:00:00Z"
        }
      ]
    }
  }
  ```

#### 6.2 创建定时任务

- **URL**: `/api/cron`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "name": "Backup website",
    "command": "tar -czf /backup/website.tar.gz /var/www/example.com",
    "schedule": "0 0 * * *",
    "log_file": "/var/log/cron/backup.log"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1
    }
  }
  ```

### 7. 容器管理接口

#### 7.1 获取容器列表

- **URL**: `/api/container`
- **方法**: `GET`
- **请求头**: 需要认证
- **请求参数**:
  - `all`: 是否显示所有容器，包括已停止的（默认false）
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": [
      {
        "id": "7f2d1d2f58cca",
        "name": "nginx",
        "image": "nginx:latest",
        "command": "nginx -g 'daemon off;'",
        "created": "2023-01-01T00:00:00Z",
        "status": "Up 2 days",
        "ports": [
          {
            "ip": "0.0.0.0",
            "private_port": 80,
            "public_port": 8080,
            "type": "tcp"
          }
        ],
        "state": "running",
        "size": "120MB",
        "networks": ["bridge"],
        "mounts": [
          {
            "type": "volume",
            "source": "nginx-data",
            "destination": "/usr/share/nginx/html",
            "mode": "",
            "rw": true
          }
        ],
        "labels": {
          "maintainer": "NGINX Docker Maintainers"
        },
        "is_running": true
      },
      {
        "id": "9a8b7c6d5e4f",
        "name": "mysql",
        "image": "mysql:8.0",
        "command": "mysqld",
        "created": "2023-01-01T00:00:00Z",
        "status": "Up 2 days",
        "ports": [
          {
            "ip": "0.0.0.0",
            "private_port": 3306,
            "public_port": 3306,
            "type": "tcp"
          }
        ],
        "state": "running",
        "size": "450MB",
        "networks": ["bridge"],
        "mounts": [
          {
            "type": "volume",
            "source": "mysql-data",
            "destination": "/var/lib/mysql",
            "mode": "",
            "rw": true
          }
        ],
        "labels": {
          "com.docker.compose.project": "myproject"
        },
        "is_running": true
      }
    ]
  }
  ```

#### 7.2 创建容器

- **URL**: `/api/container`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "name": "nginx",
    "image": "nginx:latest",
    "command": "",
    "ports": [
      {
        "ip": "0.0.0.0",
        "private_port": 80,
        "public_port": 8080,
        "type": "tcp"
      }
    ],
    "env": {
      "NGINX_HOST": "example.com",
      "NGINX_PORT": "80"
    },
    "mounts": [
      {
        "type": "volume",
        "source": "nginx-data",
        "destination": "/usr/share/nginx/html",
        "mode": "",
        "rw": true
      }
    ],
    "network": "bridge",
    "restart_policy": "unless-stopped"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": "7f2d1d2f58cca"
    }
  }
  ```

#### 7.3 启动容器

- **URL**: `/api/container/start`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "id": "7f2d1d2f58cca"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "Container started successfully",
    "data": null
  }
  ```

#### 7.4 停止容器

- **URL**: `/api/container/stop`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "id": "7f2d1d2f58cca",
    "timeout": 10
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "Container stopped successfully",
    "data": null
  }
  ```

#### 7.5 重启容器

- **URL**: `/api/container/restart`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "id": "7f2d1d2f58cca",
    "timeout": 10
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "Container restarted successfully",
    "data": null
  }
  ```

#### 7.6 删除容器

- **URL**: `/api/container`
- **方法**: `DELETE`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "id": "7f2d1d2f58cca",
    "force": false,
    "volumes": false
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "Container removed successfully",
    "data": null
  }
  ```

#### 7.7 获取容器日志

- **URL**: `/api/container/{id}/logs`
- **方法**: `GET`
- **请求头**: 需要认证
- **请求参数**:
  - `tail`: 返回的日志行数（默认100）
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "logs": "2023-01-01T00:00:00.000Z stdout F ********** - - [01/Jan/2023:00:00:00 +0000] \"GET / HTTP/1.1\" 200 612 \"-\" \"Mozilla/5.0\" \"-\"\n2023-01-01T00:00:01.000Z stdout F ********** - - [01/Jan/2023:00:00:01 +0000] \"GET /favicon.ico HTTP/1.1\" 404 555 \"-\" \"Mozilla/5.0\" \"-\"\n"
    }
  }
  ```

#### 7.8 获取容器统计信息

- **URL**: `/api/container/{id}/stats`
- **方法**: `GET`
- **请求头**: 需要认证
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "cpu_usage": 0.5,
      "memory_usage": {
        "usage": 10485760,
        "limit": 2147483648,
        "percent": 0.5
      },
      "network": {
        "rx_bytes": 1024,
        "tx_bytes": 512
      },
      "block_io": {
        "read_bytes": 1024,
        "write_bytes": 512
      },
      "pids": 5
    }
  }
  ```

#### 7.9 获取镜像列表

- **URL**: `/api/container/image`
- **方法**: `GET`
- **请求头**: 需要认证
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": [
      {
        "id": "sha256:a7be6198544f",
        "repository": "nginx",
        "tag": "latest",
        "digest": "sha256:1234567890abcdef",
        "created_at": "2023-01-01T00:00:00Z",
        "size": 133456789,
        "shared_size": 0,
        "virtual_size": 133456789,
        "labels": {
          "maintainer": "NGINX Docker Maintainers"
        }
      },
      {
        "id": "sha256:b8c9d3ef456g",
        "repository": "mysql",
        "tag": "8.0",
        "digest": "sha256:0987654321fedcba",
        "created_at": "2023-01-01T00:00:00Z",
        "size": 524288000,
        "shared_size": 0,
        "virtual_size": 524288000,
        "labels": {
          "maintainer": "MySQL Team"
        }
      }
    ]
  }
  ```

#### 7.10 拉取镜像

- **URL**: `/api/container/image/pull`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "name": "nginx",
    "tag": "latest"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "Image pulled successfully",
    "data": null
  }
  ```

#### 7.11 删除镜像

- **URL**: `/api/container/image`
- **方法**: `DELETE`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "id": "sha256:a7be6198544f",
    "force": false
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "Image removed successfully",
    "data": null
  }
  ```

#### 7.12 获取网络列表

- **URL**: `/api/container/network`
- **方法**: `GET`
- **请求头**: 需要认证
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": [
      {
        "id": "7f2d1d2f58cca",
        "name": "bridge",
        "driver": "bridge",
        "scope": "local",
        "subnet": "**********/16",
        "gateway": "**********"
      },
      {
        "id": "9a8b7c6d5e4f",
        "name": "host",
        "driver": "host",
        "scope": "local",
        "subnet": "",
        "gateway": ""
      }
    ]
  }
  ```

#### 7.13 创建网络

- **URL**: `/api/container/network`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "name": "mynetwork",
    "driver": "bridge"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "Network created successfully",
    "data": {
      "id": "7f2d1d2f58cca"
    }
  }
  ```

#### 7.14 删除网络

- **URL**: `/api/container/network`
- **方法**: `DELETE`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "id": "7f2d1d2f58cca"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "Network removed successfully",
    "data": null
  }
  ```

#### 7.15 获取数据卷列表

- **URL**: `/api/container/volume`
- **方法**: `GET`
- **请求头**: 需要认证
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": [
      {
        "name": "nginx-data",
        "driver": "local",
        "mountpoint": "/var/lib/docker/volumes/nginx-data/_data",
        "created_at": "2023-01-01T00:00:00Z",
        "labels": {}
      },
      {
        "name": "mysql-data",
        "driver": "local",
        "mountpoint": "/var/lib/docker/volumes/mysql-data/_data",
        "created_at": "2023-01-01T00:00:00Z",
        "labels": {
          "com.docker.compose.project": "myproject"
        }
      }
    ]
  }
  ```

#### 7.16 创建数据卷

- **URL**: `/api/container/volume`
- **方法**: `POST`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "name": "myvolume"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "Volume created successfully",
    "data": {
      "name": "myvolume"
    }
  }
  ```

#### 7.17 删除数据卷

- **URL**: `/api/container/volume`
- **方法**: `DELETE`
- **请求头**: 需要认证
- **请求体**:
  ```json
  {
    "name": "myvolume",
    "force": false
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "Volume removed successfully",
    "data": null
  }
  ```

### 8. WebSocket接口

#### 7.1 终端会话

- **URL**: `/ws/terminal`
- **方法**: `WebSocket`
- **请求头**: 需要认证
- **请求参数**:
  - `token`: JWT令牌
- **消息格式**:
  ```json
  {
    "type": "command",
    "data": "ls -la"
  }
  ```
- **响应格式**:
  ```json
  {
    "type": "output",
    "data": "total 20\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Jan 1 00:00 .\ndrwxr-xr-x 6 <USER> <GROUP> 4096 Jan 1 00:00 ..\n-rw-r--r-- 1 <USER> <GROUP> 1024 Jan 1 00:00 file.txt\n"
  }
  ```

#### 7.2 实时系统状态

- **URL**: `/ws/stats`
- **方法**: `WebSocket`
- **请求头**: 需要认证
- **请求参数**:
  - `token`: JWT令牌
- **响应格式**:
  ```json
  {
    "cpu_usage": 25.5,
    "memory_usage": 50.0,
    "disk_usage": 50.0,
    "network": [
      {
        "interface": "eth0",
        "rx_bytes": 1024000,
        "tx_bytes": 512000
      }
    ],
    "timestamp": "2023-01-01T00:00:00Z"
  }
  ```

## 三、错误码定义

| 错误码 | 描述 |
|--------|------|
| 0 | 成功 |
| 1000 | 未知错误 |
| 1001 | 参数错误 |
| 1002 | 资源不存在 |
| 1003 | 资源已存在 |
| 1100 | 认证失败 |
| 1101 | 令牌过期 |
| 1102 | 权限不足 |
| 2000 | 系统错误 |
| 2001 | 数据库错误 |
| 2002 | 文件系统错误 |
| 3000 | 网站错误 |
| 3001 | 网站配置错误 |
| 4000 | 数据库服务错误 |
| 4001 | 数据库连接错误 |
| 5000 | 服务管理错误 |
| 5001 | 服务启动失败 |
| 5002 | 服务停止失败 |
| 6000 | 容器错误 |
| 6001 | 容器创建失败 |
| 6002 | 容器操作失败 |
| 6003 | 镜像操作失败 |