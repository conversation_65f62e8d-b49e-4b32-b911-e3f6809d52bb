// Code generated by mockery. DO NOT EDIT.

package biz

import (
	image "image"

	biz "github.com/tnb-labs/panel/internal/biz"

	mock "github.com/stretchr/testify/mock"
)

// UserRepo is an autogenerated mock type for the UserRepo type
type UserRepo struct {
	mock.Mock
}

type UserRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *UserRepo) EXPECT() *UserRepo_Expecter {
	return &UserRepo_Expecter{mock: &_m.Mock}
}

// CheckPassword provides a mock function with given fields: username, password
func (_m *UserRepo) CheckPassword(username string, password string) (*biz.User, error) {
	ret := _m.Called(username, password)

	if len(ret) == 0 {
		panic("no return value specified for CheckPassword")
	}

	var r0 *biz.User
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*biz.User, error)); ok {
		return rf(username, password)
	}
	if rf, ok := ret.Get(0).(func(string, string) *biz.User); ok {
		r0 = rf(username, password)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.User)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(username, password)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepo_CheckPassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckPassword'
type UserRepo_CheckPassword_Call struct {
	*mock.Call
}

// CheckPassword is a helper method to define mock.On call
//   - username string
//   - password string
func (_e *UserRepo_Expecter) CheckPassword(username interface{}, password interface{}) *UserRepo_CheckPassword_Call {
	return &UserRepo_CheckPassword_Call{Call: _e.mock.On("CheckPassword", username, password)}
}

func (_c *UserRepo_CheckPassword_Call) Run(run func(username string, password string)) *UserRepo_CheckPassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *UserRepo_CheckPassword_Call) Return(_a0 *biz.User, _a1 error) *UserRepo_CheckPassword_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepo_CheckPassword_Call) RunAndReturn(run func(string, string) (*biz.User, error)) *UserRepo_CheckPassword_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: username, password, email
func (_m *UserRepo) Create(username string, password string, email string) (*biz.User, error) {
	ret := _m.Called(username, password, email)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *biz.User
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string) (*biz.User, error)); ok {
		return rf(username, password, email)
	}
	if rf, ok := ret.Get(0).(func(string, string, string) *biz.User); ok {
		r0 = rf(username, password, email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.User)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(username, password, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type UserRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - username string
//   - password string
//   - email string
func (_e *UserRepo_Expecter) Create(username interface{}, password interface{}, email interface{}) *UserRepo_Create_Call {
	return &UserRepo_Create_Call{Call: _e.mock.On("Create", username, password, email)}
}

func (_c *UserRepo_Create_Call) Run(run func(username string, password string, email string)) *UserRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *UserRepo_Create_Call) Return(_a0 *biz.User, _a1 error) *UserRepo_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepo_Create_Call) RunAndReturn(run func(string, string, string) (*biz.User, error)) *UserRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: id
func (_m *UserRepo) Delete(id uint) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepo_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type UserRepo_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - id uint
func (_e *UserRepo_Expecter) Delete(id interface{}) *UserRepo_Delete_Call {
	return &UserRepo_Delete_Call{Call: _e.mock.On("Delete", id)}
}

func (_c *UserRepo_Delete_Call) Run(run func(id uint)) *UserRepo_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *UserRepo_Delete_Call) Return(_a0 error) *UserRepo_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepo_Delete_Call) RunAndReturn(run func(uint) error) *UserRepo_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// GenerateTwoFA provides a mock function with given fields: id
func (_m *UserRepo) GenerateTwoFA(id uint) (image.Image, string, string, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GenerateTwoFA")
	}

	var r0 image.Image
	var r1 string
	var r2 string
	var r3 error
	if rf, ok := ret.Get(0).(func(uint) (image.Image, string, string, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) image.Image); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(image.Image)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) string); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(uint) string); ok {
		r2 = rf(id)
	} else {
		r2 = ret.Get(2).(string)
	}

	if rf, ok := ret.Get(3).(func(uint) error); ok {
		r3 = rf(id)
	} else {
		r3 = ret.Error(3)
	}

	return r0, r1, r2, r3
}

// UserRepo_GenerateTwoFA_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GenerateTwoFA'
type UserRepo_GenerateTwoFA_Call struct {
	*mock.Call
}

// GenerateTwoFA is a helper method to define mock.On call
//   - id uint
func (_e *UserRepo_Expecter) GenerateTwoFA(id interface{}) *UserRepo_GenerateTwoFA_Call {
	return &UserRepo_GenerateTwoFA_Call{Call: _e.mock.On("GenerateTwoFA", id)}
}

func (_c *UserRepo_GenerateTwoFA_Call) Run(run func(id uint)) *UserRepo_GenerateTwoFA_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *UserRepo_GenerateTwoFA_Call) Return(_a0 image.Image, _a1 string, _a2 string, _a3 error) *UserRepo_GenerateTwoFA_Call {
	_c.Call.Return(_a0, _a1, _a2, _a3)
	return _c
}

func (_c *UserRepo_GenerateTwoFA_Call) RunAndReturn(run func(uint) (image.Image, string, string, error)) *UserRepo_GenerateTwoFA_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: id
func (_m *UserRepo) Get(id uint) (*biz.User, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *biz.User
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*biz.User, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) *biz.User); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.User)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type UserRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - id uint
func (_e *UserRepo_Expecter) Get(id interface{}) *UserRepo_Get_Call {
	return &UserRepo_Get_Call{Call: _e.mock.On("Get", id)}
}

func (_c *UserRepo_Get_Call) Run(run func(id uint)) *UserRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *UserRepo_Get_Call) Return(_a0 *biz.User, _a1 error) *UserRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepo_Get_Call) RunAndReturn(run func(uint) (*biz.User, error)) *UserRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// IsTwoFA provides a mock function with given fields: username
func (_m *UserRepo) IsTwoFA(username string) (bool, error) {
	ret := _m.Called(username)

	if len(ret) == 0 {
		panic("no return value specified for IsTwoFA")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (bool, error)); ok {
		return rf(username)
	}
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(username)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(username)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepo_IsTwoFA_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsTwoFA'
type UserRepo_IsTwoFA_Call struct {
	*mock.Call
}

// IsTwoFA is a helper method to define mock.On call
//   - username string
func (_e *UserRepo_Expecter) IsTwoFA(username interface{}) *UserRepo_IsTwoFA_Call {
	return &UserRepo_IsTwoFA_Call{Call: _e.mock.On("IsTwoFA", username)}
}

func (_c *UserRepo_IsTwoFA_Call) Run(run func(username string)) *UserRepo_IsTwoFA_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *UserRepo_IsTwoFA_Call) Return(_a0 bool, _a1 error) *UserRepo_IsTwoFA_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepo_IsTwoFA_Call) RunAndReturn(run func(string) (bool, error)) *UserRepo_IsTwoFA_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: page, limit
func (_m *UserRepo) List(page uint, limit uint) ([]*biz.User, int64, error) {
	ret := _m.Called(page, limit)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*biz.User
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(uint, uint) ([]*biz.User, int64, error)); ok {
		return rf(page, limit)
	}
	if rf, ok := ret.Get(0).(func(uint, uint) []*biz.User); ok {
		r0 = rf(page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*biz.User)
		}
	}

	if rf, ok := ret.Get(1).(func(uint, uint) int64); ok {
		r1 = rf(page, limit)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(uint, uint) error); ok {
		r2 = rf(page, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// UserRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type UserRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - page uint
//   - limit uint
func (_e *UserRepo_Expecter) List(page interface{}, limit interface{}) *UserRepo_List_Call {
	return &UserRepo_List_Call{Call: _e.mock.On("List", page, limit)}
}

func (_c *UserRepo_List_Call) Run(run func(page uint, limit uint)) *UserRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(uint))
	})
	return _c
}

func (_c *UserRepo_List_Call) Return(_a0 []*biz.User, _a1 int64, _a2 error) *UserRepo_List_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *UserRepo_List_Call) RunAndReturn(run func(uint, uint) ([]*biz.User, int64, error)) *UserRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateEmail provides a mock function with given fields: id, email
func (_m *UserRepo) UpdateEmail(id uint, email string) error {
	ret := _m.Called(id, email)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEmail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint, string) error); ok {
		r0 = rf(id, email)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepo_UpdateEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateEmail'
type UserRepo_UpdateEmail_Call struct {
	*mock.Call
}

// UpdateEmail is a helper method to define mock.On call
//   - id uint
//   - email string
func (_e *UserRepo_Expecter) UpdateEmail(id interface{}, email interface{}) *UserRepo_UpdateEmail_Call {
	return &UserRepo_UpdateEmail_Call{Call: _e.mock.On("UpdateEmail", id, email)}
}

func (_c *UserRepo_UpdateEmail_Call) Run(run func(id uint, email string)) *UserRepo_UpdateEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(string))
	})
	return _c
}

func (_c *UserRepo_UpdateEmail_Call) Return(_a0 error) *UserRepo_UpdateEmail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepo_UpdateEmail_Call) RunAndReturn(run func(uint, string) error) *UserRepo_UpdateEmail_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePassword provides a mock function with given fields: id, password
func (_m *UserRepo) UpdatePassword(id uint, password string) error {
	ret := _m.Called(id, password)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePassword")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint, string) error); ok {
		r0 = rf(id, password)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepo_UpdatePassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePassword'
type UserRepo_UpdatePassword_Call struct {
	*mock.Call
}

// UpdatePassword is a helper method to define mock.On call
//   - id uint
//   - password string
func (_e *UserRepo_Expecter) UpdatePassword(id interface{}, password interface{}) *UserRepo_UpdatePassword_Call {
	return &UserRepo_UpdatePassword_Call{Call: _e.mock.On("UpdatePassword", id, password)}
}

func (_c *UserRepo_UpdatePassword_Call) Run(run func(id uint, password string)) *UserRepo_UpdatePassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(string))
	})
	return _c
}

func (_c *UserRepo_UpdatePassword_Call) Return(_a0 error) *UserRepo_UpdatePassword_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepo_UpdatePassword_Call) RunAndReturn(run func(uint, string) error) *UserRepo_UpdatePassword_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTwoFA provides a mock function with given fields: id, code, secret
func (_m *UserRepo) UpdateTwoFA(id uint, code string, secret string) error {
	ret := _m.Called(id, code, secret)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTwoFA")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint, string, string) error); ok {
		r0 = rf(id, code, secret)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepo_UpdateTwoFA_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTwoFA'
type UserRepo_UpdateTwoFA_Call struct {
	*mock.Call
}

// UpdateTwoFA is a helper method to define mock.On call
//   - id uint
//   - code string
//   - secret string
func (_e *UserRepo_Expecter) UpdateTwoFA(id interface{}, code interface{}, secret interface{}) *UserRepo_UpdateTwoFA_Call {
	return &UserRepo_UpdateTwoFA_Call{Call: _e.mock.On("UpdateTwoFA", id, code, secret)}
}

func (_c *UserRepo_UpdateTwoFA_Call) Run(run func(id uint, code string, secret string)) *UserRepo_UpdateTwoFA_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *UserRepo_UpdateTwoFA_Call) Return(_a0 error) *UserRepo_UpdateTwoFA_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepo_UpdateTwoFA_Call) RunAndReturn(run func(uint, string, string) error) *UserRepo_UpdateTwoFA_Call {
	_c.Call.Return(run)
	return _c
}

// NewUserRepo creates a new instance of UserRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserRepo {
	mock := &UserRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
