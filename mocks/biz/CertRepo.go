// Code generated by mockery. DO NOT EDIT.

package biz

import (
	biz "github.com/tnb-labs/panel/internal/biz"
	acme "github.com/tnb-labs/panel/pkg/acme"

	mock "github.com/stretchr/testify/mock"

	request "github.com/tnb-labs/panel/internal/http/request"

	types "github.com/tnb-labs/panel/pkg/types"
)

// CertRepo is an autogenerated mock type for the CertRepo type
type CertRepo struct {
	mock.Mock
}

type CertRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *CertRepo) EXPECT() *CertRepo_Expecter {
	return &CertRepo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: req
func (_m *CertRepo) Create(req *request.CertCreate) (*biz.Cert, error) {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *biz.Cert
	var r1 error
	if rf, ok := ret.Get(0).(func(*request.CertCreate) (*biz.Cert, error)); ok {
		return rf(req)
	}
	if rf, ok := ret.Get(0).(func(*request.CertCreate) *biz.Cert); ok {
		r0 = rf(req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.Cert)
		}
	}

	if rf, ok := ret.Get(1).(func(*request.CertCreate) error); ok {
		r1 = rf(req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type CertRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - req *request.CertCreate
func (_e *CertRepo_Expecter) Create(req interface{}) *CertRepo_Create_Call {
	return &CertRepo_Create_Call{Call: _e.mock.On("Create", req)}
}

func (_c *CertRepo_Create_Call) Run(run func(req *request.CertCreate)) *CertRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.CertCreate))
	})
	return _c
}

func (_c *CertRepo_Create_Call) Return(_a0 *biz.Cert, _a1 error) *CertRepo_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CertRepo_Create_Call) RunAndReturn(run func(*request.CertCreate) (*biz.Cert, error)) *CertRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: id
func (_m *CertRepo) Delete(id uint) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CertRepo_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type CertRepo_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - id uint
func (_e *CertRepo_Expecter) Delete(id interface{}) *CertRepo_Delete_Call {
	return &CertRepo_Delete_Call{Call: _e.mock.On("Delete", id)}
}

func (_c *CertRepo_Delete_Call) Run(run func(id uint)) *CertRepo_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CertRepo_Delete_Call) Return(_a0 error) *CertRepo_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CertRepo_Delete_Call) RunAndReturn(run func(uint) error) *CertRepo_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Deploy provides a mock function with given fields: ID, WebsiteID
func (_m *CertRepo) Deploy(ID uint, WebsiteID uint) error {
	ret := _m.Called(ID, WebsiteID)

	if len(ret) == 0 {
		panic("no return value specified for Deploy")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint, uint) error); ok {
		r0 = rf(ID, WebsiteID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CertRepo_Deploy_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Deploy'
type CertRepo_Deploy_Call struct {
	*mock.Call
}

// Deploy is a helper method to define mock.On call
//   - ID uint
//   - WebsiteID uint
func (_e *CertRepo_Expecter) Deploy(ID interface{}, WebsiteID interface{}) *CertRepo_Deploy_Call {
	return &CertRepo_Deploy_Call{Call: _e.mock.On("Deploy", ID, WebsiteID)}
}

func (_c *CertRepo_Deploy_Call) Run(run func(ID uint, WebsiteID uint)) *CertRepo_Deploy_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(uint))
	})
	return _c
}

func (_c *CertRepo_Deploy_Call) Return(_a0 error) *CertRepo_Deploy_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CertRepo_Deploy_Call) RunAndReturn(run func(uint, uint) error) *CertRepo_Deploy_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: id
func (_m *CertRepo) Get(id uint) (*biz.Cert, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *biz.Cert
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*biz.Cert, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) *biz.Cert); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.Cert)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type CertRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - id uint
func (_e *CertRepo_Expecter) Get(id interface{}) *CertRepo_Get_Call {
	return &CertRepo_Get_Call{Call: _e.mock.On("Get", id)}
}

func (_c *CertRepo_Get_Call) Run(run func(id uint)) *CertRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CertRepo_Get_Call) Return(_a0 *biz.Cert, _a1 error) *CertRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CertRepo_Get_Call) RunAndReturn(run func(uint) (*biz.Cert, error)) *CertRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetByWebsite provides a mock function with given fields: WebsiteID
func (_m *CertRepo) GetByWebsite(WebsiteID uint) (*biz.Cert, error) {
	ret := _m.Called(WebsiteID)

	if len(ret) == 0 {
		panic("no return value specified for GetByWebsite")
	}

	var r0 *biz.Cert
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*biz.Cert, error)); ok {
		return rf(WebsiteID)
	}
	if rf, ok := ret.Get(0).(func(uint) *biz.Cert); ok {
		r0 = rf(WebsiteID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.Cert)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(WebsiteID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertRepo_GetByWebsite_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByWebsite'
type CertRepo_GetByWebsite_Call struct {
	*mock.Call
}

// GetByWebsite is a helper method to define mock.On call
//   - WebsiteID uint
func (_e *CertRepo_Expecter) GetByWebsite(WebsiteID interface{}) *CertRepo_GetByWebsite_Call {
	return &CertRepo_GetByWebsite_Call{Call: _e.mock.On("GetByWebsite", WebsiteID)}
}

func (_c *CertRepo_GetByWebsite_Call) Run(run func(WebsiteID uint)) *CertRepo_GetByWebsite_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CertRepo_GetByWebsite_Call) Return(_a0 *biz.Cert, _a1 error) *CertRepo_GetByWebsite_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CertRepo_GetByWebsite_Call) RunAndReturn(run func(uint) (*biz.Cert, error)) *CertRepo_GetByWebsite_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: page, limit
func (_m *CertRepo) List(page uint, limit uint) ([]*types.CertList, int64, error) {
	ret := _m.Called(page, limit)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*types.CertList
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(uint, uint) ([]*types.CertList, int64, error)); ok {
		return rf(page, limit)
	}
	if rf, ok := ret.Get(0).(func(uint, uint) []*types.CertList); ok {
		r0 = rf(page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*types.CertList)
		}
	}

	if rf, ok := ret.Get(1).(func(uint, uint) int64); ok {
		r1 = rf(page, limit)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(uint, uint) error); ok {
		r2 = rf(page, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// CertRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type CertRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - page uint
//   - limit uint
func (_e *CertRepo_Expecter) List(page interface{}, limit interface{}) *CertRepo_List_Call {
	return &CertRepo_List_Call{Call: _e.mock.On("List", page, limit)}
}

func (_c *CertRepo_List_Call) Run(run func(page uint, limit uint)) *CertRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(uint))
	})
	return _c
}

func (_c *CertRepo_List_Call) Return(_a0 []*types.CertList, _a1 int64, _a2 error) *CertRepo_List_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *CertRepo_List_Call) RunAndReturn(run func(uint, uint) ([]*types.CertList, int64, error)) *CertRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// ManualDNS provides a mock function with given fields: id
func (_m *CertRepo) ManualDNS(id uint) ([]acme.DNSRecord, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for ManualDNS")
	}

	var r0 []acme.DNSRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) ([]acme.DNSRecord, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) []acme.DNSRecord); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]acme.DNSRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertRepo_ManualDNS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ManualDNS'
type CertRepo_ManualDNS_Call struct {
	*mock.Call
}

// ManualDNS is a helper method to define mock.On call
//   - id uint
func (_e *CertRepo_Expecter) ManualDNS(id interface{}) *CertRepo_ManualDNS_Call {
	return &CertRepo_ManualDNS_Call{Call: _e.mock.On("ManualDNS", id)}
}

func (_c *CertRepo_ManualDNS_Call) Run(run func(id uint)) *CertRepo_ManualDNS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CertRepo_ManualDNS_Call) Return(_a0 []acme.DNSRecord, _a1 error) *CertRepo_ManualDNS_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CertRepo_ManualDNS_Call) RunAndReturn(run func(uint) ([]acme.DNSRecord, error)) *CertRepo_ManualDNS_Call {
	_c.Call.Return(run)
	return _c
}

// ObtainAuto provides a mock function with given fields: id
func (_m *CertRepo) ObtainAuto(id uint) (*acme.Certificate, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for ObtainAuto")
	}

	var r0 *acme.Certificate
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*acme.Certificate, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) *acme.Certificate); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*acme.Certificate)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertRepo_ObtainAuto_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ObtainAuto'
type CertRepo_ObtainAuto_Call struct {
	*mock.Call
}

// ObtainAuto is a helper method to define mock.On call
//   - id uint
func (_e *CertRepo_Expecter) ObtainAuto(id interface{}) *CertRepo_ObtainAuto_Call {
	return &CertRepo_ObtainAuto_Call{Call: _e.mock.On("ObtainAuto", id)}
}

func (_c *CertRepo_ObtainAuto_Call) Run(run func(id uint)) *CertRepo_ObtainAuto_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CertRepo_ObtainAuto_Call) Return(_a0 *acme.Certificate, _a1 error) *CertRepo_ObtainAuto_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CertRepo_ObtainAuto_Call) RunAndReturn(run func(uint) (*acme.Certificate, error)) *CertRepo_ObtainAuto_Call {
	_c.Call.Return(run)
	return _c
}

// ObtainManual provides a mock function with given fields: id
func (_m *CertRepo) ObtainManual(id uint) (*acme.Certificate, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for ObtainManual")
	}

	var r0 *acme.Certificate
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*acme.Certificate, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) *acme.Certificate); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*acme.Certificate)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertRepo_ObtainManual_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ObtainManual'
type CertRepo_ObtainManual_Call struct {
	*mock.Call
}

// ObtainManual is a helper method to define mock.On call
//   - id uint
func (_e *CertRepo_Expecter) ObtainManual(id interface{}) *CertRepo_ObtainManual_Call {
	return &CertRepo_ObtainManual_Call{Call: _e.mock.On("ObtainManual", id)}
}

func (_c *CertRepo_ObtainManual_Call) Run(run func(id uint)) *CertRepo_ObtainManual_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CertRepo_ObtainManual_Call) Return(_a0 *acme.Certificate, _a1 error) *CertRepo_ObtainManual_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CertRepo_ObtainManual_Call) RunAndReturn(run func(uint) (*acme.Certificate, error)) *CertRepo_ObtainManual_Call {
	_c.Call.Return(run)
	return _c
}

// ObtainSelfSigned provides a mock function with given fields: id
func (_m *CertRepo) ObtainSelfSigned(id uint) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for ObtainSelfSigned")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CertRepo_ObtainSelfSigned_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ObtainSelfSigned'
type CertRepo_ObtainSelfSigned_Call struct {
	*mock.Call
}

// ObtainSelfSigned is a helper method to define mock.On call
//   - id uint
func (_e *CertRepo_Expecter) ObtainSelfSigned(id interface{}) *CertRepo_ObtainSelfSigned_Call {
	return &CertRepo_ObtainSelfSigned_Call{Call: _e.mock.On("ObtainSelfSigned", id)}
}

func (_c *CertRepo_ObtainSelfSigned_Call) Run(run func(id uint)) *CertRepo_ObtainSelfSigned_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CertRepo_ObtainSelfSigned_Call) Return(_a0 error) *CertRepo_ObtainSelfSigned_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CertRepo_ObtainSelfSigned_Call) RunAndReturn(run func(uint) error) *CertRepo_ObtainSelfSigned_Call {
	_c.Call.Return(run)
	return _c
}

// Renew provides a mock function with given fields: id
func (_m *CertRepo) Renew(id uint) (*acme.Certificate, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Renew")
	}

	var r0 *acme.Certificate
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*acme.Certificate, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) *acme.Certificate); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*acme.Certificate)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertRepo_Renew_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Renew'
type CertRepo_Renew_Call struct {
	*mock.Call
}

// Renew is a helper method to define mock.On call
//   - id uint
func (_e *CertRepo_Expecter) Renew(id interface{}) *CertRepo_Renew_Call {
	return &CertRepo_Renew_Call{Call: _e.mock.On("Renew", id)}
}

func (_c *CertRepo_Renew_Call) Run(run func(id uint)) *CertRepo_Renew_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CertRepo_Renew_Call) Return(_a0 *acme.Certificate, _a1 error) *CertRepo_Renew_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CertRepo_Renew_Call) RunAndReturn(run func(uint) (*acme.Certificate, error)) *CertRepo_Renew_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: req
func (_m *CertRepo) Update(req *request.CertUpdate) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.CertUpdate) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CertRepo_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type CertRepo_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - req *request.CertUpdate
func (_e *CertRepo_Expecter) Update(req interface{}) *CertRepo_Update_Call {
	return &CertRepo_Update_Call{Call: _e.mock.On("Update", req)}
}

func (_c *CertRepo_Update_Call) Run(run func(req *request.CertUpdate)) *CertRepo_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.CertUpdate))
	})
	return _c
}

func (_c *CertRepo_Update_Call) Return(_a0 error) *CertRepo_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CertRepo_Update_Call) RunAndReturn(run func(*request.CertUpdate) error) *CertRepo_Update_Call {
	_c.Call.Return(run)
	return _c
}

// Upload provides a mock function with given fields: req
func (_m *CertRepo) Upload(req *request.CertUpload) (*biz.Cert, error) {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Upload")
	}

	var r0 *biz.Cert
	var r1 error
	if rf, ok := ret.Get(0).(func(*request.CertUpload) (*biz.Cert, error)); ok {
		return rf(req)
	}
	if rf, ok := ret.Get(0).(func(*request.CertUpload) *biz.Cert); ok {
		r0 = rf(req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.Cert)
		}
	}

	if rf, ok := ret.Get(1).(func(*request.CertUpload) error); ok {
		r1 = rf(req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertRepo_Upload_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Upload'
type CertRepo_Upload_Call struct {
	*mock.Call
}

// Upload is a helper method to define mock.On call
//   - req *request.CertUpload
func (_e *CertRepo_Expecter) Upload(req interface{}) *CertRepo_Upload_Call {
	return &CertRepo_Upload_Call{Call: _e.mock.On("Upload", req)}
}

func (_c *CertRepo_Upload_Call) Run(run func(req *request.CertUpload)) *CertRepo_Upload_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.CertUpload))
	})
	return _c
}

func (_c *CertRepo_Upload_Call) Return(_a0 *biz.Cert, _a1 error) *CertRepo_Upload_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CertRepo_Upload_Call) RunAndReturn(run func(*request.CertUpload) (*biz.Cert, error)) *CertRepo_Upload_Call {
	_c.Call.Return(run)
	return _c
}

// NewCertRepo creates a new instance of CertRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCertRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *CertRepo {
	mock := &CertRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
