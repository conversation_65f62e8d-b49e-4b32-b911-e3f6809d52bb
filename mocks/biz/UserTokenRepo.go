// Code generated by mockery. DO NOT EDIT.

package biz

import (
	http "net/http"

	biz "github.com/tnb-labs/panel/internal/biz"

	mock "github.com/stretchr/testify/mock"

	time "time"
)

// UserTokenRepo is an autogenerated mock type for the UserTokenRepo type
type UserTokenRepo struct {
	mock.Mock
}

type UserTokenRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *UserTokenRepo) EXPECT() *UserTokenRepo_Expecter {
	return &UserTokenRepo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: userID, ips, expired
func (_m *UserTokenRepo) Create(userID uint, ips []string, expired time.Time) (*biz.UserToken, error) {
	ret := _m.Called(userID, ips, expired)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *biz.UserToken
	var r1 error
	if rf, ok := ret.Get(0).(func(uint, []string, time.Time) (*biz.UserToken, error)); ok {
		return rf(userID, ips, expired)
	}
	if rf, ok := ret.Get(0).(func(uint, []string, time.Time) *biz.UserToken); ok {
		r0 = rf(userID, ips, expired)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.UserToken)
		}
	}

	if rf, ok := ret.Get(1).(func(uint, []string, time.Time) error); ok {
		r1 = rf(userID, ips, expired)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserTokenRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type UserTokenRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - userID uint
//   - ips []string
//   - expired time.Time
func (_e *UserTokenRepo_Expecter) Create(userID interface{}, ips interface{}, expired interface{}) *UserTokenRepo_Create_Call {
	return &UserTokenRepo_Create_Call{Call: _e.mock.On("Create", userID, ips, expired)}
}

func (_c *UserTokenRepo_Create_Call) Run(run func(userID uint, ips []string, expired time.Time)) *UserTokenRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].([]string), args[2].(time.Time))
	})
	return _c
}

func (_c *UserTokenRepo_Create_Call) Return(_a0 *biz.UserToken, _a1 error) *UserTokenRepo_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserTokenRepo_Create_Call) RunAndReturn(run func(uint, []string, time.Time) (*biz.UserToken, error)) *UserTokenRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: id
func (_m *UserTokenRepo) Delete(id uint) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserTokenRepo_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type UserTokenRepo_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - id uint
func (_e *UserTokenRepo_Expecter) Delete(id interface{}) *UserTokenRepo_Delete_Call {
	return &UserTokenRepo_Delete_Call{Call: _e.mock.On("Delete", id)}
}

func (_c *UserTokenRepo_Delete_Call) Run(run func(id uint)) *UserTokenRepo_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *UserTokenRepo_Delete_Call) Return(_a0 error) *UserTokenRepo_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserTokenRepo_Delete_Call) RunAndReturn(run func(uint) error) *UserTokenRepo_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: id
func (_m *UserTokenRepo) Get(id uint) (*biz.UserToken, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *biz.UserToken
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*biz.UserToken, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) *biz.UserToken); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.UserToken)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserTokenRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type UserTokenRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - id uint
func (_e *UserTokenRepo_Expecter) Get(id interface{}) *UserTokenRepo_Get_Call {
	return &UserTokenRepo_Get_Call{Call: _e.mock.On("Get", id)}
}

func (_c *UserTokenRepo_Get_Call) Run(run func(id uint)) *UserTokenRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *UserTokenRepo_Get_Call) Return(_a0 *biz.UserToken, _a1 error) *UserTokenRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserTokenRepo_Get_Call) RunAndReturn(run func(uint) (*biz.UserToken, error)) *UserTokenRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: userID, page, limit
func (_m *UserTokenRepo) List(userID uint, page uint, limit uint) ([]*biz.UserToken, int64, error) {
	ret := _m.Called(userID, page, limit)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*biz.UserToken
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(uint, uint, uint) ([]*biz.UserToken, int64, error)); ok {
		return rf(userID, page, limit)
	}
	if rf, ok := ret.Get(0).(func(uint, uint, uint) []*biz.UserToken); ok {
		r0 = rf(userID, page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*biz.UserToken)
		}
	}

	if rf, ok := ret.Get(1).(func(uint, uint, uint) int64); ok {
		r1 = rf(userID, page, limit)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(uint, uint, uint) error); ok {
		r2 = rf(userID, page, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// UserTokenRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type UserTokenRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - userID uint
//   - page uint
//   - limit uint
func (_e *UserTokenRepo_Expecter) List(userID interface{}, page interface{}, limit interface{}) *UserTokenRepo_List_Call {
	return &UserTokenRepo_List_Call{Call: _e.mock.On("List", userID, page, limit)}
}

func (_c *UserTokenRepo_List_Call) Run(run func(userID uint, page uint, limit uint)) *UserTokenRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(uint), args[2].(uint))
	})
	return _c
}

func (_c *UserTokenRepo_List_Call) Return(_a0 []*biz.UserToken, _a1 int64, _a2 error) *UserTokenRepo_List_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *UserTokenRepo_List_Call) RunAndReturn(run func(uint, uint, uint) ([]*biz.UserToken, int64, error)) *UserTokenRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: id, ips, expired
func (_m *UserTokenRepo) Update(id uint, ips []string, expired time.Time) (*biz.UserToken, error) {
	ret := _m.Called(id, ips, expired)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 *biz.UserToken
	var r1 error
	if rf, ok := ret.Get(0).(func(uint, []string, time.Time) (*biz.UserToken, error)); ok {
		return rf(id, ips, expired)
	}
	if rf, ok := ret.Get(0).(func(uint, []string, time.Time) *biz.UserToken); ok {
		r0 = rf(id, ips, expired)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.UserToken)
		}
	}

	if rf, ok := ret.Get(1).(func(uint, []string, time.Time) error); ok {
		r1 = rf(id, ips, expired)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserTokenRepo_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type UserTokenRepo_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - id uint
//   - ips []string
//   - expired time.Time
func (_e *UserTokenRepo_Expecter) Update(id interface{}, ips interface{}, expired interface{}) *UserTokenRepo_Update_Call {
	return &UserTokenRepo_Update_Call{Call: _e.mock.On("Update", id, ips, expired)}
}

func (_c *UserTokenRepo_Update_Call) Run(run func(id uint, ips []string, expired time.Time)) *UserTokenRepo_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].([]string), args[2].(time.Time))
	})
	return _c
}

func (_c *UserTokenRepo_Update_Call) Return(_a0 *biz.UserToken, _a1 error) *UserTokenRepo_Update_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserTokenRepo_Update_Call) RunAndReturn(run func(uint, []string, time.Time) (*biz.UserToken, error)) *UserTokenRepo_Update_Call {
	_c.Call.Return(run)
	return _c
}

// ValidateReq provides a mock function with given fields: req
func (_m *UserTokenRepo) ValidateReq(req *http.Request) (uint, error) {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for ValidateReq")
	}

	var r0 uint
	var r1 error
	if rf, ok := ret.Get(0).(func(*http.Request) (uint, error)); ok {
		return rf(req)
	}
	if rf, ok := ret.Get(0).(func(*http.Request) uint); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Get(0).(uint)
	}

	if rf, ok := ret.Get(1).(func(*http.Request) error); ok {
		r1 = rf(req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserTokenRepo_ValidateReq_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateReq'
type UserTokenRepo_ValidateReq_Call struct {
	*mock.Call
}

// ValidateReq is a helper method to define mock.On call
//   - req *http.Request
func (_e *UserTokenRepo_Expecter) ValidateReq(req interface{}) *UserTokenRepo_ValidateReq_Call {
	return &UserTokenRepo_ValidateReq_Call{Call: _e.mock.On("ValidateReq", req)}
}

func (_c *UserTokenRepo_ValidateReq_Call) Run(run func(req *http.Request)) *UserTokenRepo_ValidateReq_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*http.Request))
	})
	return _c
}

func (_c *UserTokenRepo_ValidateReq_Call) Return(_a0 uint, _a1 error) *UserTokenRepo_ValidateReq_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserTokenRepo_ValidateReq_Call) RunAndReturn(run func(*http.Request) (uint, error)) *UserTokenRepo_ValidateReq_Call {
	_c.Call.Return(run)
	return _c
}

// NewUserTokenRepo creates a new instance of UserTokenRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserTokenRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserTokenRepo {
	mock := &UserTokenRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
