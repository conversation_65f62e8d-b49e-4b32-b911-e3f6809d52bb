// Code generated by mockery. DO NOT EDIT.

package biz

import mock "github.com/stretchr/testify/mock"

// SafeRepo is an autogenerated mock type for the SafeRepo type
type SafeRepo struct {
	mock.Mock
}

type SafeRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *SafeRepo) EXPECT() *SafeRepo_Expecter {
	return &SafeRepo_Expecter{mock: &_m.Mock}
}

// GetPingStatus provides a mock function with no fields
func (_m *SafeRepo) GetPingStatus() (bool, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetPingStatus")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func() (bool, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SafeRepo_GetPingStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPingStatus'
type SafeRepo_GetPingStatus_Call struct {
	*mock.Call
}

// GetPingStatus is a helper method to define mock.On call
func (_e *SafeRepo_Expecter) GetPingStatus() *SafeRepo_GetPingStatus_Call {
	return &SafeRepo_GetPingStatus_Call{Call: _e.mock.On("GetPingStatus")}
}

func (_c *SafeRepo_GetPingStatus_Call) Run(run func()) *SafeRepo_GetPingStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SafeRepo_GetPingStatus_Call) Return(_a0 bool, _a1 error) *SafeRepo_GetPingStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *SafeRepo_GetPingStatus_Call) RunAndReturn(run func() (bool, error)) *SafeRepo_GetPingStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetSSH provides a mock function with no fields
func (_m *SafeRepo) GetSSH() (uint, bool, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetSSH")
	}

	var r0 uint
	var r1 bool
	var r2 error
	if rf, ok := ret.Get(0).(func() (uint, bool, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() uint); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(uint)
	}

	if rf, ok := ret.Get(1).(func() bool); ok {
		r1 = rf()
	} else {
		r1 = ret.Get(1).(bool)
	}

	if rf, ok := ret.Get(2).(func() error); ok {
		r2 = rf()
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// SafeRepo_GetSSH_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSSH'
type SafeRepo_GetSSH_Call struct {
	*mock.Call
}

// GetSSH is a helper method to define mock.On call
func (_e *SafeRepo_Expecter) GetSSH() *SafeRepo_GetSSH_Call {
	return &SafeRepo_GetSSH_Call{Call: _e.mock.On("GetSSH")}
}

func (_c *SafeRepo_GetSSH_Call) Run(run func()) *SafeRepo_GetSSH_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SafeRepo_GetSSH_Call) Return(_a0 uint, _a1 bool, _a2 error) *SafeRepo_GetSSH_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *SafeRepo_GetSSH_Call) RunAndReturn(run func() (uint, bool, error)) *SafeRepo_GetSSH_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePingStatus provides a mock function with given fields: status
func (_m *SafeRepo) UpdatePingStatus(status bool) error {
	ret := _m.Called(status)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePingStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(bool) error); ok {
		r0 = rf(status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SafeRepo_UpdatePingStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePingStatus'
type SafeRepo_UpdatePingStatus_Call struct {
	*mock.Call
}

// UpdatePingStatus is a helper method to define mock.On call
//   - status bool
func (_e *SafeRepo_Expecter) UpdatePingStatus(status interface{}) *SafeRepo_UpdatePingStatus_Call {
	return &SafeRepo_UpdatePingStatus_Call{Call: _e.mock.On("UpdatePingStatus", status)}
}

func (_c *SafeRepo_UpdatePingStatus_Call) Run(run func(status bool)) *SafeRepo_UpdatePingStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *SafeRepo_UpdatePingStatus_Call) Return(_a0 error) *SafeRepo_UpdatePingStatus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *SafeRepo_UpdatePingStatus_Call) RunAndReturn(run func(bool) error) *SafeRepo_UpdatePingStatus_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateSSH provides a mock function with given fields: port, status
func (_m *SafeRepo) UpdateSSH(port uint, status bool) error {
	ret := _m.Called(port, status)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSSH")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint, bool) error); ok {
		r0 = rf(port, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SafeRepo_UpdateSSH_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateSSH'
type SafeRepo_UpdateSSH_Call struct {
	*mock.Call
}

// UpdateSSH is a helper method to define mock.On call
//   - port uint
//   - status bool
func (_e *SafeRepo_Expecter) UpdateSSH(port interface{}, status interface{}) *SafeRepo_UpdateSSH_Call {
	return &SafeRepo_UpdateSSH_Call{Call: _e.mock.On("UpdateSSH", port, status)}
}

func (_c *SafeRepo_UpdateSSH_Call) Run(run func(port uint, status bool)) *SafeRepo_UpdateSSH_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(bool))
	})
	return _c
}

func (_c *SafeRepo_UpdateSSH_Call) Return(_a0 error) *SafeRepo_UpdateSSH_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *SafeRepo_UpdateSSH_Call) RunAndReturn(run func(uint, bool) error) *SafeRepo_UpdateSSH_Call {
	_c.Call.Return(run)
	return _c
}

// NewSafeRepo creates a new instance of SafeRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSafeRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *SafeRepo {
	mock := &SafeRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
