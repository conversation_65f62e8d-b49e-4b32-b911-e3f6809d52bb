// Code generated by mockery. DO NOT EDIT.

package biz

import (
	mock "github.com/stretchr/testify/mock"
	types "github.com/tnb-labs/panel/pkg/types"
)

// ContainerComposeRepo is an autogenerated mock type for the ContainerComposeRepo type
type ContainerComposeRepo struct {
	mock.Mock
}

type ContainerComposeRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *ContainerComposeRepo) EXPECT() *ContainerComposeRepo_Expecter {
	return &ContainerComposeRepo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: name, compose, envs
func (_m *ContainerComposeRepo) Create(name string, compose string, envs []types.KV) error {
	ret := _m.Called(name, compose, envs)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, []types.KV) error); ok {
		r0 = rf(name, compose, envs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerComposeRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type ContainerComposeRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - name string
//   - compose string
//   - envs []types.KV
func (_e *ContainerComposeRepo_Expecter) Create(name interface{}, compose interface{}, envs interface{}) *ContainerComposeRepo_Create_Call {
	return &ContainerComposeRepo_Create_Call{Call: _e.mock.On("Create", name, compose, envs)}
}

func (_c *ContainerComposeRepo_Create_Call) Run(run func(name string, compose string, envs []types.KV)) *ContainerComposeRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].([]types.KV))
	})
	return _c
}

func (_c *ContainerComposeRepo_Create_Call) Return(_a0 error) *ContainerComposeRepo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerComposeRepo_Create_Call) RunAndReturn(run func(string, string, []types.KV) error) *ContainerComposeRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Down provides a mock function with given fields: name
func (_m *ContainerComposeRepo) Down(name string) error {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for Down")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerComposeRepo_Down_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Down'
type ContainerComposeRepo_Down_Call struct {
	*mock.Call
}

// Down is a helper method to define mock.On call
//   - name string
func (_e *ContainerComposeRepo_Expecter) Down(name interface{}) *ContainerComposeRepo_Down_Call {
	return &ContainerComposeRepo_Down_Call{Call: _e.mock.On("Down", name)}
}

func (_c *ContainerComposeRepo_Down_Call) Run(run func(name string)) *ContainerComposeRepo_Down_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerComposeRepo_Down_Call) Return(_a0 error) *ContainerComposeRepo_Down_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerComposeRepo_Down_Call) RunAndReturn(run func(string) error) *ContainerComposeRepo_Down_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: name
func (_m *ContainerComposeRepo) Get(name string) (string, []types.KV, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 string
	var r1 []types.KV
	var r2 error
	if rf, ok := ret.Get(0).(func(string) (string, []types.KV, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) string); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string) []types.KV); ok {
		r1 = rf(name)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]types.KV)
		}
	}

	if rf, ok := ret.Get(2).(func(string) error); ok {
		r2 = rf(name)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ContainerComposeRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type ContainerComposeRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - name string
func (_e *ContainerComposeRepo_Expecter) Get(name interface{}) *ContainerComposeRepo_Get_Call {
	return &ContainerComposeRepo_Get_Call{Call: _e.mock.On("Get", name)}
}

func (_c *ContainerComposeRepo_Get_Call) Run(run func(name string)) *ContainerComposeRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerComposeRepo_Get_Call) Return(_a0 string, _a1 []types.KV, _a2 error) *ContainerComposeRepo_Get_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *ContainerComposeRepo_Get_Call) RunAndReturn(run func(string) (string, []types.KV, error)) *ContainerComposeRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with no fields
func (_m *ContainerComposeRepo) List() ([]types.ContainerCompose, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []types.ContainerCompose
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]types.ContainerCompose, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []types.ContainerCompose); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.ContainerCompose)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ContainerComposeRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type ContainerComposeRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
func (_e *ContainerComposeRepo_Expecter) List() *ContainerComposeRepo_List_Call {
	return &ContainerComposeRepo_List_Call{Call: _e.mock.On("List")}
}

func (_c *ContainerComposeRepo_List_Call) Run(run func()) *ContainerComposeRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ContainerComposeRepo_List_Call) Return(_a0 []types.ContainerCompose, _a1 error) *ContainerComposeRepo_List_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ContainerComposeRepo_List_Call) RunAndReturn(run func() ([]types.ContainerCompose, error)) *ContainerComposeRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// Remove provides a mock function with given fields: name
func (_m *ContainerComposeRepo) Remove(name string) error {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for Remove")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerComposeRepo_Remove_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Remove'
type ContainerComposeRepo_Remove_Call struct {
	*mock.Call
}

// Remove is a helper method to define mock.On call
//   - name string
func (_e *ContainerComposeRepo_Expecter) Remove(name interface{}) *ContainerComposeRepo_Remove_Call {
	return &ContainerComposeRepo_Remove_Call{Call: _e.mock.On("Remove", name)}
}

func (_c *ContainerComposeRepo_Remove_Call) Run(run func(name string)) *ContainerComposeRepo_Remove_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerComposeRepo_Remove_Call) Return(_a0 error) *ContainerComposeRepo_Remove_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerComposeRepo_Remove_Call) RunAndReturn(run func(string) error) *ContainerComposeRepo_Remove_Call {
	_c.Call.Return(run)
	return _c
}

// Up provides a mock function with given fields: name, force
func (_m *ContainerComposeRepo) Up(name string, force bool) error {
	ret := _m.Called(name, force)

	if len(ret) == 0 {
		panic("no return value specified for Up")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, bool) error); ok {
		r0 = rf(name, force)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerComposeRepo_Up_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Up'
type ContainerComposeRepo_Up_Call struct {
	*mock.Call
}

// Up is a helper method to define mock.On call
//   - name string
//   - force bool
func (_e *ContainerComposeRepo_Expecter) Up(name interface{}, force interface{}) *ContainerComposeRepo_Up_Call {
	return &ContainerComposeRepo_Up_Call{Call: _e.mock.On("Up", name, force)}
}

func (_c *ContainerComposeRepo_Up_Call) Run(run func(name string, force bool)) *ContainerComposeRepo_Up_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(bool))
	})
	return _c
}

func (_c *ContainerComposeRepo_Up_Call) Return(_a0 error) *ContainerComposeRepo_Up_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerComposeRepo_Up_Call) RunAndReturn(run func(string, bool) error) *ContainerComposeRepo_Up_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: name, compose, envs
func (_m *ContainerComposeRepo) Update(name string, compose string, envs []types.KV) error {
	ret := _m.Called(name, compose, envs)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, []types.KV) error); ok {
		r0 = rf(name, compose, envs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerComposeRepo_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type ContainerComposeRepo_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - name string
//   - compose string
//   - envs []types.KV
func (_e *ContainerComposeRepo_Expecter) Update(name interface{}, compose interface{}, envs interface{}) *ContainerComposeRepo_Update_Call {
	return &ContainerComposeRepo_Update_Call{Call: _e.mock.On("Update", name, compose, envs)}
}

func (_c *ContainerComposeRepo_Update_Call) Run(run func(name string, compose string, envs []types.KV)) *ContainerComposeRepo_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].([]types.KV))
	})
	return _c
}

func (_c *ContainerComposeRepo_Update_Call) Return(_a0 error) *ContainerComposeRepo_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerComposeRepo_Update_Call) RunAndReturn(run func(string, string, []types.KV) error) *ContainerComposeRepo_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewContainerComposeRepo creates a new instance of ContainerComposeRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewContainerComposeRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *ContainerComposeRepo {
	mock := &ContainerComposeRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
