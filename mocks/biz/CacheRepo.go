// Code generated by mockery. DO NOT EDIT.

package biz

import (
	mock "github.com/stretchr/testify/mock"
	biz "github.com/tnb-labs/panel/internal/biz"
)

// CacheRepo is an autogenerated mock type for the CacheRepo type
type CacheRepo struct {
	mock.Mock
}

type CacheRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *CacheRepo) EXPECT() *CacheRepo_Expecter {
	return &CacheRepo_Expecter{mock: &_m.Mock}
}

// Get provides a mock function with given fields: key, defaultValue
func (_m *CacheRepo) Get(key biz.CacheKey, defaultValue ...string) (string, error) {
	_va := make([]interface{}, len(defaultValue))
	for _i := range defaultValue {
		_va[_i] = defaultValue[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, key)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(biz.<PERSON>ache<PERSON>, ...string) (string, error)); ok {
		return rf(key, defaultValue...)
	}
	if rf, ok := ret.Get(0).(func(biz.CacheKey, ...string) string); ok {
		r0 = rf(key, defaultValue...)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(biz.CacheKey, ...string) error); ok {
		r1 = rf(key, defaultValue...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CacheRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type CacheRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - key biz.CacheKey
//   - defaultValue ...string
func (_e *CacheRepo_Expecter) Get(key interface{}, defaultValue ...interface{}) *CacheRepo_Get_Call {
	return &CacheRepo_Get_Call{Call: _e.mock.On("Get",
		append([]interface{}{key}, defaultValue...)...)}
}

func (_c *CacheRepo_Get_Call) Run(run func(key biz.CacheKey, defaultValue ...string)) *CacheRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]string, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(string)
			}
		}
		run(args[0].(biz.CacheKey), variadicArgs...)
	})
	return _c
}

func (_c *CacheRepo_Get_Call) Return(_a0 string, _a1 error) *CacheRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CacheRepo_Get_Call) RunAndReturn(run func(biz.CacheKey, ...string) (string, error)) *CacheRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// Set provides a mock function with given fields: key, value
func (_m *CacheRepo) Set(key biz.CacheKey, value string) error {
	ret := _m.Called(key, value)

	if len(ret) == 0 {
		panic("no return value specified for Set")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(biz.CacheKey, string) error); ok {
		r0 = rf(key, value)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CacheRepo_Set_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Set'
type CacheRepo_Set_Call struct {
	*mock.Call
}

// Set is a helper method to define mock.On call
//   - key biz.CacheKey
//   - value string
func (_e *CacheRepo_Expecter) Set(key interface{}, value interface{}) *CacheRepo_Set_Call {
	return &CacheRepo_Set_Call{Call: _e.mock.On("Set", key, value)}
}

func (_c *CacheRepo_Set_Call) Run(run func(key biz.CacheKey, value string)) *CacheRepo_Set_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(biz.CacheKey), args[1].(string))
	})
	return _c
}

func (_c *CacheRepo_Set_Call) Return(_a0 error) *CacheRepo_Set_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CacheRepo_Set_Call) RunAndReturn(run func(biz.CacheKey, string) error) *CacheRepo_Set_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateApps provides a mock function with no fields
func (_m *CacheRepo) UpdateApps() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for UpdateApps")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CacheRepo_UpdateApps_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateApps'
type CacheRepo_UpdateApps_Call struct {
	*mock.Call
}

// UpdateApps is a helper method to define mock.On call
func (_e *CacheRepo_Expecter) UpdateApps() *CacheRepo_UpdateApps_Call {
	return &CacheRepo_UpdateApps_Call{Call: _e.mock.On("UpdateApps")}
}

func (_c *CacheRepo_UpdateApps_Call) Run(run func()) *CacheRepo_UpdateApps_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *CacheRepo_UpdateApps_Call) Return(_a0 error) *CacheRepo_UpdateApps_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CacheRepo_UpdateApps_Call) RunAndReturn(run func() error) *CacheRepo_UpdateApps_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRewrites provides a mock function with no fields
func (_m *CacheRepo) UpdateRewrites() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for UpdateRewrites")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CacheRepo_UpdateRewrites_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRewrites'
type CacheRepo_UpdateRewrites_Call struct {
	*mock.Call
}

// UpdateRewrites is a helper method to define mock.On call
func (_e *CacheRepo_Expecter) UpdateRewrites() *CacheRepo_UpdateRewrites_Call {
	return &CacheRepo_UpdateRewrites_Call{Call: _e.mock.On("UpdateRewrites")}
}

func (_c *CacheRepo_UpdateRewrites_Call) Run(run func()) *CacheRepo_UpdateRewrites_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *CacheRepo_UpdateRewrites_Call) Return(_a0 error) *CacheRepo_UpdateRewrites_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CacheRepo_UpdateRewrites_Call) RunAndReturn(run func() error) *CacheRepo_UpdateRewrites_Call {
	_c.Call.Return(run)
	return _c
}

// NewCacheRepo creates a new instance of CacheRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCacheRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *CacheRepo {
	mock := &CacheRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
