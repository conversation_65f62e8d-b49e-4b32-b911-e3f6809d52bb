// Code generated by mockery. DO NOT EDIT.

package biz

import (
	mock "github.com/stretchr/testify/mock"
	biz "github.com/tnb-labs/panel/internal/biz"

	request "github.com/tnb-labs/panel/internal/http/request"
)

// DatabaseRepo is an autogenerated mock type for the DatabaseRepo type
type DatabaseRepo struct {
	mock.Mock
}

type DatabaseRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *DatabaseRepo) EXPECT() *DatabaseRepo_Expecter {
	return &DatabaseRepo_Expecter{mock: &_m.Mock}
}

// Comment provides a mock function with given fields: req
func (_m *DatabaseRepo) Comment(req *request.DatabaseComment) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Comment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.DatabaseComment) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseRepo_Comment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Comment'
type DatabaseRepo_Comment_Call struct {
	*mock.Call
}

// Comment is a helper method to define mock.On call
//   - req *request.DatabaseComment
func (_e *DatabaseRepo_Expecter) Comment(req interface{}) *DatabaseRepo_Comment_Call {
	return &DatabaseRepo_Comment_Call{Call: _e.mock.On("Comment", req)}
}

func (_c *DatabaseRepo_Comment_Call) Run(run func(req *request.DatabaseComment)) *DatabaseRepo_Comment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.DatabaseComment))
	})
	return _c
}

func (_c *DatabaseRepo_Comment_Call) Return(_a0 error) *DatabaseRepo_Comment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseRepo_Comment_Call) RunAndReturn(run func(*request.DatabaseComment) error) *DatabaseRepo_Comment_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: req
func (_m *DatabaseRepo) Create(req *request.DatabaseCreate) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.DatabaseCreate) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type DatabaseRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - req *request.DatabaseCreate
func (_e *DatabaseRepo_Expecter) Create(req interface{}) *DatabaseRepo_Create_Call {
	return &DatabaseRepo_Create_Call{Call: _e.mock.On("Create", req)}
}

func (_c *DatabaseRepo_Create_Call) Run(run func(req *request.DatabaseCreate)) *DatabaseRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.DatabaseCreate))
	})
	return _c
}

func (_c *DatabaseRepo_Create_Call) Return(_a0 error) *DatabaseRepo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseRepo_Create_Call) RunAndReturn(run func(*request.DatabaseCreate) error) *DatabaseRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: serverID, name
func (_m *DatabaseRepo) Delete(serverID uint, name string) error {
	ret := _m.Called(serverID, name)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint, string) error); ok {
		r0 = rf(serverID, name)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseRepo_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type DatabaseRepo_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - serverID uint
//   - name string
func (_e *DatabaseRepo_Expecter) Delete(serverID interface{}, name interface{}) *DatabaseRepo_Delete_Call {
	return &DatabaseRepo_Delete_Call{Call: _e.mock.On("Delete", serverID, name)}
}

func (_c *DatabaseRepo_Delete_Call) Run(run func(serverID uint, name string)) *DatabaseRepo_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(string))
	})
	return _c
}

func (_c *DatabaseRepo_Delete_Call) Return(_a0 error) *DatabaseRepo_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseRepo_Delete_Call) RunAndReturn(run func(uint, string) error) *DatabaseRepo_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: page, limit
func (_m *DatabaseRepo) List(page uint, limit uint) ([]*biz.Database, int64, error) {
	ret := _m.Called(page, limit)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*biz.Database
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(uint, uint) ([]*biz.Database, int64, error)); ok {
		return rf(page, limit)
	}
	if rf, ok := ret.Get(0).(func(uint, uint) []*biz.Database); ok {
		r0 = rf(page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*biz.Database)
		}
	}

	if rf, ok := ret.Get(1).(func(uint, uint) int64); ok {
		r1 = rf(page, limit)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(uint, uint) error); ok {
		r2 = rf(page, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// DatabaseRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type DatabaseRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - page uint
//   - limit uint
func (_e *DatabaseRepo_Expecter) List(page interface{}, limit interface{}) *DatabaseRepo_List_Call {
	return &DatabaseRepo_List_Call{Call: _e.mock.On("List", page, limit)}
}

func (_c *DatabaseRepo_List_Call) Run(run func(page uint, limit uint)) *DatabaseRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(uint))
	})
	return _c
}

func (_c *DatabaseRepo_List_Call) Return(_a0 []*biz.Database, _a1 int64, _a2 error) *DatabaseRepo_List_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *DatabaseRepo_List_Call) RunAndReturn(run func(uint, uint) ([]*biz.Database, int64, error)) *DatabaseRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// NewDatabaseRepo creates a new instance of DatabaseRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDatabaseRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *DatabaseRepo {
	mock := &DatabaseRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
