// Code generated by mockery. DO NOT EDIT.

package biz

import (
	mock "github.com/stretchr/testify/mock"
	biz "github.com/tnb-labs/panel/internal/biz"

	request "github.com/tnb-labs/panel/internal/http/request"
)

// DatabaseUserRepo is an autogenerated mock type for the DatabaseUserRepo type
type DatabaseUserRepo struct {
	mock.Mock
}

type DatabaseUserRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *DatabaseUserRepo) EXPECT() *DatabaseUserRepo_Expecter {
	return &DatabaseUserRepo_Expecter{mock: &_m.Mock}
}

// Count provides a mock function with no fields
func (_m *DatabaseUserRepo) Count() (int64, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func() (int64, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DatabaseUserRepo_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type DatabaseUserRepo_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
func (_e *DatabaseUserRepo_Expecter) Count() *DatabaseUserRepo_Count_Call {
	return &DatabaseUserRepo_Count_Call{Call: _e.mock.On("Count")}
}

func (_c *DatabaseUserRepo_Count_Call) Run(run func()) *DatabaseUserRepo_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *DatabaseUserRepo_Count_Call) Return(_a0 int64, _a1 error) *DatabaseUserRepo_Count_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DatabaseUserRepo_Count_Call) RunAndReturn(run func() (int64, error)) *DatabaseUserRepo_Count_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: req
func (_m *DatabaseUserRepo) Create(req *request.DatabaseUserCreate) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.DatabaseUserCreate) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseUserRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type DatabaseUserRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - req *request.DatabaseUserCreate
func (_e *DatabaseUserRepo_Expecter) Create(req interface{}) *DatabaseUserRepo_Create_Call {
	return &DatabaseUserRepo_Create_Call{Call: _e.mock.On("Create", req)}
}

func (_c *DatabaseUserRepo_Create_Call) Run(run func(req *request.DatabaseUserCreate)) *DatabaseUserRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.DatabaseUserCreate))
	})
	return _c
}

func (_c *DatabaseUserRepo_Create_Call) Return(_a0 error) *DatabaseUserRepo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseUserRepo_Create_Call) RunAndReturn(run func(*request.DatabaseUserCreate) error) *DatabaseUserRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: id
func (_m *DatabaseUserRepo) Delete(id uint) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseUserRepo_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type DatabaseUserRepo_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - id uint
func (_e *DatabaseUserRepo_Expecter) Delete(id interface{}) *DatabaseUserRepo_Delete_Call {
	return &DatabaseUserRepo_Delete_Call{Call: _e.mock.On("Delete", id)}
}

func (_c *DatabaseUserRepo_Delete_Call) Run(run func(id uint)) *DatabaseUserRepo_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *DatabaseUserRepo_Delete_Call) Return(_a0 error) *DatabaseUserRepo_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseUserRepo_Delete_Call) RunAndReturn(run func(uint) error) *DatabaseUserRepo_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByNames provides a mock function with given fields: serverID, names
func (_m *DatabaseUserRepo) DeleteByNames(serverID uint, names []string) error {
	ret := _m.Called(serverID, names)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByNames")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint, []string) error); ok {
		r0 = rf(serverID, names)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseUserRepo_DeleteByNames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByNames'
type DatabaseUserRepo_DeleteByNames_Call struct {
	*mock.Call
}

// DeleteByNames is a helper method to define mock.On call
//   - serverID uint
//   - names []string
func (_e *DatabaseUserRepo_Expecter) DeleteByNames(serverID interface{}, names interface{}) *DatabaseUserRepo_DeleteByNames_Call {
	return &DatabaseUserRepo_DeleteByNames_Call{Call: _e.mock.On("DeleteByNames", serverID, names)}
}

func (_c *DatabaseUserRepo_DeleteByNames_Call) Run(run func(serverID uint, names []string)) *DatabaseUserRepo_DeleteByNames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].([]string))
	})
	return _c
}

func (_c *DatabaseUserRepo_DeleteByNames_Call) Return(_a0 error) *DatabaseUserRepo_DeleteByNames_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseUserRepo_DeleteByNames_Call) RunAndReturn(run func(uint, []string) error) *DatabaseUserRepo_DeleteByNames_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: id
func (_m *DatabaseUserRepo) Get(id uint) (*biz.DatabaseUser, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *biz.DatabaseUser
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*biz.DatabaseUser, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) *biz.DatabaseUser); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.DatabaseUser)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DatabaseUserRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type DatabaseUserRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - id uint
func (_e *DatabaseUserRepo_Expecter) Get(id interface{}) *DatabaseUserRepo_Get_Call {
	return &DatabaseUserRepo_Get_Call{Call: _e.mock.On("Get", id)}
}

func (_c *DatabaseUserRepo_Get_Call) Run(run func(id uint)) *DatabaseUserRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *DatabaseUserRepo_Get_Call) Return(_a0 *biz.DatabaseUser, _a1 error) *DatabaseUserRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DatabaseUserRepo_Get_Call) RunAndReturn(run func(uint) (*biz.DatabaseUser, error)) *DatabaseUserRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: page, limit
func (_m *DatabaseUserRepo) List(page uint, limit uint) ([]*biz.DatabaseUser, int64, error) {
	ret := _m.Called(page, limit)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*biz.DatabaseUser
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(uint, uint) ([]*biz.DatabaseUser, int64, error)); ok {
		return rf(page, limit)
	}
	if rf, ok := ret.Get(0).(func(uint, uint) []*biz.DatabaseUser); ok {
		r0 = rf(page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*biz.DatabaseUser)
		}
	}

	if rf, ok := ret.Get(1).(func(uint, uint) int64); ok {
		r1 = rf(page, limit)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(uint, uint) error); ok {
		r2 = rf(page, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// DatabaseUserRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type DatabaseUserRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - page uint
//   - limit uint
func (_e *DatabaseUserRepo_Expecter) List(page interface{}, limit interface{}) *DatabaseUserRepo_List_Call {
	return &DatabaseUserRepo_List_Call{Call: _e.mock.On("List", page, limit)}
}

func (_c *DatabaseUserRepo_List_Call) Run(run func(page uint, limit uint)) *DatabaseUserRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(uint))
	})
	return _c
}

func (_c *DatabaseUserRepo_List_Call) Return(_a0 []*biz.DatabaseUser, _a1 int64, _a2 error) *DatabaseUserRepo_List_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *DatabaseUserRepo_List_Call) RunAndReturn(run func(uint, uint) ([]*biz.DatabaseUser, int64, error)) *DatabaseUserRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: req
func (_m *DatabaseUserRepo) Update(req *request.DatabaseUserUpdate) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.DatabaseUserUpdate) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseUserRepo_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type DatabaseUserRepo_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - req *request.DatabaseUserUpdate
func (_e *DatabaseUserRepo_Expecter) Update(req interface{}) *DatabaseUserRepo_Update_Call {
	return &DatabaseUserRepo_Update_Call{Call: _e.mock.On("Update", req)}
}

func (_c *DatabaseUserRepo_Update_Call) Run(run func(req *request.DatabaseUserUpdate)) *DatabaseUserRepo_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.DatabaseUserUpdate))
	})
	return _c
}

func (_c *DatabaseUserRepo_Update_Call) Return(_a0 error) *DatabaseUserRepo_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseUserRepo_Update_Call) RunAndReturn(run func(*request.DatabaseUserUpdate) error) *DatabaseUserRepo_Update_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRemark provides a mock function with given fields: req
func (_m *DatabaseUserRepo) UpdateRemark(req *request.DatabaseUserUpdateRemark) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRemark")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.DatabaseUserUpdateRemark) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseUserRepo_UpdateRemark_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRemark'
type DatabaseUserRepo_UpdateRemark_Call struct {
	*mock.Call
}

// UpdateRemark is a helper method to define mock.On call
//   - req *request.DatabaseUserUpdateRemark
func (_e *DatabaseUserRepo_Expecter) UpdateRemark(req interface{}) *DatabaseUserRepo_UpdateRemark_Call {
	return &DatabaseUserRepo_UpdateRemark_Call{Call: _e.mock.On("UpdateRemark", req)}
}

func (_c *DatabaseUserRepo_UpdateRemark_Call) Run(run func(req *request.DatabaseUserUpdateRemark)) *DatabaseUserRepo_UpdateRemark_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.DatabaseUserUpdateRemark))
	})
	return _c
}

func (_c *DatabaseUserRepo_UpdateRemark_Call) Return(_a0 error) *DatabaseUserRepo_UpdateRemark_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseUserRepo_UpdateRemark_Call) RunAndReturn(run func(*request.DatabaseUserUpdateRemark) error) *DatabaseUserRepo_UpdateRemark_Call {
	_c.Call.Return(run)
	return _c
}

// NewDatabaseUserRepo creates a new instance of DatabaseUserRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDatabaseUserRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *DatabaseUserRepo {
	mock := &DatabaseUserRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
