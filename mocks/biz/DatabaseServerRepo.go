// Code generated by mockery. DO NOT EDIT.

package biz

import (
	mock "github.com/stretchr/testify/mock"
	biz "github.com/tnb-labs/panel/internal/biz"

	request "github.com/tnb-labs/panel/internal/http/request"
)

// DatabaseServerRepo is an autogenerated mock type for the DatabaseServerRepo type
type DatabaseServerRepo struct {
	mock.Mock
}

type DatabaseServerRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *DatabaseServerRepo) EXPECT() *DatabaseServerRepo_Expecter {
	return &DatabaseServerRepo_Expecter{mock: &_m.Mock}
}

// ClearUsers provides a mock function with given fields: id
func (_m *DatabaseServerRepo) ClearUsers(id uint) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for ClearUsers")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseServerRepo_ClearUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClearUsers'
type DatabaseServerRepo_ClearUsers_Call struct {
	*mock.Call
}

// ClearUsers is a helper method to define mock.On call
//   - id uint
func (_e *DatabaseServerRepo_Expecter) ClearUsers(id interface{}) *DatabaseServerRepo_ClearUsers_Call {
	return &DatabaseServerRepo_ClearUsers_Call{Call: _e.mock.On("ClearUsers", id)}
}

func (_c *DatabaseServerRepo_ClearUsers_Call) Run(run func(id uint)) *DatabaseServerRepo_ClearUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *DatabaseServerRepo_ClearUsers_Call) Return(_a0 error) *DatabaseServerRepo_ClearUsers_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseServerRepo_ClearUsers_Call) RunAndReturn(run func(uint) error) *DatabaseServerRepo_ClearUsers_Call {
	_c.Call.Return(run)
	return _c
}

// Count provides a mock function with no fields
func (_m *DatabaseServerRepo) Count() (int64, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func() (int64, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DatabaseServerRepo_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type DatabaseServerRepo_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
func (_e *DatabaseServerRepo_Expecter) Count() *DatabaseServerRepo_Count_Call {
	return &DatabaseServerRepo_Count_Call{Call: _e.mock.On("Count")}
}

func (_c *DatabaseServerRepo_Count_Call) Run(run func()) *DatabaseServerRepo_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *DatabaseServerRepo_Count_Call) Return(_a0 int64, _a1 error) *DatabaseServerRepo_Count_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DatabaseServerRepo_Count_Call) RunAndReturn(run func() (int64, error)) *DatabaseServerRepo_Count_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: req
func (_m *DatabaseServerRepo) Create(req *request.DatabaseServerCreate) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.DatabaseServerCreate) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseServerRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type DatabaseServerRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - req *request.DatabaseServerCreate
func (_e *DatabaseServerRepo_Expecter) Create(req interface{}) *DatabaseServerRepo_Create_Call {
	return &DatabaseServerRepo_Create_Call{Call: _e.mock.On("Create", req)}
}

func (_c *DatabaseServerRepo_Create_Call) Run(run func(req *request.DatabaseServerCreate)) *DatabaseServerRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.DatabaseServerCreate))
	})
	return _c
}

func (_c *DatabaseServerRepo_Create_Call) Return(_a0 error) *DatabaseServerRepo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseServerRepo_Create_Call) RunAndReturn(run func(*request.DatabaseServerCreate) error) *DatabaseServerRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: id
func (_m *DatabaseServerRepo) Delete(id uint) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseServerRepo_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type DatabaseServerRepo_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - id uint
func (_e *DatabaseServerRepo_Expecter) Delete(id interface{}) *DatabaseServerRepo_Delete_Call {
	return &DatabaseServerRepo_Delete_Call{Call: _e.mock.On("Delete", id)}
}

func (_c *DatabaseServerRepo_Delete_Call) Run(run func(id uint)) *DatabaseServerRepo_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *DatabaseServerRepo_Delete_Call) Return(_a0 error) *DatabaseServerRepo_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseServerRepo_Delete_Call) RunAndReturn(run func(uint) error) *DatabaseServerRepo_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: id
func (_m *DatabaseServerRepo) Get(id uint) (*biz.DatabaseServer, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *biz.DatabaseServer
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*biz.DatabaseServer, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) *biz.DatabaseServer); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.DatabaseServer)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DatabaseServerRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type DatabaseServerRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - id uint
func (_e *DatabaseServerRepo_Expecter) Get(id interface{}) *DatabaseServerRepo_Get_Call {
	return &DatabaseServerRepo_Get_Call{Call: _e.mock.On("Get", id)}
}

func (_c *DatabaseServerRepo_Get_Call) Run(run func(id uint)) *DatabaseServerRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *DatabaseServerRepo_Get_Call) Return(_a0 *biz.DatabaseServer, _a1 error) *DatabaseServerRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DatabaseServerRepo_Get_Call) RunAndReturn(run func(uint) (*biz.DatabaseServer, error)) *DatabaseServerRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetByName provides a mock function with given fields: name
func (_m *DatabaseServerRepo) GetByName(name string) (*biz.DatabaseServer, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for GetByName")
	}

	var r0 *biz.DatabaseServer
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*biz.DatabaseServer, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) *biz.DatabaseServer); ok {
		r0 = rf(name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.DatabaseServer)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DatabaseServerRepo_GetByName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByName'
type DatabaseServerRepo_GetByName_Call struct {
	*mock.Call
}

// GetByName is a helper method to define mock.On call
//   - name string
func (_e *DatabaseServerRepo_Expecter) GetByName(name interface{}) *DatabaseServerRepo_GetByName_Call {
	return &DatabaseServerRepo_GetByName_Call{Call: _e.mock.On("GetByName", name)}
}

func (_c *DatabaseServerRepo_GetByName_Call) Run(run func(name string)) *DatabaseServerRepo_GetByName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *DatabaseServerRepo_GetByName_Call) Return(_a0 *biz.DatabaseServer, _a1 error) *DatabaseServerRepo_GetByName_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DatabaseServerRepo_GetByName_Call) RunAndReturn(run func(string) (*biz.DatabaseServer, error)) *DatabaseServerRepo_GetByName_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: page, limit
func (_m *DatabaseServerRepo) List(page uint, limit uint) ([]*biz.DatabaseServer, int64, error) {
	ret := _m.Called(page, limit)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*biz.DatabaseServer
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(uint, uint) ([]*biz.DatabaseServer, int64, error)); ok {
		return rf(page, limit)
	}
	if rf, ok := ret.Get(0).(func(uint, uint) []*biz.DatabaseServer); ok {
		r0 = rf(page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*biz.DatabaseServer)
		}
	}

	if rf, ok := ret.Get(1).(func(uint, uint) int64); ok {
		r1 = rf(page, limit)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(uint, uint) error); ok {
		r2 = rf(page, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// DatabaseServerRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type DatabaseServerRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - page uint
//   - limit uint
func (_e *DatabaseServerRepo_Expecter) List(page interface{}, limit interface{}) *DatabaseServerRepo_List_Call {
	return &DatabaseServerRepo_List_Call{Call: _e.mock.On("List", page, limit)}
}

func (_c *DatabaseServerRepo_List_Call) Run(run func(page uint, limit uint)) *DatabaseServerRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(uint))
	})
	return _c
}

func (_c *DatabaseServerRepo_List_Call) Return(_a0 []*biz.DatabaseServer, _a1 int64, _a2 error) *DatabaseServerRepo_List_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *DatabaseServerRepo_List_Call) RunAndReturn(run func(uint, uint) ([]*biz.DatabaseServer, int64, error)) *DatabaseServerRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// Sync provides a mock function with given fields: id
func (_m *DatabaseServerRepo) Sync(id uint) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Sync")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseServerRepo_Sync_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Sync'
type DatabaseServerRepo_Sync_Call struct {
	*mock.Call
}

// Sync is a helper method to define mock.On call
//   - id uint
func (_e *DatabaseServerRepo_Expecter) Sync(id interface{}) *DatabaseServerRepo_Sync_Call {
	return &DatabaseServerRepo_Sync_Call{Call: _e.mock.On("Sync", id)}
}

func (_c *DatabaseServerRepo_Sync_Call) Run(run func(id uint)) *DatabaseServerRepo_Sync_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *DatabaseServerRepo_Sync_Call) Return(_a0 error) *DatabaseServerRepo_Sync_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseServerRepo_Sync_Call) RunAndReturn(run func(uint) error) *DatabaseServerRepo_Sync_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: req
func (_m *DatabaseServerRepo) Update(req *request.DatabaseServerUpdate) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.DatabaseServerUpdate) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseServerRepo_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type DatabaseServerRepo_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - req *request.DatabaseServerUpdate
func (_e *DatabaseServerRepo_Expecter) Update(req interface{}) *DatabaseServerRepo_Update_Call {
	return &DatabaseServerRepo_Update_Call{Call: _e.mock.On("Update", req)}
}

func (_c *DatabaseServerRepo_Update_Call) Run(run func(req *request.DatabaseServerUpdate)) *DatabaseServerRepo_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.DatabaseServerUpdate))
	})
	return _c
}

func (_c *DatabaseServerRepo_Update_Call) Return(_a0 error) *DatabaseServerRepo_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseServerRepo_Update_Call) RunAndReturn(run func(*request.DatabaseServerUpdate) error) *DatabaseServerRepo_Update_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRemark provides a mock function with given fields: req
func (_m *DatabaseServerRepo) UpdateRemark(req *request.DatabaseServerUpdateRemark) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRemark")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.DatabaseServerUpdateRemark) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DatabaseServerRepo_UpdateRemark_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRemark'
type DatabaseServerRepo_UpdateRemark_Call struct {
	*mock.Call
}

// UpdateRemark is a helper method to define mock.On call
//   - req *request.DatabaseServerUpdateRemark
func (_e *DatabaseServerRepo_Expecter) UpdateRemark(req interface{}) *DatabaseServerRepo_UpdateRemark_Call {
	return &DatabaseServerRepo_UpdateRemark_Call{Call: _e.mock.On("UpdateRemark", req)}
}

func (_c *DatabaseServerRepo_UpdateRemark_Call) Run(run func(req *request.DatabaseServerUpdateRemark)) *DatabaseServerRepo_UpdateRemark_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.DatabaseServerUpdateRemark))
	})
	return _c
}

func (_c *DatabaseServerRepo_UpdateRemark_Call) Return(_a0 error) *DatabaseServerRepo_UpdateRemark_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DatabaseServerRepo_UpdateRemark_Call) RunAndReturn(run func(*request.DatabaseServerUpdateRemark) error) *DatabaseServerRepo_UpdateRemark_Call {
	_c.Call.Return(run)
	return _c
}

// NewDatabaseServerRepo creates a new instance of DatabaseServerRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDatabaseServerRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *DatabaseServerRepo {
	mock := &DatabaseServerRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
