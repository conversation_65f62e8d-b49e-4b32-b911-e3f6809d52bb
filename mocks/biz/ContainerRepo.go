// Code generated by mockery. DO NOT EDIT.

package biz

import (
	mock "github.com/stretchr/testify/mock"
	request "github.com/tnb-labs/panel/internal/http/request"
	types "github.com/tnb-labs/panel/pkg/types"
)

// ContainerRepo is an autogenerated mock type for the ContainerRepo type
type ContainerRepo struct {
	mock.Mock
}

type ContainerRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *ContainerRepo) EXPECT() *ContainerRepo_Expecter {
	return &ContainerRepo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: req
func (_m *ContainerRepo) Create(req *request.ContainerCreate) (string, error) {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(*request.ContainerCreate) (string, error)); ok {
		return rf(req)
	}
	if rf, ok := ret.Get(0).(func(*request.ContainerCreate) string); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(*request.ContainerCreate) error); ok {
		r1 = rf(req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ContainerRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type ContainerRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - req *request.ContainerCreate
func (_e *ContainerRepo_Expecter) Create(req interface{}) *ContainerRepo_Create_Call {
	return &ContainerRepo_Create_Call{Call: _e.mock.On("Create", req)}
}

func (_c *ContainerRepo_Create_Call) Run(run func(req *request.ContainerCreate)) *ContainerRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.ContainerCreate))
	})
	return _c
}

func (_c *ContainerRepo_Create_Call) Return(_a0 string, _a1 error) *ContainerRepo_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ContainerRepo_Create_Call) RunAndReturn(run func(*request.ContainerCreate) (string, error)) *ContainerRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Kill provides a mock function with given fields: id
func (_m *ContainerRepo) Kill(id string) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Kill")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerRepo_Kill_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Kill'
type ContainerRepo_Kill_Call struct {
	*mock.Call
}

// Kill is a helper method to define mock.On call
//   - id string
func (_e *ContainerRepo_Expecter) Kill(id interface{}) *ContainerRepo_Kill_Call {
	return &ContainerRepo_Kill_Call{Call: _e.mock.On("Kill", id)}
}

func (_c *ContainerRepo_Kill_Call) Run(run func(id string)) *ContainerRepo_Kill_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerRepo_Kill_Call) Return(_a0 error) *ContainerRepo_Kill_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerRepo_Kill_Call) RunAndReturn(run func(string) error) *ContainerRepo_Kill_Call {
	_c.Call.Return(run)
	return _c
}

// ListAll provides a mock function with no fields
func (_m *ContainerRepo) ListAll() ([]types.Container, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ListAll")
	}

	var r0 []types.Container
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]types.Container, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []types.Container); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.Container)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ContainerRepo_ListAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListAll'
type ContainerRepo_ListAll_Call struct {
	*mock.Call
}

// ListAll is a helper method to define mock.On call
func (_e *ContainerRepo_Expecter) ListAll() *ContainerRepo_ListAll_Call {
	return &ContainerRepo_ListAll_Call{Call: _e.mock.On("ListAll")}
}

func (_c *ContainerRepo_ListAll_Call) Run(run func()) *ContainerRepo_ListAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ContainerRepo_ListAll_Call) Return(_a0 []types.Container, _a1 error) *ContainerRepo_ListAll_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ContainerRepo_ListAll_Call) RunAndReturn(run func() ([]types.Container, error)) *ContainerRepo_ListAll_Call {
	_c.Call.Return(run)
	return _c
}

// ListByName provides a mock function with given fields: name
func (_m *ContainerRepo) ListByName(name string) ([]types.Container, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for ListByName")
	}

	var r0 []types.Container
	var r1 error
	if rf, ok := ret.Get(0).(func(string) ([]types.Container, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) []types.Container); ok {
		r0 = rf(name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.Container)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ContainerRepo_ListByName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByName'
type ContainerRepo_ListByName_Call struct {
	*mock.Call
}

// ListByName is a helper method to define mock.On call
//   - name string
func (_e *ContainerRepo_Expecter) ListByName(name interface{}) *ContainerRepo_ListByName_Call {
	return &ContainerRepo_ListByName_Call{Call: _e.mock.On("ListByName", name)}
}

func (_c *ContainerRepo_ListByName_Call) Run(run func(name string)) *ContainerRepo_ListByName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerRepo_ListByName_Call) Return(_a0 []types.Container, _a1 error) *ContainerRepo_ListByName_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ContainerRepo_ListByName_Call) RunAndReturn(run func(string) ([]types.Container, error)) *ContainerRepo_ListByName_Call {
	_c.Call.Return(run)
	return _c
}

// Logs provides a mock function with given fields: id
func (_m *ContainerRepo) Logs(id string) (string, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Logs")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (string, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(string) string); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ContainerRepo_Logs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Logs'
type ContainerRepo_Logs_Call struct {
	*mock.Call
}

// Logs is a helper method to define mock.On call
//   - id string
func (_e *ContainerRepo_Expecter) Logs(id interface{}) *ContainerRepo_Logs_Call {
	return &ContainerRepo_Logs_Call{Call: _e.mock.On("Logs", id)}
}

func (_c *ContainerRepo_Logs_Call) Run(run func(id string)) *ContainerRepo_Logs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerRepo_Logs_Call) Return(_a0 string, _a1 error) *ContainerRepo_Logs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ContainerRepo_Logs_Call) RunAndReturn(run func(string) (string, error)) *ContainerRepo_Logs_Call {
	_c.Call.Return(run)
	return _c
}

// Pause provides a mock function with given fields: id
func (_m *ContainerRepo) Pause(id string) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Pause")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerRepo_Pause_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Pause'
type ContainerRepo_Pause_Call struct {
	*mock.Call
}

// Pause is a helper method to define mock.On call
//   - id string
func (_e *ContainerRepo_Expecter) Pause(id interface{}) *ContainerRepo_Pause_Call {
	return &ContainerRepo_Pause_Call{Call: _e.mock.On("Pause", id)}
}

func (_c *ContainerRepo_Pause_Call) Run(run func(id string)) *ContainerRepo_Pause_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerRepo_Pause_Call) Return(_a0 error) *ContainerRepo_Pause_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerRepo_Pause_Call) RunAndReturn(run func(string) error) *ContainerRepo_Pause_Call {
	_c.Call.Return(run)
	return _c
}

// Prune provides a mock function with no fields
func (_m *ContainerRepo) Prune() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Prune")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerRepo_Prune_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Prune'
type ContainerRepo_Prune_Call struct {
	*mock.Call
}

// Prune is a helper method to define mock.On call
func (_e *ContainerRepo_Expecter) Prune() *ContainerRepo_Prune_Call {
	return &ContainerRepo_Prune_Call{Call: _e.mock.On("Prune")}
}

func (_c *ContainerRepo_Prune_Call) Run(run func()) *ContainerRepo_Prune_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ContainerRepo_Prune_Call) Return(_a0 error) *ContainerRepo_Prune_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerRepo_Prune_Call) RunAndReturn(run func() error) *ContainerRepo_Prune_Call {
	_c.Call.Return(run)
	return _c
}

// Remove provides a mock function with given fields: id
func (_m *ContainerRepo) Remove(id string) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Remove")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerRepo_Remove_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Remove'
type ContainerRepo_Remove_Call struct {
	*mock.Call
}

// Remove is a helper method to define mock.On call
//   - id string
func (_e *ContainerRepo_Expecter) Remove(id interface{}) *ContainerRepo_Remove_Call {
	return &ContainerRepo_Remove_Call{Call: _e.mock.On("Remove", id)}
}

func (_c *ContainerRepo_Remove_Call) Run(run func(id string)) *ContainerRepo_Remove_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerRepo_Remove_Call) Return(_a0 error) *ContainerRepo_Remove_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerRepo_Remove_Call) RunAndReturn(run func(string) error) *ContainerRepo_Remove_Call {
	_c.Call.Return(run)
	return _c
}

// Rename provides a mock function with given fields: id, newName
func (_m *ContainerRepo) Rename(id string, newName string) error {
	ret := _m.Called(id, newName)

	if len(ret) == 0 {
		panic("no return value specified for Rename")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(id, newName)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerRepo_Rename_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Rename'
type ContainerRepo_Rename_Call struct {
	*mock.Call
}

// Rename is a helper method to define mock.On call
//   - id string
//   - newName string
func (_e *ContainerRepo_Expecter) Rename(id interface{}, newName interface{}) *ContainerRepo_Rename_Call {
	return &ContainerRepo_Rename_Call{Call: _e.mock.On("Rename", id, newName)}
}

func (_c *ContainerRepo_Rename_Call) Run(run func(id string, newName string)) *ContainerRepo_Rename_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *ContainerRepo_Rename_Call) Return(_a0 error) *ContainerRepo_Rename_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerRepo_Rename_Call) RunAndReturn(run func(string, string) error) *ContainerRepo_Rename_Call {
	_c.Call.Return(run)
	return _c
}

// Restart provides a mock function with given fields: id
func (_m *ContainerRepo) Restart(id string) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Restart")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerRepo_Restart_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Restart'
type ContainerRepo_Restart_Call struct {
	*mock.Call
}

// Restart is a helper method to define mock.On call
//   - id string
func (_e *ContainerRepo_Expecter) Restart(id interface{}) *ContainerRepo_Restart_Call {
	return &ContainerRepo_Restart_Call{Call: _e.mock.On("Restart", id)}
}

func (_c *ContainerRepo_Restart_Call) Run(run func(id string)) *ContainerRepo_Restart_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerRepo_Restart_Call) Return(_a0 error) *ContainerRepo_Restart_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerRepo_Restart_Call) RunAndReturn(run func(string) error) *ContainerRepo_Restart_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: id
func (_m *ContainerRepo) Start(id string) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerRepo_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type ContainerRepo_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - id string
func (_e *ContainerRepo_Expecter) Start(id interface{}) *ContainerRepo_Start_Call {
	return &ContainerRepo_Start_Call{Call: _e.mock.On("Start", id)}
}

func (_c *ContainerRepo_Start_Call) Run(run func(id string)) *ContainerRepo_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerRepo_Start_Call) Return(_a0 error) *ContainerRepo_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerRepo_Start_Call) RunAndReturn(run func(string) error) *ContainerRepo_Start_Call {
	_c.Call.Return(run)
	return _c
}

// Stop provides a mock function with given fields: id
func (_m *ContainerRepo) Stop(id string) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Stop")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerRepo_Stop_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Stop'
type ContainerRepo_Stop_Call struct {
	*mock.Call
}

// Stop is a helper method to define mock.On call
//   - id string
func (_e *ContainerRepo_Expecter) Stop(id interface{}) *ContainerRepo_Stop_Call {
	return &ContainerRepo_Stop_Call{Call: _e.mock.On("Stop", id)}
}

func (_c *ContainerRepo_Stop_Call) Run(run func(id string)) *ContainerRepo_Stop_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerRepo_Stop_Call) Return(_a0 error) *ContainerRepo_Stop_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerRepo_Stop_Call) RunAndReturn(run func(string) error) *ContainerRepo_Stop_Call {
	_c.Call.Return(run)
	return _c
}

// Unpause provides a mock function with given fields: id
func (_m *ContainerRepo) Unpause(id string) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Unpause")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerRepo_Unpause_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Unpause'
type ContainerRepo_Unpause_Call struct {
	*mock.Call
}

// Unpause is a helper method to define mock.On call
//   - id string
func (_e *ContainerRepo_Expecter) Unpause(id interface{}) *ContainerRepo_Unpause_Call {
	return &ContainerRepo_Unpause_Call{Call: _e.mock.On("Unpause", id)}
}

func (_c *ContainerRepo_Unpause_Call) Run(run func(id string)) *ContainerRepo_Unpause_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerRepo_Unpause_Call) Return(_a0 error) *ContainerRepo_Unpause_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerRepo_Unpause_Call) RunAndReturn(run func(string) error) *ContainerRepo_Unpause_Call {
	_c.Call.Return(run)
	return _c
}

// NewContainerRepo creates a new instance of ContainerRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewContainerRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *ContainerRepo {
	mock := &ContainerRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
