// Code generated by mockery. DO NOT EDIT.

package biz

import (
	mock "github.com/stretchr/testify/mock"
	request "github.com/tnb-labs/panel/internal/http/request"
	types "github.com/tnb-labs/panel/pkg/types"
)

// ContainerVolumeRepo is an autogenerated mock type for the ContainerVolumeRepo type
type ContainerVolumeRepo struct {
	mock.Mock
}

type ContainerVolumeRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *ContainerVolumeRepo) EXPECT() *ContainerVolumeRepo_Expecter {
	return &ContainerVolumeRepo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: req
func (_m *ContainerVolumeRepo) Create(req *request.ContainerVolumeCreate) (string, error) {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(*request.ContainerVolumeCreate) (string, error)); ok {
		return rf(req)
	}
	if rf, ok := ret.Get(0).(func(*request.ContainerVolumeCreate) string); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(*request.ContainerVolumeCreate) error); ok {
		r1 = rf(req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ContainerVolumeRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type ContainerVolumeRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - req *request.ContainerVolumeCreate
func (_e *ContainerVolumeRepo_Expecter) Create(req interface{}) *ContainerVolumeRepo_Create_Call {
	return &ContainerVolumeRepo_Create_Call{Call: _e.mock.On("Create", req)}
}

func (_c *ContainerVolumeRepo_Create_Call) Run(run func(req *request.ContainerVolumeCreate)) *ContainerVolumeRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.ContainerVolumeCreate))
	})
	return _c
}

func (_c *ContainerVolumeRepo_Create_Call) Return(_a0 string, _a1 error) *ContainerVolumeRepo_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ContainerVolumeRepo_Create_Call) RunAndReturn(run func(*request.ContainerVolumeCreate) (string, error)) *ContainerVolumeRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with no fields
func (_m *ContainerVolumeRepo) List() ([]types.ContainerVolume, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []types.ContainerVolume
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]types.ContainerVolume, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []types.ContainerVolume); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.ContainerVolume)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ContainerVolumeRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type ContainerVolumeRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
func (_e *ContainerVolumeRepo_Expecter) List() *ContainerVolumeRepo_List_Call {
	return &ContainerVolumeRepo_List_Call{Call: _e.mock.On("List")}
}

func (_c *ContainerVolumeRepo_List_Call) Run(run func()) *ContainerVolumeRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ContainerVolumeRepo_List_Call) Return(_a0 []types.ContainerVolume, _a1 error) *ContainerVolumeRepo_List_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ContainerVolumeRepo_List_Call) RunAndReturn(run func() ([]types.ContainerVolume, error)) *ContainerVolumeRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// Prune provides a mock function with no fields
func (_m *ContainerVolumeRepo) Prune() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Prune")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerVolumeRepo_Prune_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Prune'
type ContainerVolumeRepo_Prune_Call struct {
	*mock.Call
}

// Prune is a helper method to define mock.On call
func (_e *ContainerVolumeRepo_Expecter) Prune() *ContainerVolumeRepo_Prune_Call {
	return &ContainerVolumeRepo_Prune_Call{Call: _e.mock.On("Prune")}
}

func (_c *ContainerVolumeRepo_Prune_Call) Run(run func()) *ContainerVolumeRepo_Prune_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ContainerVolumeRepo_Prune_Call) Return(_a0 error) *ContainerVolumeRepo_Prune_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerVolumeRepo_Prune_Call) RunAndReturn(run func() error) *ContainerVolumeRepo_Prune_Call {
	_c.Call.Return(run)
	return _c
}

// Remove provides a mock function with given fields: id
func (_m *ContainerVolumeRepo) Remove(id string) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Remove")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContainerVolumeRepo_Remove_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Remove'
type ContainerVolumeRepo_Remove_Call struct {
	*mock.Call
}

// Remove is a helper method to define mock.On call
//   - id string
func (_e *ContainerVolumeRepo_Expecter) Remove(id interface{}) *ContainerVolumeRepo_Remove_Call {
	return &ContainerVolumeRepo_Remove_Call{Call: _e.mock.On("Remove", id)}
}

func (_c *ContainerVolumeRepo_Remove_Call) Run(run func(id string)) *ContainerVolumeRepo_Remove_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ContainerVolumeRepo_Remove_Call) Return(_a0 error) *ContainerVolumeRepo_Remove_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContainerVolumeRepo_Remove_Call) RunAndReturn(run func(string) error) *ContainerVolumeRepo_Remove_Call {
	_c.Call.Return(run)
	return _c
}

// NewContainerVolumeRepo creates a new instance of ContainerVolumeRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewContainerVolumeRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *ContainerVolumeRepo {
	mock := &ContainerVolumeRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
