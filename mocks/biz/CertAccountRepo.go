// Code generated by mockery. DO NOT EDIT.

package biz

import (
	mock "github.com/stretchr/testify/mock"
	biz "github.com/tnb-labs/panel/internal/biz"

	request "github.com/tnb-labs/panel/internal/http/request"
)

// CertAccountRepo is an autogenerated mock type for the CertAccountRepo type
type CertAccountRepo struct {
	mock.Mock
}

type CertAccountRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *CertAccountRepo) EXPECT() *CertAccountRepo_Expecter {
	return &CertAccountRepo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: req
func (_m *CertAccountRepo) Create(req *request.CertAccountCreate) (*biz.CertAccount, error) {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *biz.CertAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(*request.CertAccountCreate) (*biz.CertAccount, error)); ok {
		return rf(req)
	}
	if rf, ok := ret.Get(0).(func(*request.CertAccountCreate) *biz.CertAccount); ok {
		r0 = rf(req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.CertAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(*request.CertAccountCreate) error); ok {
		r1 = rf(req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertAccountRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type CertAccountRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - req *request.CertAccountCreate
func (_e *CertAccountRepo_Expecter) Create(req interface{}) *CertAccountRepo_Create_Call {
	return &CertAccountRepo_Create_Call{Call: _e.mock.On("Create", req)}
}

func (_c *CertAccountRepo_Create_Call) Run(run func(req *request.CertAccountCreate)) *CertAccountRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.CertAccountCreate))
	})
	return _c
}

func (_c *CertAccountRepo_Create_Call) Return(_a0 *biz.CertAccount, _a1 error) *CertAccountRepo_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CertAccountRepo_Create_Call) RunAndReturn(run func(*request.CertAccountCreate) (*biz.CertAccount, error)) *CertAccountRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: id
func (_m *CertAccountRepo) Delete(id uint) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CertAccountRepo_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type CertAccountRepo_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - id uint
func (_e *CertAccountRepo_Expecter) Delete(id interface{}) *CertAccountRepo_Delete_Call {
	return &CertAccountRepo_Delete_Call{Call: _e.mock.On("Delete", id)}
}

func (_c *CertAccountRepo_Delete_Call) Run(run func(id uint)) *CertAccountRepo_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CertAccountRepo_Delete_Call) Return(_a0 error) *CertAccountRepo_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CertAccountRepo_Delete_Call) RunAndReturn(run func(uint) error) *CertAccountRepo_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: id
func (_m *CertAccountRepo) Get(id uint) (*biz.CertAccount, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *biz.CertAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*biz.CertAccount, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) *biz.CertAccount); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.CertAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertAccountRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type CertAccountRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - id uint
func (_e *CertAccountRepo_Expecter) Get(id interface{}) *CertAccountRepo_Get_Call {
	return &CertAccountRepo_Get_Call{Call: _e.mock.On("Get", id)}
}

func (_c *CertAccountRepo_Get_Call) Run(run func(id uint)) *CertAccountRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CertAccountRepo_Get_Call) Return(_a0 *biz.CertAccount, _a1 error) *CertAccountRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CertAccountRepo_Get_Call) RunAndReturn(run func(uint) (*biz.CertAccount, error)) *CertAccountRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetDefault provides a mock function with given fields: userID
func (_m *CertAccountRepo) GetDefault(userID uint) (*biz.CertAccount, error) {
	ret := _m.Called(userID)

	if len(ret) == 0 {
		panic("no return value specified for GetDefault")
	}

	var r0 *biz.CertAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*biz.CertAccount, error)); ok {
		return rf(userID)
	}
	if rf, ok := ret.Get(0).(func(uint) *biz.CertAccount); ok {
		r0 = rf(userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.CertAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertAccountRepo_GetDefault_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDefault'
type CertAccountRepo_GetDefault_Call struct {
	*mock.Call
}

// GetDefault is a helper method to define mock.On call
//   - userID uint
func (_e *CertAccountRepo_Expecter) GetDefault(userID interface{}) *CertAccountRepo_GetDefault_Call {
	return &CertAccountRepo_GetDefault_Call{Call: _e.mock.On("GetDefault", userID)}
}

func (_c *CertAccountRepo_GetDefault_Call) Run(run func(userID uint)) *CertAccountRepo_GetDefault_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CertAccountRepo_GetDefault_Call) Return(_a0 *biz.CertAccount, _a1 error) *CertAccountRepo_GetDefault_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CertAccountRepo_GetDefault_Call) RunAndReturn(run func(uint) (*biz.CertAccount, error)) *CertAccountRepo_GetDefault_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: page, limit
func (_m *CertAccountRepo) List(page uint, limit uint) ([]*biz.CertAccount, int64, error) {
	ret := _m.Called(page, limit)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*biz.CertAccount
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(uint, uint) ([]*biz.CertAccount, int64, error)); ok {
		return rf(page, limit)
	}
	if rf, ok := ret.Get(0).(func(uint, uint) []*biz.CertAccount); ok {
		r0 = rf(page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*biz.CertAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(uint, uint) int64); ok {
		r1 = rf(page, limit)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(uint, uint) error); ok {
		r2 = rf(page, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// CertAccountRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type CertAccountRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - page uint
//   - limit uint
func (_e *CertAccountRepo_Expecter) List(page interface{}, limit interface{}) *CertAccountRepo_List_Call {
	return &CertAccountRepo_List_Call{Call: _e.mock.On("List", page, limit)}
}

func (_c *CertAccountRepo_List_Call) Run(run func(page uint, limit uint)) *CertAccountRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(uint))
	})
	return _c
}

func (_c *CertAccountRepo_List_Call) Return(_a0 []*biz.CertAccount, _a1 int64, _a2 error) *CertAccountRepo_List_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *CertAccountRepo_List_Call) RunAndReturn(run func(uint, uint) ([]*biz.CertAccount, int64, error)) *CertAccountRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: req
func (_m *CertAccountRepo) Update(req *request.CertAccountUpdate) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.CertAccountUpdate) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CertAccountRepo_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type CertAccountRepo_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - req *request.CertAccountUpdate
func (_e *CertAccountRepo_Expecter) Update(req interface{}) *CertAccountRepo_Update_Call {
	return &CertAccountRepo_Update_Call{Call: _e.mock.On("Update", req)}
}

func (_c *CertAccountRepo_Update_Call) Run(run func(req *request.CertAccountUpdate)) *CertAccountRepo_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.CertAccountUpdate))
	})
	return _c
}

func (_c *CertAccountRepo_Update_Call) Return(_a0 error) *CertAccountRepo_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CertAccountRepo_Update_Call) RunAndReturn(run func(*request.CertAccountUpdate) error) *CertAccountRepo_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewCertAccountRepo creates a new instance of CertAccountRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCertAccountRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *CertAccountRepo {
	mock := &CertAccountRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
