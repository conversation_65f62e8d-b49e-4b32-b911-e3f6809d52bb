// Code generated by mockery. DO NOT EDIT.

package biz

import (
	mock "github.com/stretchr/testify/mock"
	biz "github.com/tnb-labs/panel/internal/biz"

	request "github.com/tnb-labs/panel/internal/http/request"
)

// CronRepo is an autogenerated mock type for the CronRepo type
type CronRepo struct {
	mock.Mock
}

type CronRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *CronRepo) EXPECT() *CronRepo_Expecter {
	return &CronRepo_Expecter{mock: &_m.Mock}
}

// Count provides a mock function with no fields
func (_m *CronRepo) Count() (int64, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func() (int64, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CronRepo_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type CronRepo_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
func (_e *CronRepo_Expecter) Count() *CronRepo_Count_Call {
	return &CronRepo_Count_Call{Call: _e.mock.On("Count")}
}

func (_c *CronRepo_Count_Call) Run(run func()) *CronRepo_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *CronRepo_Count_Call) Return(_a0 int64, _a1 error) *CronRepo_Count_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CronRepo_Count_Call) RunAndReturn(run func() (int64, error)) *CronRepo_Count_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: req
func (_m *CronRepo) Create(req *request.CronCreate) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.CronCreate) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CronRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type CronRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - req *request.CronCreate
func (_e *CronRepo_Expecter) Create(req interface{}) *CronRepo_Create_Call {
	return &CronRepo_Create_Call{Call: _e.mock.On("Create", req)}
}

func (_c *CronRepo_Create_Call) Run(run func(req *request.CronCreate)) *CronRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.CronCreate))
	})
	return _c
}

func (_c *CronRepo_Create_Call) Return(_a0 error) *CronRepo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CronRepo_Create_Call) RunAndReturn(run func(*request.CronCreate) error) *CronRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: id
func (_m *CronRepo) Delete(id uint) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CronRepo_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type CronRepo_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - id uint
func (_e *CronRepo_Expecter) Delete(id interface{}) *CronRepo_Delete_Call {
	return &CronRepo_Delete_Call{Call: _e.mock.On("Delete", id)}
}

func (_c *CronRepo_Delete_Call) Run(run func(id uint)) *CronRepo_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CronRepo_Delete_Call) Return(_a0 error) *CronRepo_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CronRepo_Delete_Call) RunAndReturn(run func(uint) error) *CronRepo_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: id
func (_m *CronRepo) Get(id uint) (*biz.Cron, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *biz.Cron
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*biz.Cron, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) *biz.Cron); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.Cron)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CronRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type CronRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - id uint
func (_e *CronRepo_Expecter) Get(id interface{}) *CronRepo_Get_Call {
	return &CronRepo_Get_Call{Call: _e.mock.On("Get", id)}
}

func (_c *CronRepo_Get_Call) Run(run func(id uint)) *CronRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *CronRepo_Get_Call) Return(_a0 *biz.Cron, _a1 error) *CronRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CronRepo_Get_Call) RunAndReturn(run func(uint) (*biz.Cron, error)) *CronRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: page, limit
func (_m *CronRepo) List(page uint, limit uint) ([]*biz.Cron, int64, error) {
	ret := _m.Called(page, limit)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*biz.Cron
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(uint, uint) ([]*biz.Cron, int64, error)); ok {
		return rf(page, limit)
	}
	if rf, ok := ret.Get(0).(func(uint, uint) []*biz.Cron); ok {
		r0 = rf(page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*biz.Cron)
		}
	}

	if rf, ok := ret.Get(1).(func(uint, uint) int64); ok {
		r1 = rf(page, limit)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(uint, uint) error); ok {
		r2 = rf(page, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// CronRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type CronRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - page uint
//   - limit uint
func (_e *CronRepo_Expecter) List(page interface{}, limit interface{}) *CronRepo_List_Call {
	return &CronRepo_List_Call{Call: _e.mock.On("List", page, limit)}
}

func (_c *CronRepo_List_Call) Run(run func(page uint, limit uint)) *CronRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(uint))
	})
	return _c
}

func (_c *CronRepo_List_Call) Return(_a0 []*biz.Cron, _a1 int64, _a2 error) *CronRepo_List_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *CronRepo_List_Call) RunAndReturn(run func(uint, uint) ([]*biz.Cron, int64, error)) *CronRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// Status provides a mock function with given fields: id, status
func (_m *CronRepo) Status(id uint, status bool) error {
	ret := _m.Called(id, status)

	if len(ret) == 0 {
		panic("no return value specified for Status")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint, bool) error); ok {
		r0 = rf(id, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CronRepo_Status_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Status'
type CronRepo_Status_Call struct {
	*mock.Call
}

// Status is a helper method to define mock.On call
//   - id uint
//   - status bool
func (_e *CronRepo_Expecter) Status(id interface{}, status interface{}) *CronRepo_Status_Call {
	return &CronRepo_Status_Call{Call: _e.mock.On("Status", id, status)}
}

func (_c *CronRepo_Status_Call) Run(run func(id uint, status bool)) *CronRepo_Status_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(bool))
	})
	return _c
}

func (_c *CronRepo_Status_Call) Return(_a0 error) *CronRepo_Status_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CronRepo_Status_Call) RunAndReturn(run func(uint, bool) error) *CronRepo_Status_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: req
func (_m *CronRepo) Update(req *request.CronUpdate) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.CronUpdate) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CronRepo_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type CronRepo_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - req *request.CronUpdate
func (_e *CronRepo_Expecter) Update(req interface{}) *CronRepo_Update_Call {
	return &CronRepo_Update_Call{Call: _e.mock.On("Update", req)}
}

func (_c *CronRepo_Update_Call) Run(run func(req *request.CronUpdate)) *CronRepo_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.CronUpdate))
	})
	return _c
}

func (_c *CronRepo_Update_Call) Return(_a0 error) *CronRepo_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CronRepo_Update_Call) RunAndReturn(run func(*request.CronUpdate) error) *CronRepo_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewCronRepo creates a new instance of CronRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCronRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *CronRepo {
	mock := &CronRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
