// Code generated by mockery. DO NOT EDIT.

package biz

import (
	mock "github.com/stretchr/testify/mock"
	biz "github.com/tnb-labs/panel/internal/biz"

	request "github.com/tnb-labs/panel/internal/http/request"
)

// SSHRepo is an autogenerated mock type for the SSHRepo type
type SSHRepo struct {
	mock.Mock
}

type SSHRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *SSHRepo) EXPECT() *SSHRepo_Expecter {
	return &SSHRepo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: req
func (_m *SSHRepo) Create(req *request.SSHCreate) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.SSHCreate) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SSHRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type SSHRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - req *request.SSHCreate
func (_e *SSHRepo_Expecter) Create(req interface{}) *SSHRepo_Create_Call {
	return &SSHRepo_Create_Call{Call: _e.mock.On("Create", req)}
}

func (_c *SSHRepo_Create_Call) Run(run func(req *request.SSHCreate)) *SSHRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.SSHCreate))
	})
	return _c
}

func (_c *SSHRepo_Create_Call) Return(_a0 error) *SSHRepo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *SSHRepo_Create_Call) RunAndReturn(run func(*request.SSHCreate) error) *SSHRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: id
func (_m *SSHRepo) Delete(id uint) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(uint) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SSHRepo_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type SSHRepo_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - id uint
func (_e *SSHRepo_Expecter) Delete(id interface{}) *SSHRepo_Delete_Call {
	return &SSHRepo_Delete_Call{Call: _e.mock.On("Delete", id)}
}

func (_c *SSHRepo_Delete_Call) Run(run func(id uint)) *SSHRepo_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *SSHRepo_Delete_Call) Return(_a0 error) *SSHRepo_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *SSHRepo_Delete_Call) RunAndReturn(run func(uint) error) *SSHRepo_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: id
func (_m *SSHRepo) Get(id uint) (*biz.SSH, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *biz.SSH
	var r1 error
	if rf, ok := ret.Get(0).(func(uint) (*biz.SSH, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(uint) *biz.SSH); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*biz.SSH)
		}
	}

	if rf, ok := ret.Get(1).(func(uint) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SSHRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type SSHRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - id uint
func (_e *SSHRepo_Expecter) Get(id interface{}) *SSHRepo_Get_Call {
	return &SSHRepo_Get_Call{Call: _e.mock.On("Get", id)}
}

func (_c *SSHRepo_Get_Call) Run(run func(id uint)) *SSHRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint))
	})
	return _c
}

func (_c *SSHRepo_Get_Call) Return(_a0 *biz.SSH, _a1 error) *SSHRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *SSHRepo_Get_Call) RunAndReturn(run func(uint) (*biz.SSH, error)) *SSHRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: page, limit
func (_m *SSHRepo) List(page uint, limit uint) ([]*biz.SSH, int64, error) {
	ret := _m.Called(page, limit)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*biz.SSH
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(uint, uint) ([]*biz.SSH, int64, error)); ok {
		return rf(page, limit)
	}
	if rf, ok := ret.Get(0).(func(uint, uint) []*biz.SSH); ok {
		r0 = rf(page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*biz.SSH)
		}
	}

	if rf, ok := ret.Get(1).(func(uint, uint) int64); ok {
		r1 = rf(page, limit)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(uint, uint) error); ok {
		r2 = rf(page, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// SSHRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type SSHRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - page uint
//   - limit uint
func (_e *SSHRepo_Expecter) List(page interface{}, limit interface{}) *SSHRepo_List_Call {
	return &SSHRepo_List_Call{Call: _e.mock.On("List", page, limit)}
}

func (_c *SSHRepo_List_Call) Run(run func(page uint, limit uint)) *SSHRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uint), args[1].(uint))
	})
	return _c
}

func (_c *SSHRepo_List_Call) Return(_a0 []*biz.SSH, _a1 int64, _a2 error) *SSHRepo_List_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *SSHRepo_List_Call) RunAndReturn(run func(uint, uint) ([]*biz.SSH, int64, error)) *SSHRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: req
func (_m *SSHRepo) Update(req *request.SSHUpdate) error {
	ret := _m.Called(req)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*request.SSHUpdate) error); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SSHRepo_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type SSHRepo_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - req *request.SSHUpdate
func (_e *SSHRepo_Expecter) Update(req interface{}) *SSHRepo_Update_Call {
	return &SSHRepo_Update_Call{Call: _e.mock.On("Update", req)}
}

func (_c *SSHRepo_Update_Call) Run(run func(req *request.SSHUpdate)) *SSHRepo_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*request.SSHUpdate))
	})
	return _c
}

func (_c *SSHRepo_Update_Call) Return(_a0 error) *SSHRepo_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *SSHRepo_Update_Call) RunAndReturn(run func(*request.SSHUpdate) error) *SSHRepo_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewSSHRepo creates a new instance of SSHRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSSHRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *SSHRepo {
	mock := &SSHRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
