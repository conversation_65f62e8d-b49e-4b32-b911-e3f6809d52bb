## 安全说明

**我们不对面板的安全性做任何保证**，但安全性是我们最关心的问题之一，我们已在多个不同应用的生产环境广泛应用耗子面板，至今无任何安全事故。

如果您在面板中发现任何安全问题，请勿提交 Issue，可通过以下方式直接联系我们：

- 邮箱：[<EMAIL>](mailto:<EMAIL>)
- QQ：826896000

提前感谢您的支持与帮助！

致某些安全初学者：通过面板 `session` / `access_token` 执行的任何操作（包括且不限于：获取 root 权限、读取/写入系统敏感文件、执行任意 shell 命令等）均不被认为是安全问题，请不要刷此类报告浪费彼此的时间，这类低水平的报告对你的简历也没有任何帮助。

## Security Policy

**We do not make any guarantees about the security of the panel**, but security is one of our top concerns. We have widely used the Rat panel in the production environment of multiple different applications, and there has been no security incidents to date.

If you find any security issues while using the panel, please do not submit an Issue. You can contact us directly through the following methods:

- Email: [<EMAIL>](mailto:<EMAIL>)
- Telegram: @devhaozi

Thank you in advance for your support and help!

To some security beginners: Any operation performed through the panel `session` / `access_token` (including but not limited to: obtaining root permissions, reading/writing sensitive system files, executing arbitrary shell commands, etc.) is not considered a security issue. Please do not waste each other's time by submitting such reports. Such low-level reports are of no help to your resume.
