package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// Config 应用配置
type Config struct {
	Server ServerConfig `json:"server"`
	Data   DataConfig   `json:"data"`
	Log    LogConfig    `json:"log"`
	JWT    JWTConfig    `json:"jwt"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Address string `json:"address"`
	Port    int    `json:"port"`
}

// DataConfig 数据配置
type DataConfig struct {
	Path string `json:"path"`
}

// LogConfig 日志配置
type LogConfig struct {
	Path  string `json:"path"`
	Level string `json:"level"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret    string `json:"secret"`
	ExpiresIn int    `json:"expires_in"` // 过期时间（小时）
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		homeDir = "."
	}

	dataPath := filepath.Join(homeDir, ".panel")
	logPath := filepath.Join(dataPath, "logs")

	return &Config{
		Server: ServerConfig{
			Address: "0.0.0.0",
			Port:    8080,
		},
		Data: DataConfig{
			Path: dataPath,
		},
		Log: LogConfig{
			Path:  logPath,
			Level: "info",
		},
		JWT: JWTConfig{
			Secret:    "panel_secret_key",
			ExpiresIn: 24, // 24小时
		},
	}
}

// Load 加载配置
func Load() *Config {
	cfg := DefaultConfig()

	// 确保配置目录存在
	if err := os.MkdirAll(cfg.Data.Path, 0755); err != nil {
		fmt.Printf("Failed to create data directory: %v\n", err)
		return cfg
	}

	// 确保日志目录存在
	if err := os.MkdirAll(cfg.Log.Path, 0755); err != nil {
		fmt.Printf("Failed to create log directory: %v\n", err)
		return cfg
	}

	// 配置文件路径
	configPath := filepath.Join(cfg.Data.Path, "config.json")

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// 配置文件不存在，创建默认配置
		data, err := json.MarshalIndent(cfg, "", "  ")
		if err != nil {
			fmt.Printf("Failed to marshal config: %v\n", err)
			return cfg
		}

		if err := os.WriteFile(configPath, data, 0644); err != nil {
			fmt.Printf("Failed to write config file: %v\n", err)
			return cfg
		}

		fmt.Printf("Created default config at %s\n", configPath)
		return cfg
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		fmt.Printf("Failed to read config file: %v\n", err)
		return cfg
	}

	// 解析配置
	if err := json.Unmarshal(data, cfg); err != nil {
		fmt.Printf("Failed to parse config file: %v\n", err)
		return cfg
	}

	return cfg
}
