package router

import (
	"github.com/cloudwego/hertz/pkg/app/server"
	"gorm.io/gorm"

	"panel/server/biz/handler"
	"panel/server/config"
	"panel/server/infra/middleware"
)

// Register 注册路由
func Register(h *server.Hertz, cfg *config.Config, db *gorm.DB) {
	// 全局中间件
	h.Use(middleware.Cors())
	h.Use(middleware.Logger())

	// API路由组
	api := h.Group("/api")

	// 认证路由
	auth := api.Group("/auth")
	{
		auth.POST("/login", handler.NewAuthHandler(db, cfg).Login)
		auth.POST("/logout", middleware.Auth(cfg), handler.NewAuthHandler(db, cfg).Logout)
		auth.GET("/profile", middleware.Auth(cfg), handler.NewAuthHandler(db, cfg).GetProfile)
	}

	// 系统路由
	system := api.Group("/system", middleware.Auth(cfg))
	{
		system.GET("/info", handler.NewSystemHandler(db).GetInfo)
		system.GET("/status", handler.NewSystemHandler(db).GetStatus)
		system.POST("/realtime", handler.NewSystemHandler(db).GetRealtime)
		system.GET("/count", handler.NewSystemHandler(db).GetCountInfo)
		system.GET("/service", handler.NewSystemHandler(db).ListServices)
		system.GET("/service/:name", handler.NewSystemHandler(db).GetService)
		system.POST("/service/:name/start", handler.NewSystemHandler(db).StartService)
		system.POST("/service/:name/stop", handler.NewSystemHandler(db).StopService)
		system.POST("/service/:name/restart", handler.NewSystemHandler(db).RestartService)
	}

	// 网站路由
	// website := api.Group("/website", middleware.Auth(cfg)) // Commented out as it's unused
	// {
	// TODO: 实现网站处理器
	// website.GET("", handler.ListWebsites)
	// website.POST("", handler.CreateWebsite)
	// website.GET("/:id", handler.GetWebsite)
	// website.PUT("/:id", handler.UpdateWebsite)
	// website.DELETE("/:id", handler.DeleteWebsite)
	// }

	// 数据库路由
	// database := api.Group("/database", middleware.Auth(cfg)) // Commented out as it's unused
	// {
	// TODO: 实现数据库处理器
	// database.GET("", handler.ListDatabases)
	// database.POST("", handler.CreateDatabase)
	// database.GET("/:id", handler.GetDatabase)
	// database.PUT("/:id", handler.UpdateDatabase)
	// database.DELETE("/:id", handler.DeleteDatabase)
	// }

	// 文件路由
	// file := api.Group("/file", middleware.Auth(cfg)) // Commented out as it's unused
	// {
	// TODO: 实现文件处理器
	// file.GET("/list", handler.ListFiles)
	// file.GET("/download", handler.DownloadFile)
	// file.POST("/upload", handler.UploadFile)
	// file.POST("/mkdir", handler.CreateDirectory)
	// file.DELETE("", handler.DeleteFile)
	// file.PUT("/rename", handler.RenameFile)
	// file.PUT("/chmod", handler.ChmodFile)
	// }

	// 定时任务路由
	// cron := api.Group("/cron", middleware.Auth(cfg)) // Commented out as it's unused
	// {
	// TODO: 实现定时任务处理器
	// cron.GET("", handler.ListCronJobs)
	// cron.POST("", handler.CreateCronJob)
	// cron.GET("/:id", handler.GetCronJob)
	// cron.PUT("/:id", handler.UpdateCronJob)
	// cron.DELETE("/:id", handler.DeleteCronJob)
	// }

	// 容器路由
	container := api.Group("/container", middleware.Auth(cfg))
	{
		container.GET("", handler.NewContainerHandler(db).List)
		container.POST("", handler.NewContainerHandler(db).Create)
		container.POST("/start", handler.NewContainerHandler(db).Start)
		container.POST("/stop", handler.NewContainerHandler(db).Stop)
		container.POST("/restart", handler.NewContainerHandler(db).Restart)
		container.DELETE("", handler.NewContainerHandler(db).Remove)
		container.GET("/:id/logs", handler.NewContainerHandler(db).Logs)
		container.GET("/:id/stats", handler.NewContainerHandler(db).Stats)

		// 镜像管理
		image := container.Group("/image")
		{
			image.GET("", handler.NewContainerHandler(db).ListImages)
			image.POST("/pull", handler.NewContainerHandler(db).PullImage)
			image.DELETE("", handler.NewContainerHandler(db).RemoveImage)
		}

		// 网络管理
		network := container.Group("/network")
		{
			network.GET("", handler.NewContainerHandler(db).ListNetworks)
			network.POST("", handler.NewContainerHandler(db).CreateNetwork)
			network.DELETE("", handler.NewContainerHandler(db).RemoveNetwork)
		}

		// 数据卷管理
		volume := container.Group("/volume")
		{
			volume.GET("", handler.NewContainerHandler(db).ListVolumes)
			volume.POST("", handler.NewContainerHandler(db).CreateVolume)
			volume.DELETE("", handler.NewContainerHandler(db).RemoveVolume)
		}
	}

	// WebSocket路由
	// ws := h.Group("/ws") // Commented out as it's unused
	// {
	// TODO: 实现WebSocket处理器
	// ws.GET("/terminal", middleware.AuthWS(cfg), handler.Terminal)
	// ws.GET("/stats", middleware.AuthWS(cfg), handler.Stats)
	// }
}
