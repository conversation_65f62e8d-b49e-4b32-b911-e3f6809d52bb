package shell

import (
	"bytes"
	"fmt"
	"os/exec"
	"strings"
)

// Exec 执行Shell命令
func Exec(command string) (string, error) {
	cmd := exec.Command("bash", "-c", command)
	
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr
	
	err := cmd.Run()
	if err != nil {
		return "", fmt.Errorf("command failed: %s, error: %w, stderr: %s", command, err, stderr.String())
	}
	
	return strings.TrimSpace(stdout.String()), nil
}

// Execf 格式化执行Shell命令
func Execf(format string, args ...interface{}) (string, error) {
	return Exec(fmt.Sprintf(format, args...))
}

// ExecWithOutput 执行Shell命令并返回标准输出和标准错误
func ExecWithOutput(command string) (string, string, error) {
	cmd := exec.Command("bash", "-c", command)
	
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr
	
	err := cmd.Run()
	
	return strings.TrimSpace(stdout.String()), strings.TrimSpace(stderr.String()), err
}
