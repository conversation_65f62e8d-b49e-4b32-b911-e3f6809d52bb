package utils

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"panel/server/pkg/constant"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// Success 成功响应
func Success(ctx context.Context, c *app.RequestContext, data interface{}) {
	c.JSON(200, Response{
		Code:    constant.CodeSuccess,
		Message: constant.CodeMessages[constant.CodeSuccess],
		Data:    data,
	})
}

// Error 错误响应
func Error(ctx context.Context, c *app.RequestContext, code int, msg ...string) {
	message := constant.CodeMessages[code]
	if len(msg) > 0 && msg[0] != "" {
		message = msg[0]
	}

	c.JSON(200, Response{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}
