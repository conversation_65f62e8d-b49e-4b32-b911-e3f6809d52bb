package constant

// 错误码定义
const (
	// 成功
	CodeSuccess = 0

	// 通用错误 (1000-1999)
	CodeUnknownError   = 1000
	CodeParamError     = 1001
	CodeNotFound       = 1002
	CodeAlreadyExists  = 1003

	// 认证错误 (1100-1199)
	CodeAuthFailed     = 1100
	CodeTokenExpired   = 1101
	CodePermissionDenied = 1102

	// 系统错误 (2000-2999)
	CodeSystemError    = 2000
	CodeDatabaseError  = 2001
	CodeFileSystemError = 2002

	// 网站错误 (3000-3999)
	CodeWebsiteError   = 3000
	CodeWebsiteConfigError = 3001

	// 数据库服务错误 (4000-4999)
	CodeDatabaseServerError = 4000
	CodeDatabaseConnectError = 4001

	// 服务管理错误 (5000-5999)
	CodeServiceError   = 5000
)

// 响应消息
var CodeMessages = map[int]string{
	CodeSuccess:        "成功",
	CodeUnknownError:   "未知错误",
	CodeParamError:     "参数错误",
	CodeNotFound:       "资源不存在",
	CodeAlreadyExists:  "资源已存在",
	CodeAuthFailed:     "认证失败",
	CodeTokenExpired:   "令牌过期",
	CodePermissionDenied: "权限不足",
	CodeSystemError:    "系统错误",
	CodeDatabaseError:  "数据库错误",
	CodeFileSystemError: "文件系统错误",
	CodeWebsiteError:   "网站错误",
	CodeWebsiteConfigError: "网站配置错误",
	CodeDatabaseServerError: "数据库服务错误",
	CodeDatabaseConnectError: "数据库连接错误",
	CodeServiceError:   "服务管理错误",
}
