package systemctl

import (
	"fmt"
	"runtime"
	"strings"

	"panel/server/pkg/shell"
)

// Service 服务信息
type Service struct {
	Name        string `json:"name"`
	LoadState   string `json:"load_state"`
	ActiveState string `json:"active_state"`
	SubState    string `json:"sub_state"`
	Description string `json:"description"`
}

// ListServices 获取服务列表
func ListServices(filter string) ([]Service, error) {
	osType := runtime.GOOS

	switch osType {
	case "linux":
		return listLinuxServices(filter)
	case "darwin":
		return listMacOSServices(filter)
	default:
		return getMockServices(), nil
	}
}

// listLinuxServices 获取Linux服务列表
func listLinuxServices(filter string) ([]Service, error) {
	cmd := "systemctl list-units --type=service --all --no-legend"
	if filter != "" {
		cmd += " | grep " + filter
	}

	output, err := shell.Exec(cmd)
	if err != nil {
		return nil, err
	}

	var services []Service
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if line == "" {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) < 5 {
			continue
		}

		name := strings.TrimSuffix(fields[0], ".service")
		description := strings.Join(fields[4:], " ")

		services = append(services, Service{
			Name:        name,
			LoadState:   fields[1],
			ActiveState: fields[2],
			SubState:    fields[3],
			Description: description,
		})
	}

	return services, nil
}

// listMacOSServices 获取macOS服务列表
func listMacOSServices(filter string) ([]Service, error) {
	// 在macOS上，我们可以使用launchctl，但为了简化，返回一些常见的系统服务
	services := getMockServices()

	if filter != "" {
		var filtered []Service
		for _, service := range services {
			if strings.Contains(service.Name, filter) || strings.Contains(service.Description, filter) {
				filtered = append(filtered, service)
			}
		}
		return filtered, nil
	}

	return services, nil
}

// getMockServices 获取模拟服务列表
func getMockServices() []Service {
	return []Service{
		{
			Name:        "nginx",
			LoadState:   "loaded",
			ActiveState: "active",
			SubState:    "running",
			Description: "The nginx HTTP and reverse proxy server",
		},
		{
			Name:        "mysql",
			LoadState:   "loaded",
			ActiveState: "active",
			SubState:    "running",
			Description: "MySQL Community Server",
		},
		{
			Name:        "redis",
			LoadState:   "loaded",
			ActiveState: "inactive",
			SubState:    "dead",
			Description: "Advanced key-value store",
		},
		{
			Name:        "docker",
			LoadState:   "loaded",
			ActiveState: "active",
			SubState:    "running",
			Description: "Docker Application Container Engine",
		},
		{
			Name:        "ssh",
			LoadState:   "loaded",
			ActiveState: "active",
			SubState:    "running",
			Description: "OpenBSD Secure Shell server",
		},
	}
}

// GetService 获取服务信息
func GetService(name string) (*Service, error) {
	osType := runtime.GOOS

	switch osType {
	case "linux":
		return getLinuxService(name)
	case "darwin":
		return getMacOSService(name)
	default:
		return getMockService(name)
	}
}

// getLinuxService 获取Linux服务信息
func getLinuxService(name string) (*Service, error) {
	cmd := fmt.Sprintf("systemctl list-units --type=service --all --no-legend | grep %s.service", name)
	output, err := shell.Exec(cmd)
	if err != nil {
		return nil, fmt.Errorf("service not found: %s", name)
	}

	fields := strings.Fields(output)
	if len(fields) < 5 {
		return nil, fmt.Errorf("invalid service output: %s", output)
	}

	description := strings.Join(fields[4:], " ")

	return &Service{
		Name:        strings.TrimSuffix(fields[0], ".service"),
		LoadState:   fields[1],
		ActiveState: fields[2],
		SubState:    fields[3],
		Description: description,
	}, nil
}

// getMacOSService 获取macOS服务信息
func getMacOSService(name string) (*Service, error) {
	services := getMockServices()
	for _, service := range services {
		if service.Name == name {
			return &service, nil
		}
	}
	return nil, fmt.Errorf("service not found: %s", name)
}

// getMockService 获取模拟服务信息
func getMockService(name string) (*Service, error) {
	services := getMockServices()
	for _, service := range services {
		if service.Name == name {
			return &service, nil
		}
	}
	return nil, fmt.Errorf("service not found: %s", name)
}

// StartService 启动服务
func StartService(name string) error {
	osType := runtime.GOOS

	switch osType {
	case "linux":
		_, err := shell.Exec(fmt.Sprintf("systemctl start %s", name))
		return err
	case "darwin":
		// 在macOS上，我们模拟服务启动
		return nil
	default:
		return nil
	}
}

// StopService 停止服务
func StopService(name string) error {
	osType := runtime.GOOS

	switch osType {
	case "linux":
		_, err := shell.Exec(fmt.Sprintf("systemctl stop %s", name))
		return err
	case "darwin":
		// 在macOS上，我们模拟服务停止
		return nil
	default:
		return nil
	}
}

// RestartService 重启服务
func RestartService(name string) error {
	osType := runtime.GOOS

	switch osType {
	case "linux":
		_, err := shell.Exec(fmt.Sprintf("systemctl restart %s", name))
		return err
	case "darwin":
		// 在macOS上，我们模拟服务重启
		return nil
	default:
		return nil
	}
}

// EnableService 启用服务
func EnableService(name string) error {
	_, err := shell.Exec(fmt.Sprintf("systemctl enable %s", name))
	return err
}

// DisableService 禁用服务
func DisableService(name string) error {
	_, err := shell.Exec(fmt.Sprintf("systemctl disable %s", name))
	return err
}

// GetServiceStatus 获取服务状态
func GetServiceStatus(name string) (string, error) {
	output, err := shell.Exec(fmt.Sprintf("systemctl is-active %s", name))
	if err != nil {
		return "inactive", nil
	}
	return output, nil
}
