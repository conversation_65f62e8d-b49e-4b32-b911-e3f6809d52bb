package docker

import (
	"encoding/json"
	"fmt"
	"log" // Added for logging
	"strings"

	"panel/server/pkg/shell"
)

// Network 网络信息
type Network struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Driver  string `json:"driver"`
	Scope   string `json:"scope"`
	Subnet  string `json:"subnet"`
	Gateway string `json:"gateway"`
}

// DockerNetworkLSFormat is a temporary struct to parse the JSON output from 'docker network ls --format'
type DockerNetworkLSFormat struct {
	ID        string `json:"ID"`
	Name      string `json:"Name"`
	Driver    string `json:"Driver"`
	Scope     string `json:"Scope"`
	CreatedAt string `json:"CreatedAt"` // Raw string, not directly used in final Network struct
	IPv4      string `json:"IPv4"`      // Raw string, not directly used
	IPv6      string `json:"IPv6"`      // Raw string, not directly used
	Internal  string `json:"Internal"`  // Raw string, not directly used
	Labels    string `json:"Labels"`    // Raw string, not directly used
}

// ListNetworks 获取网络列表
func ListNetworks() ([]Network, error) {
	cmd := "docker network ls --format '{{json .}}'"

	output, err := shell.Exec(cmd)
	if err != nil {
		log.Printf("Error executing docker network ls: %v, output: %s", err, output)
		return nil, fmt.Errorf("failed to execute docker network ls: %w. Output: %s", err, output)
	}

	var networks []Network
	lines := strings.Split(strings.TrimSpace(output), "\n")
	if len(lines) == 1 && lines[0] == "" { // Handle case where no networks are present
		return []Network{}, nil
	}

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		var lsInfo DockerNetworkLSFormat
		if err := json.Unmarshal([]byte(line), &lsInfo); err != nil {
			log.Printf("Failed to unmarshal network line from ls: '%s', error: %v", line, err)
			continue
		}

		var network Network
		network.ID = lsInfo.ID
		network.Name = lsInfo.Name
		network.Driver = lsInfo.Driver
		network.Scope = lsInfo.Scope

		// 获取网络详细信息以填充 Subnet 和 Gateway
		details, inspectErr := InspectNetwork(network.ID)
		if inspectErr != nil {
			log.Printf("Failed to inspect network %s ('%s'): %v. Subnet/Gateway will be empty.", network.ID, network.Name, inspectErr)
		} else if len(details) > 0 {
			if ipam, ok := details["IPAM"].(map[string]interface{}); ok {
				if configs, ok := ipam["Config"].([]interface{}); ok && len(configs) > 0 {
					if config, ok := configs[0].(map[string]interface{}); ok {
						if subnet, ok := config["Subnet"].(string); ok {
							network.Subnet = subnet
						}
						if gateway, ok := config["Gateway"].(string); ok {
							network.Gateway = gateway
						}
					}
				}
			}
		}

		networks = append(networks, network)
	}

	if len(lines) > 0 && len(networks) == 0 {
		log.Printf("Processed %d lines from docker network ls, but no networks were successfully parsed. Check output and unmarshal errors above.", len(lines))
	}

	return networks, nil
}

// CreateNetwork 创建网络
func CreateNetwork(name string, driver string) (string, error) {
	cmd := fmt.Sprintf("docker network create --driver %s %s", driver, name)
	output, err := shell.Exec(cmd)
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(output), nil
}

// RemoveNetwork 删除网络
func RemoveNetwork(id string) error {
	_, err := shell.Execf("docker network rm %s", id)
	return err
}

// InspectNetwork 获取网络详细信息
func InspectNetwork(id string) (map[string]interface{}, error) {
	output, err := shell.Execf("docker network inspect %s", id)
	if err != nil {
		return nil, err
	}

	var details []map[string]interface{}
	if err := json.Unmarshal([]byte(output), &details); err != nil {
		return nil, err
	}

	if len(details) == 0 {
		return nil, fmt.Errorf("network not found: %s", id)
	}

	return details[0], nil
}
