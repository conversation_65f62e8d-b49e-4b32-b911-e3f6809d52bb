package docker

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"panel/server/pkg/shell"
)

// Container struct (target structure for API response)
type Container struct {
	ID        string            `json:"id"`
	Name      string            `json:"name"`
	Image     string            `json:"image"`
	Command   string            `json:"command"`
	Created   time.Time         `json:"created"`
	Status    string            `json:"status"`
	Ports     []Port            `json:"ports"`
	State     string            `json:"state"`
	Size      string            `json:"size"`
	Networks  []string          `json:"networks"`
	Mounts    []Mount           `json:"mounts"`
	Labels    map[string]string `json:"labels"`
	IsRunning bool              `json:"is_running"`
}

// PsContainerInfo is a temporary struct to parse the JSON output from 'docker ps --format'
type PsContainerInfo struct {
	ID        string `json:"ID"`
	Names     string `json:"Names"`
	Image     string `json:"Image"`
	Command   string `json:"Command"`
	CreatedAt string `json:"CreatedAt"`
	Status    string `json:"Status"`
	State     string `json:"State"`
	Size      string `json:"Size"`
}

// Port structure for API response
type Port struct {
	IP          string `json:"ip,omitempty"`
	PrivatePort uint16 `json:"private_port"`
	PublicPort  uint16 `json:"public_port,omitempty"`
	Type        string `json:"type"`
}

// Mount structure for API response
type Mount struct {
	Type        string `json:"type"`
	Source      string `json:"source"`
	Destination string `json:"destination"`
	Mode        string `json:"mode"`
	RW          bool   `json:"rw"`
}

// FullContainerState from 'docker inspect .State'
type FullContainerState struct {
	Status     string    `json:"Status"`
	Running    bool      `json:"Running"`
	Paused     bool      `json:"Paused"`
	Restarting bool      `json:"Restarting"`
	OOMKilled  bool      `json:"OOMKilled"`
	Dead       bool      `json:"Dead"`
	Pid        int       `json:"Pid"`
	ExitCode   int       `json:"ExitCode"`
	Error      string    `json:"Error"`
	StartedAt  time.Time `json:"StartedAt"`
	FinishedAt time.Time `json:"FinishedAt"`
}

// InspectMountPoint from 'docker inspect .Mounts'
type InspectMountPoint struct {
	Type        string `json:"Type"`
	Name        string `json:"Name,omitempty"`
	Source      string `json:"Source"`
	Destination string `json:"Destination"`
	Driver      string `json:"Driver,omitempty"`
	Mode        string `json:"Mode"`
	RW          bool   `json:"RW"`
	Propagation string `json:"Propagation"`
}

// InspectPortBinding from 'docker inspect .NetworkSettings.Ports.[]'
type InspectPortBinding struct {
	HostIP   string `json:"HostIp"`
	HostPort string `json:"HostPort"`
}

// InspectAttachedNetwork from 'docker inspect .NetworkSettings.Networks.[]' (value part)
type InspectAttachedNetwork struct {
	NetworkID string `json:"NetworkID"`
}

// InspectNetworkSettings from 'docker inspect .NetworkSettings'
type InspectNetworkSettings struct {
	Ports    map[string][]InspectPortBinding   `json:"Ports"`
	Networks map[string]InspectAttachedNetwork `json:"Networks"`
}

// InspectConfig from 'docker inspect .Config'
type InspectConfig struct {
	Labels map[string]string `json:"Labels"`
}

// DockerInspectOutput is the top-level structure for unmarshalling 'docker inspect <id>' output
type DockerInspectOutput struct {
	ID              string                 `json:"Id"`
	Created         time.Time              `json:"Created"`
	Name            string                 `json:"Name"`
	State           FullContainerState     `json:"State"`
	Mounts          []InspectMountPoint    `json:"Mounts"`
	Config          InspectConfig          `json:"Config"`
	NetworkSettings InspectNetworkSettings `json:"NetworkSettings"`
}

// ContainerDetails holds processed data from 'docker inspect' relevant to our Container struct
type ContainerDetails struct {
	Networks  []string
	Mounts    []Mount
	Labels    map[string]string
	IsRunning bool
	Ports     []Port
}

// ListContainers fetches and processes container list
func ListContainers(all bool) ([]Container, error) {
	cmd := "docker ps --format '{{json .}}'"
	if all {
		cmd = "docker ps -a --format '{{json .}}'"
	}

	output, err := shell.Exec(cmd)
	if err != nil {
		log.Printf("Error executing docker ps: %v, output: %s", err, output)
		return nil, fmt.Errorf("failed to execute docker ps: %w. Output: %s", err, output)
	}

	var containers []Container
	lines := strings.Split(strings.TrimSpace(output), "\n")
	if len(lines) == 1 && lines[0] == "" {
		lines = []string{}
	}

	for _, line := range lines {
		if line == "" {
			continue
		}

		var psInfo PsContainerInfo
		if err := json.Unmarshal([]byte(line), &psInfo); err != nil {
			log.Printf("Failed to unmarshal container line from ps: '%s', error: %v", line, err)
			continue
		}

		var container Container
		container.ID = psInfo.ID
		container.Name = strings.TrimPrefix(psInfo.Names, "/")
		container.Image = psInfo.Image
		container.Command = psInfo.Command
		container.Status = psInfo.Status
		container.State = psInfo.State
		container.Size = psInfo.Size

		parsedTime, err := time.Parse("2006-01-02 15:04:05 -0700 MST", psInfo.CreatedAt)
		if err != nil {
			parsedTime, err = time.Parse("2006-01-02 15:04:05 Z0700 MST", psInfo.CreatedAt)
			if err != nil {
				log.Printf("Failed to parse CreatedAt string '%s' for container %s: %v", psInfo.CreatedAt, psInfo.ID, err)
			}
		}
		container.Created = parsedTime

		details, err := InspectContainer(container.ID)
		if err == nil {
			container.Networks = details.Networks
			container.Mounts = details.Mounts
			container.Labels = details.Labels
			container.IsRunning = details.IsRunning
			container.Ports = details.Ports
		} else {
			log.Printf("Failed to inspect container %s: %v. Appending with partial info from ps.", container.ID, err)
			container.IsRunning = strings.ToLower(psInfo.State) == "running"
		}
		containers = append(containers, container)
	}

	if len(lines) > 0 && len(containers) == 0 {
		log.Printf("Processed %d lines from docker ps, but no containers were successfully parsed and added. Check ps output and unmarshal errors above.", len(lines))
	}
	return containers, nil
}

// InspectContainer fetches and parses 'docker inspect' output for a container
func InspectContainer(id string) (*ContainerDetails, error) {
	output, err := shell.Execf("docker inspect %s", id)
	if err != nil {
		return nil, fmt.Errorf("docker inspect %s command failed: %w. Output: %s", id, err, output)
	}

	var inspectOutputArr []DockerInspectOutput
	if err := json.Unmarshal([]byte(output), &inspectOutputArr); err != nil {
		return nil, fmt.Errorf("failed to unmarshal docker inspect output for %s: %w. Raw output: %s", id, err, output)
	}

	if len(inspectOutputArr) == 0 {
		return nil, fmt.Errorf("docker inspect %s returned empty array", id)
	}
	inspected := inspectOutputArr[0]

	details := &ContainerDetails{}
	details.IsRunning = inspected.State.Running
	details.Labels = inspected.Config.Labels

	details.Mounts = make([]Mount, len(inspected.Mounts))
	for i, m := range inspected.Mounts {
		details.Mounts[i] = Mount{
			Type:        m.Type,
			Source:      m.Source,
			Destination: m.Destination,
			Mode:        m.Mode,
			RW:          m.RW,
		}
	}

	details.Networks = make([]string, 0, len(inspected.NetworkSettings.Networks))
	for netName := range inspected.NetworkSettings.Networks {
		details.Networks = append(details.Networks, netName)
	}

	details.Ports = make([]Port, 0)
	for privatePortProto, hostBindings := range inspected.NetworkSettings.Ports {
		parts := strings.SplitN(privatePortProto, "/", 2)
		if len(parts) != 2 {
			log.Printf("InspectContainer: Could not parse private port/protocol: %s", privatePortProto)
			continue
		}
		privPortStr, proto := parts[0], parts[1]
		privPortUint64, _ := strconv.ParseUint(privPortStr, 10, 16)

		if hostBindings == nil || len(hostBindings) == 0 {
			details.Ports = append(details.Ports, Port{
				PrivatePort: uint16(privPortUint64),
				Type:        proto,
			})
			continue
		}

		for _, binding := range hostBindings {
			pubPortUint64, _ := strconv.ParseUint(binding.HostPort, 10, 16)
			details.Ports = append(details.Ports, Port{
				IP:          binding.HostIP,
				PrivatePort: uint16(privPortUint64),
				PublicPort:  uint16(pubPortUint64),
				Type:        proto,
			})
		}
	}
	return details, nil
}

// CreateContainerOptions 创建容器选项
type CreateContainerOptions struct {
	Name          string            `json:"name"`
	Image         string            `json:"image"`
	Command       string            `json:"command"`
	Ports         []Port            `json:"ports"`
	Env           map[string]string `json:"env"`
	Mounts        []Mount           `json:"mounts"`
	Network       string            `json:"network"`
	RestartPolicy string            `json:"restart_policy"`
}

// StartContainer 启动容器
func StartContainer(id string) error {
	_, err := shell.Execf("docker start %s", id)
	return err
}

// StopContainer 停止容器
func StopContainer(id string, timeout int) error {
	if timeout > 0 {
		_, err := shell.Execf("docker stop --time %d %s", timeout, id)
		return err
	}
	_, err := shell.Execf("docker stop %s", id)
	return err
}

// RestartContainer 重启容器
func RestartContainer(id string, timeout int) error {
	if timeout > 0 {
		_, err := shell.Execf("docker restart --time %d %s", timeout, id)
		return err
	}
	_, err := shell.Execf("docker restart %s", id)
	return err
}

// RemoveContainer 删除容器
func RemoveContainer(id string, force bool, volumes bool) error {
	cmd := "docker rm"
	if force {
		cmd += " -f"
	}
	if volumes {
		cmd += " -v"
	}
	_, err := shell.Execf("%s %s", cmd, id)
	return err
}

// CreateContainer 创建容器
func CreateContainer(options CreateContainerOptions) (string, error) {
	cmd := "docker run -d"

	if options.Name != "" {
		cmd += fmt.Sprintf(" --name %s", options.Name)
	}
	for _, port := range options.Ports {
		if port.PublicPort != 0 {
			if port.IP != "" {
				cmd += fmt.Sprintf(" -p %s:%d:%d/%s", port.IP, port.PublicPort, port.PrivatePort, port.Type)
			} else {
				cmd += fmt.Sprintf(" -p %d:%d/%s", port.PublicPort, port.PrivatePort, port.Type)
			}
		}
	}
	for key, value := range options.Env {
		cmd += fmt.Sprintf(" -e %s=%s", key, value)
	}
	for _, mount := range options.Mounts {
		if mount.Type == "volume" || mount.Type == "bind" {
			mountStr := fmt.Sprintf(" -v %s:%s", mount.Source, mount.Destination)
			if mount.Mode != "" {
				if !mount.RW && mount.Mode == "" {
					mountStr += ":ro"
				} else if mount.Mode != "" {
					mountStr += fmt.Sprintf(":%s", mount.Mode)
				}
			} else if !mount.RW {
				mountStr += ":ro"
			}
			cmd += mountStr
		}
	}
	if options.Network != "" {
		cmd += fmt.Sprintf(" --network %s", options.Network)
	}
	if options.RestartPolicy != "" {
		cmd += fmt.Sprintf(" --restart %s", options.RestartPolicy)
	}
	cmd += fmt.Sprintf(" %s", options.Image)
	if options.Command != "" {
		cmd += fmt.Sprintf(" %s", options.Command)
	}

	output, err := shell.Exec(cmd)
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(output), nil
}

// GetContainerLogs 获取容器日志
func GetContainerLogs(id string, tail int) (string, error) {
	cmd := fmt.Sprintf("docker logs --tail %d %s", tail, id)
	return shell.Exec(cmd)
}

// GetContainerStats 获取容器统计信息
func GetContainerStats(id string) (map[string]interface{}, error) {
	cmd := fmt.Sprintf("docker stats %s --no-stream --format '{{json .}}'", id)
	output, err := shell.Exec(cmd)
	if err != nil {
		return nil, err
	}
	var stats map[string]interface{}
	if err := json.Unmarshal([]byte(output), &stats); err != nil {
		return nil, err
	}
	return stats, nil
}
