package docker

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strconv"
	"strings"
	"time"

	"panel/server/pkg/shell"
)

// Image 镜像信息
type Image struct {
	ID          string            `json:"id"`
	Repository  string            `json:"repository"`
	Tag         string            `json:"tag"`
	Digest      string            `json:"digest"`
	CreatedAt   time.Time         `json:"created_at"`
	Size        int64             `json:"size"`
	SharedSize  int64             `json:"shared_size"`
	VirtualSize int64             `json:"virtual_size"`
	Labels      map[string]string `json:"labels"`
}

// DockerImageFormat is a temporary struct to parse the JSON output from 'docker images --format'
type DockerImageFormat struct {
	ID           string `json:"ID"`
	Repository   string `json:"Repository"`
	Tag          string `json:"Tag"`
	Digest       string `json:"Digest"`
	CreatedAt    string `json:"CreatedAt"`    // String, to be parsed
	CreatedSince string `json:"CreatedSince"` // Informational
	Size         string `json:"Size"`         // String, e.g., "846MB"
	VirtualSize  string `json:"VirtualSize"`  // String, e.g., "845.8MB"
	SharedSize   string `json:"SharedSize"`   // String, e.g., "N/A"
	Containers   string `json:"Containers"`   // Informational
	UniqueSize   string `json:"UniqueSize"`   // Informational
}

var imageSizeRegex = regexp.MustCompile(`(?i)^([\d\.]+)\s*(k|m|g|t)?b?$`)

// parseImageSize converts image size strings (e.g., "1.2GB", "500MB", "N/A") to bytes (int64)
func parseImageSize(sizeStr string) (int64, error) {
	sizeStr = strings.TrimSpace(sizeStr)
	if strings.ToUpper(sizeStr) == "N/A" || sizeStr == "" {
		return 0, nil
	}

	matches := imageSizeRegex.FindStringSubmatch(sizeStr)
	if matches == nil {
		// If regex doesn't match, try to parse as a plain number (bytes)
		bytes, err := strconv.ParseInt(sizeStr, 10, 64)
		if err == nil {
			return bytes, nil
		}
		return 0, fmt.Errorf("invalid image size format: '%s'", sizeStr)
	}

	value, err := strconv.ParseFloat(matches[1], 64)
	if err != nil {
		return 0, fmt.Errorf("invalid image size value in '%s': %v", sizeStr, err)
	}

	unit := strings.ToLower(matches[2])
	var multiplier float64 = 1.0
	switch unit {
	case "k":
		multiplier = 1024
	case "m":
		multiplier = 1024 * 1024
	case "g":
		multiplier = 1024 * 1024 * 1024
	case "t":
		multiplier = 1024 * 1024 * 1024 * 1024
	}

	return int64(value * multiplier), nil
}

// ListImages 获取镜像列表
func ListImages() ([]Image, error) {
	cmd := "docker images --format '{{json .}}'"

	output, err := shell.Exec(cmd)
	if err != nil {
		log.Printf("Error executing docker images: %v, output: %s", err, output)
		return nil, fmt.Errorf("failed to execute docker images: %w. Output: %s", err, output)
	}

	var images []Image
	lines := strings.Split(strings.TrimSpace(output), "\n")
	if len(lines) == 1 && lines[0] == "" { // Handle case where no images are present
		return []Image{}, nil
	}

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		var rawImage DockerImageFormat
		if err := json.Unmarshal([]byte(line), &rawImage); err != nil {
			log.Printf("Failed to unmarshal image line: '%s', error: %v", line, err)
			continue
		}

		var image Image
		image.ID = rawImage.ID
		image.Repository = rawImage.Repository
		image.Tag = rawImage.Tag
		if rawImage.Digest == "<none>" { // Handle <none> digest
			image.Digest = ""
		} else {
			image.Digest = rawImage.Digest
		}

		// Parse CreatedAt
		// Example format: "2025-04-23 01:21:52 +0800 CST"
		parsedTime, timeErr := time.Parse("2006-01-02 15:04:05 -0700 MST", rawImage.CreatedAt)
		if timeErr != nil {
			// Fallback for potentially different timezone string representations if needed
			parsedTime, timeErr = time.Parse("2006-01-02 15:04:05 Z0700 MST", rawImage.CreatedAt)
			if timeErr != nil {
				log.Printf("Failed to parse CreatedAt string '%s' for image %s: %v", rawImage.CreatedAt, rawImage.ID, timeErr)
				// image.CreatedAt will be zero time
			}
		}
		image.CreatedAt = parsedTime

		// Parse Size
		size, sizeErr := parseImageSize(rawImage.Size)
		if sizeErr != nil {
			log.Printf("Failed to parse Size string '%s' for image %s: %v", rawImage.Size, rawImage.ID, sizeErr)
		}
		image.Size = size

		// Parse VirtualSize
		virtualSize, vsErr := parseImageSize(rawImage.VirtualSize)
		if vsErr != nil {
			log.Printf("Failed to parse VirtualSize string '%s' for image %s: %v", rawImage.VirtualSize, rawImage.ID, vsErr)
		}
		image.VirtualSize = virtualSize

		// Parse SharedSize
		sharedSize, ssErr := parseImageSize(rawImage.SharedSize)
		if ssErr != nil {
			log.Printf("Failed to parse SharedSize string '%s' for image %s: %v", rawImage.SharedSize, rawImage.ID, ssErr)
		}
		image.SharedSize = sharedSize

		// Labels are not available from 'docker images --format', InspectImage would be needed for that.
		image.Labels = make(map[string]string) // Initialize to empty map

		images = append(images, image)
	}

	if len(lines) > 0 && len(images) == 0 {
		log.Printf("Processed %d lines from docker images, but no images were successfully parsed. Check output and unmarshal errors above.", len(lines))
	}

	return images, nil
}

// PullImage 拉取镜像
func PullImage(name string, tag string) error {
	if tag == "" {
		tag = "latest"
	}

	_, err := shell.Exec(fmt.Sprintf("docker pull %s:%s", name, tag))
	return err
}

// RemoveImage 删除镜像
func RemoveImage(id string, force bool) error {
	cmd := "docker rmi"
	if force {
		cmd += " -f"
	}

	_, err := shell.Execf("%s %s", cmd, id)
	return err
}

// InspectImage 获取镜像详细信息
func InspectImage(id string) (map[string]interface{}, error) {
	output, err := shell.Execf("docker inspect %s", id)
	if err != nil {
		return nil, err
	}

	var details []map[string]interface{}
	if err := json.Unmarshal([]byte(output), &details); err != nil {
		return nil, err
	}

	if len(details) == 0 {
		return nil, fmt.Errorf("image not found: %s", id)
	}

	return details[0], nil
}
