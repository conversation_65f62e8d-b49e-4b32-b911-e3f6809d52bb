package docker

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"panel/server/pkg/shell"
)

// Volume 数据卷信息
type Volume struct {
	Name       string            `json:"name"`
	Driver     string            `json:"driver"`
	Mountpoint string            `json:"mountpoint"`
	CreatedAt  time.Time         `json:"created_at"`
	Labels     map[string]string `json:"labels"`
}

// DockerVolumeLSFormat is a temporary struct to parse the JSON output from 'docker volume ls --format'
type DockerVolumeLSFormat struct {
	Name       string `json:"Name"`
	Driver     string `json:"Driver"`
	Mountpoint string `json:"Mountpoint"`
	Labels     string `json:"Labels"` // Raw string from ls, e.g., "com.docker.volume.anonymous="
}

// parseDockerLabels parses a label string like "key1=value1,key2=value2" into a map.
// Handles cases where there are no labels or labels are empty.
func parseDockerLabels(labelsStr string) map[string]string {
	labels := make(map[string]string)
	if labelsStr == "" || strings.ToLower(labelsStr) == "n/a" {
		return labels
	}
	parts := strings.Split(labelsStr, ",")
	for _, part := range parts {
		kv := strings.SplitN(part, "=", 2)
		if len(kv) == 2 {
			labels[strings.TrimSpace(kv[0])] = strings.TrimSpace(kv[1])
		} else if len(kv) == 1 && strings.TrimSpace(kv[0]) != "" { // Handle labels without a value
			labels[strings.TrimSpace(kv[0])] = ""
		}
	}
	return labels
}

// ListVolumes 获取数据卷列表
func ListVolumes() ([]Volume, error) {
	cmd := "docker volume ls --format '{{json .}}'"

	output, err := shell.Exec(cmd)
	if err != nil {
		log.Printf("Error executing docker volume ls: %v, output: %s", err, output)
		return nil, fmt.Errorf("failed to execute docker volume ls: %w. Output: %s", err, output)
	}

	var volumes []Volume
	lines := strings.Split(strings.TrimSpace(output), "\n")
	if len(lines) == 1 && lines[0] == "" { // Handle case where no volumes are present
		return []Volume{}, nil
	}

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		var lsInfo DockerVolumeLSFormat
		if err := json.Unmarshal([]byte(line), &lsInfo); err != nil {
			log.Printf("Failed to unmarshal volume line from ls: '%s', error: %v", line, err)
			continue
		}

		var volume Volume
		volume.Name = lsInfo.Name
		volume.Driver = lsInfo.Driver
		volume.Mountpoint = lsInfo.Mountpoint
		volume.Labels = parseDockerLabels(lsInfo.Labels)

		// 获取数据卷详细信息以填充 CreatedAt (and potentially re-verify other fields if needed)
		details, inspectErr := InspectVolume(volume.Name)
		if inspectErr != nil {
			log.Printf("Failed to inspect volume %s: %v. CreatedAt will be zero.", volume.Name, inspectErr)
		} else {
			volume.CreatedAt = details.CreatedAt
			if details.Labels != nil {
				volume.Labels = details.Labels // Prefer labels from inspect if available
			}
			if details.Mountpoint != "" {
				volume.Mountpoint = details.Mountpoint // Prefer mountpoint from inspect
			}
		}

		volumes = append(volumes, volume)
	}

	if len(lines) > 0 && len(volumes) == 0 {
		log.Printf("Processed %d lines from docker volume ls, but no volumes were successfully parsed. Check output and unmarshal errors above.", len(lines))
	}

	return volumes, nil
}

// VolumeDetails 数据卷详细信息
type VolumeDetails struct {
	Name       string            `json:"Name"`
	Driver     string            `json:"Driver"`
	Mountpoint string            `json:"Mountpoint"`
	CreatedAt  time.Time         `json:"CreatedAt"`
	Labels     map[string]string `json:"Labels"`
}

// InspectVolume 获取数据卷详细信息
func InspectVolume(name string) (*VolumeDetails, error) {
	output, err := shell.Execf("docker volume inspect %s", name)
	if err != nil {
		return nil, err
	}

	var details []VolumeDetails
	if err := json.Unmarshal([]byte(output), &details); err != nil {
		return nil, err
	}

	if len(details) == 0 {
		return nil, fmt.Errorf("volume not found: %s", name)
	}

	return &details[0], nil
}

// CreateVolume 创建数据卷
func CreateVolume(name string) (string, error) {
	cmd := fmt.Sprintf("docker volume create %s", name)
	output, err := shell.Exec(cmd)
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(output), nil
}

// RemoveVolume 删除数据卷
func RemoveVolume(name string, force bool) error {
	cmd := "docker volume rm"
	if force {
		cmd += " -f"
	}

	_, err := shell.Execf("%s %s", cmd, name)
	return err
}
