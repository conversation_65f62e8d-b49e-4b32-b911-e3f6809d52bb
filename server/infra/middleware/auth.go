package middleware

import (
	"context"
	"strings"
	"time"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/golang-jwt/jwt/v5"
	
	"panel/server/config"
	"panel/server/pkg/constant"
	"panel/server/pkg/utils"
)

// JWTClaims JWT声明
type JWTClaims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	IsAdmin  bool   `json:"is_admin"`
	jwt.RegisteredClaims
}

// Auth 认证中间件
func Auth(cfg *config.Config) app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		// 从请求头获取令牌
		authHeader := string(c.Request.Header.Get("Authorization"))
		if authHeader == "" {
			utils.Error(ctx, c, constant.CodeAuthFailed, "未提供认证令牌")
			c.Abort()
			return
		}

		// 解析令牌
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			utils.Error(ctx, c, constant.CodeAuthFailed, "认证令牌格式错误")
			c.Abort()
			return
		}

		// 验证令牌
		token, err := jwt.ParseWithClaims(parts[1], &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
			return []byte(cfg.JWT.Secret), nil
		})

		if err != nil {
			if strings.Contains(err.Error(), "expired") {
				utils.Error(ctx, c, constant.CodeTokenExpired, "认证令牌已过期")
			} else {
				utils.Error(ctx, c, constant.CodeAuthFailed, "认证令牌无效")
			}
			c.Abort()
			return
		}

		// 获取声明
		claims, ok := token.Claims.(*JWTClaims)
		if !ok || !token.Valid {
			utils.Error(ctx, c, constant.CodeAuthFailed, "认证令牌无效")
			c.Abort()
			return
		}

		// 将用户信息存入上下文
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("is_admin", claims.IsAdmin)

		c.Next(ctx)
	}
}

// GenerateToken 生成JWT令牌
func GenerateToken(cfg *config.Config, userID uint, username string, isAdmin bool) (string, time.Time, error) {
	// 设置过期时间
	expiresAt := time.Now().Add(time.Duration(cfg.JWT.ExpiresIn) * time.Hour)

	// 创建声明
	claims := JWTClaims{
		UserID:   userID,
		Username: username,
		IsAdmin:  isAdmin,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	// 创建令牌
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名令牌
	tokenString, err := token.SignedString([]byte(cfg.JWT.Secret))
	if err != nil {
		return "", time.Time{}, err
	}

	return tokenString, expiresAt, nil
}
