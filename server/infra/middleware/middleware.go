package middleware

import (
	"context"
	"time"
	
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/hlog"
)

// Cors 跨域中间件
func Cors() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.<PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		
		// 处理预检请求
		if string(c.Request.Method()) == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next(ctx)
	}
}

// Logger 日志中间件
func Logger() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		start := time.Now()
		path := string(c.Request.URI().Path())
		method := string(c.Request.Method())
		
		c.Next(ctx)
		
		latency := time.Since(start)
		statusCode := c.Response.StatusCode()
		clientIP := c.ClientIP()
		
		hlog.Infof("[%s] %s %s %d %s", method, path, clientIP, statusCode, latency)
	}
}
