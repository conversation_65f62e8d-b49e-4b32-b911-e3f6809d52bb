package database

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"panel/server/biz/model"
	"panel/server/config"
)

// Init 初始化数据库连接
func Init(cfg *config.Config) (*gorm.DB, error) {
	// 确保数据目录存在
	if err := os.Mkdir<PERSON>ll(cfg.Data.Path, 0755); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %w", err)
	}

	// 数据库文件路径
	dbPath := filepath.Join(cfg.Data.Path, "panel.db")

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 自动迁移表结构
	if err := db.AutoMigrate(
		&model.User{},
		&model.UserToken{},
	); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	// 初始化默认数据
	if err := initDefaultData(db, cfg); err != nil {
		return nil, fmt.Errorf("failed to initialize default data: %w", err)
	}

	return db, nil
}

// initDefaultData 初始化默认数据
func initDefaultData(db *gorm.DB, cfg *config.Config) error {
	// 检查是否已有管理员用户
	var count int64
	db.Model(&model.User{}).Count(&count)

	// 如果没有用户，创建默认管理员
	if count == 0 {
		// 生成密码哈希
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin"), bcrypt.DefaultCost)
		if err != nil {
			return fmt.Errorf("failed to hash password: %w", err)
		}

		// 创建管理员用户
		admin := model.User{
			Username:  "admin",
			Password:  string(hashedPassword),
			Email:     "<EMAIL>",
			IsAdmin:   true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		if err := db.Create(&admin).Error; err != nil {
			return fmt.Errorf("failed to create admin user: %w", err)
		}
	}

	return nil
}
