package model

import (
	"time"

	"gorm.io/gorm"
)

// OperationLog 操作日志
type OperationLog struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	UserID    uint           `gorm:"index" json:"user_id"`
	Username  string         `gorm:"size:50" json:"username"`
	IP        string         `gorm:"size:50" json:"ip"`
	Module    string         `gorm:"size:50" json:"module"`
	Action    string         `gorm:"size:50" json:"action"`
	Status    bool           `json:"status"` // true: 成功, false: 失败
	Detail    string         `gorm:"size:1000" json:"detail"`
	CreatedAt time.Time      `json:"created_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// Backup 备份
type Backup struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	Name      string         `gorm:"size:100;not null" json:"name"`
	Type      string         `gorm:"size:20;not null" json:"type"` // website, database, etc.
	TargetID  uint           `gorm:"index" json:"target_id"`       // 备份目标ID
	Path      string         `gorm:"size:255;not null" json:"path"`
	Size      int64          `json:"size"` // 文件大小（字节）
	Status    bool           `json:"status"`
	CreatedAt time.Time      `json:"created_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}
