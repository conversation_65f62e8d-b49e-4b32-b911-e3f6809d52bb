package model

import (
	"time"

	"gorm.io/gorm"
)

// CronJob 定时任务
type CronJob struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	Name      string         `gorm:"size:100;not null" json:"name"`
	Command   string         `gorm:"size:500;not null" json:"command"`
	Schedule  string         `gorm:"size:50;not null" json:"schedule"` // cron表达式
	Status    bool           `gorm:"default:true" json:"status"`       // true: 启用, false: 禁用
	LogFile   string         `gorm:"size:255" json:"log_file"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}
