package model

import (
	"time"

	"gorm.io/gorm"
)

// DatabaseServer 数据库服务器
type DatabaseServer struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	Name      string         `gorm:"size:100;not null" json:"name"`
	Type      string         `gorm:"size:20;not null" json:"type"` // mysql, postgresql, etc.
	Host      string         `gorm:"size:100;not null" json:"host"`
	Port      int            `json:"port"`
	Username  string         `gorm:"size:50;not null" json:"username"`
	Password  string         `gorm:"size:100;not null" json:"-"` // 不返回密码
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// Database 数据库
type Database struct {
	ID         uint           `gorm:"primarykey" json:"id"`
	ServerID   uint           `gorm:"index;not null" json:"server_id"`
	Name       string         `gorm:"size:100;not null" json:"name"`
	Username   string         `gorm:"size:50;not null" json:"username"`
	Password   string         `gorm:"size:100;not null" json:"-"` // 不返回密码
	Charset    string         `gorm:"size:20;default:utf8mb4" json:"charset"`
	Collation  string         `gorm:"size:50;default:utf8mb4_general_ci" json:"collation"`
	Comment    string         `gorm:"size:255" json:"comment"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
	Server     DatabaseServer `gorm:"foreignKey:ServerID" json:"server,omitempty"`
}
