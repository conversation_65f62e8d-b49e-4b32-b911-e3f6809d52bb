package model

import (
	"time"

	"gorm.io/gorm"
)

// Certificate SSL证书
type Certificate struct {
	ID         uint           `gorm:"primarykey" json:"id"`
	Name       string         `gorm:"size:100;not null" json:"name"`
	Domains    string         `gorm:"size:500;not null" json:"domains"` // 多个域名用逗号分隔
	CertPath   string         `gorm:"size:255;not null" json:"cert_path"`
	KeyPath    string         `gorm:"size:255;not null" json:"key_path"`
	Type       string         `gorm:"size:20;not null" json:"type"` // letsencrypt, manual
	ExpiresAt  time.Time      `json:"expires_at"`
	AutoRenew  bool           `gorm:"default:true" json:"auto_renew"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
}
