package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
)

// WebsiteConfig 网站配置
type WebsiteConfig struct {
	ServerName string `json:"server_name"`
	Root       string `json:"root"`
	Index      string `json:"index"`
	ErrorPage  string `json:"error_page"`
	AccessLog  string `json:"access_log"`
	ErrorLog   string `json:"error_log"`
	PHP        string `json:"php"`
}

// Value 实现 driver.Valuer 接口
func (c WebsiteConfig) Value() (driver.Value, error) {
	return json.Marshal(c)
}

// Scan 实现 sql.Scanner 接口
func (c *WebsiteConfig) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, &c)
}

// Website 网站模型
type Website struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	Name      string         `gorm:"size:100;not null" json:"name"`
	Type      string         `gorm:"size:20;not null" json:"type"` // static, php, node, etc.
	Path      string         `gorm:"size:255;not null" json:"path"`
	Domains   string         `gorm:"size:500;not null" json:"domains"` // 多个域名用逗号分隔
	HTTPS     bool           `gorm:"default:false" json:"https"`
	Status    bool           `gorm:"default:true" json:"status"` // true: 启用, false: 禁用
	PHP       string         `gorm:"size:10" json:"php"`         // PHP版本
	Remark    string         `gorm:"size:255" json:"remark"`
	Config    WebsiteConfig  `gorm:"type:json" json:"config"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}
