package handler

import (
	"context"
	"strconv"

	"github.com/cloudwego/hertz/pkg/app"
	"gorm.io/gorm"

	"panel/server/pkg/constant"
	"panel/server/pkg/container/docker"
	"panel/server/pkg/utils"
)

// ContainerHandler 容器处理器
type ContainerHandler struct {
	db *gorm.DB
}

// NewContainerHandler 创建容器处理器
func NewContainerHandler(db *gorm.DB) *ContainerHandler {
	return &ContainerHandler{db: db}
}

// List 获取容器列表
func (h *ContainerHandler) List(ctx context.Context, c *app.RequestContext) {
	all := c.Query("all") == "true"

	containers, err := docker.ListContainers(all)
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取容器列表失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, containers)
}

// Create 创建容器
func (h *ContainerHandler) Create(ctx context.Context, c *app.RequestContext) {
	var req docker.CreateContainerOptions

	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	id, err := docker.CreateContainer(req)
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "创建容器失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, map[string]string{
		"id": id,
	})
}

// Start 启动容器
func (h *ContainerHandler) Start(ctx context.Context, c *app.RequestContext) {
	var req struct {
		ID string `json:"id" vd:"len($) > 0"`
	}

	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	if err := docker.StartContainer(req.ID); err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "启动容器失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, nil)
}

// Stop 停止容器
func (h *ContainerHandler) Stop(ctx context.Context, c *app.RequestContext) {
	var req struct {
		ID      string `json:"id" vd:"len($) > 0"`
		Timeout int    `json:"timeout"`
	}

	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	if err := docker.StopContainer(req.ID, req.Timeout); err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "停止容器失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, nil)
}

// Restart 重启容器
func (h *ContainerHandler) Restart(ctx context.Context, c *app.RequestContext) {
	var req struct {
		ID      string `json:"id" vd:"len($) > 0"`
		Timeout int    `json:"timeout"`
	}

	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	if err := docker.RestartContainer(req.ID, req.Timeout); err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "重启容器失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, nil)
}

// Remove 删除容器
func (h *ContainerHandler) Remove(ctx context.Context, c *app.RequestContext) {
	var req struct {
		ID      string `json:"id" vd:"len($) > 0"`
		Force   bool   `json:"force"`
		Volumes bool   `json:"volumes"`
	}

	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	if err := docker.RemoveContainer(req.ID, req.Force, req.Volumes); err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "删除容器失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, nil)
}

// Logs 获取容器日志
func (h *ContainerHandler) Logs(ctx context.Context, c *app.RequestContext) {
	id := c.Param("id")
	tail, _ := strconv.Atoi(c.DefaultQuery("tail", "100"))

	logs, err := docker.GetContainerLogs(id, tail)
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取容器日志失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, map[string]string{
		"logs": logs,
	})
}

// Stats 获取容器统计信息
func (h *ContainerHandler) Stats(ctx context.Context, c *app.RequestContext) {
	id := c.Param("id")

	stats, err := docker.GetContainerStats(id)
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取容器统计信息失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, stats)
}

// ListImages 获取镜像列表
func (h *ContainerHandler) ListImages(ctx context.Context, c *app.RequestContext) {
	images, err := docker.ListImages()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取镜像列表失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, images)
}

// PullImage 拉取镜像
func (h *ContainerHandler) PullImage(ctx context.Context, c *app.RequestContext) {
	var req struct {
		Name string `json:"name" vd:"len($) > 0"`
		Tag  string `json:"tag"`
	}

	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	if err := docker.PullImage(req.Name, req.Tag); err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "拉取镜像失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, nil)
}

// RemoveImage 删除镜像
func (h *ContainerHandler) RemoveImage(ctx context.Context, c *app.RequestContext) {
	var req struct {
		ID    string `json:"id" vd:"len($) > 0"`
		Force bool   `json:"force"`
	}

	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	if err := docker.RemoveImage(req.ID, req.Force); err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "删除镜像失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, nil)
}

// ListNetworks 获取网络列表
func (h *ContainerHandler) ListNetworks(ctx context.Context, c *app.RequestContext) {
	networks, err := docker.ListNetworks()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取网络列表失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, networks)
}

// CreateNetwork 创建网络
func (h *ContainerHandler) CreateNetwork(ctx context.Context, c *app.RequestContext) {
	var req struct {
		Name   string `json:"name" vd:"len($) > 0"`
		Driver string `json:"driver"`
	}

	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	if req.Driver == "" {
		req.Driver = "bridge"
	}

	id, err := docker.CreateNetwork(req.Name, req.Driver)
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "创建网络失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, map[string]string{
		"id": id,
	})
}

// RemoveNetwork 删除网络
func (h *ContainerHandler) RemoveNetwork(ctx context.Context, c *app.RequestContext) {
	var req struct {
		ID string `json:"id" vd:"len($) > 0"`
	}

	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	if err := docker.RemoveNetwork(req.ID); err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "删除网络失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, nil)
}

// ListVolumes 获取数据卷列表
func (h *ContainerHandler) ListVolumes(ctx context.Context, c *app.RequestContext) {
	volumes, err := docker.ListVolumes()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取数据卷列表失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, volumes)
}

// CreateVolume 创建数据卷
func (h *ContainerHandler) CreateVolume(ctx context.Context, c *app.RequestContext) {
	var req struct {
		Name string `json:"name" vd:"len($) > 0"`
	}

	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	name, err := docker.CreateVolume(req.Name)
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "创建数据卷失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, map[string]string{
		"name": name,
	})
}

// RemoveVolume 删除数据卷
func (h *ContainerHandler) RemoveVolume(ctx context.Context, c *app.RequestContext) {
	var req struct {
		Name  string `json:"name" vd:"len($) > 0"`
		Force bool   `json:"force"`
	}

	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	if err := docker.RemoveVolume(req.Name, req.Force); err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "删除数据卷失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, nil)
}
