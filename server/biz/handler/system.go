package handler

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"gorm.io/gorm"

	"panel/server/pkg/constant"
	"panel/server/pkg/os"
	"panel/server/pkg/systemctl"
	"panel/server/pkg/utils"
)

// SystemHandler 系统处理器
type SystemHandler struct {
	db *gorm.DB
}

// NewSystemHandler 创建系统处理器
func NewSystemHandler(db *gorm.DB) *SystemHandler {
	return &SystemHandler{db: db}
}

// GetInfo 获取系统信息
func (h *SystemHandler) GetInfo(ctx context.Context, c *app.RequestContext) {
	// 获取系统信息
	info, err := os.GetSystemInfo()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取系统信息失败: "+err.Error())
		return
	}

	// 获取网络信息
	networkInfo, err := os.GetNetworkInfo()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取网络信息失败: "+err.Error())
		return
	}

	// 构建响应
	response := map[string]interface{}{
		"system":  info,
		"network": networkInfo,
	}

	utils.Success(ctx, c, response)
}

// GetStatus 获取系统状态
func (h *SystemHandler) GetStatus(ctx context.Context, c *app.RequestContext) {
	// 获取CPU使用率
	cpuUsage, err := os.GetCPUUsage()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取CPU使用率失败: "+err.Error())
		return
	}

	// 获取内存使用率
	memoryUsage, err := os.GetMemoryUsage()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取内存使用率失败: "+err.Error())
		return
	}

	// 获取磁盘使用率
	diskUsage, err := os.GetDiskUsage()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取磁盘使用率失败: "+err.Error())
		return
	}

	// 获取负载均衡
	loadAverage, err := os.GetLoadAverage()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取负载均衡失败: "+err.Error())
		return
	}

	// 获取网络信息
	networkInfo, err := os.GetNetworkInfo()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取网络信息失败: "+err.Error())
		return
	}

	// 构建响应
	response := map[string]interface{}{
		"cpu_usage":    cpuUsage,
		"memory_usage": memoryUsage,
		"disk_usage":   diskUsage,
		"load_average": loadAverage,
		"network":      networkInfo,
	}

	utils.Success(ctx, c, response)
}

// ListServices 获取服务列表
func (h *SystemHandler) ListServices(ctx context.Context, c *app.RequestContext) {
	// 获取过滤参数
	filter := c.Query("filter")

	// 获取服务列表
	services, err := systemctl.ListServices(filter)
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取服务列表失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, services)
}

// GetService 获取服务详情
func (h *SystemHandler) GetService(ctx context.Context, c *app.RequestContext) {
	// 获取服务名称
	name := c.Param("name")
	if name == "" {
		utils.Error(ctx, c, constant.CodeParamError, "服务名称不能为空")
		return
	}

	// 获取服务信息
	service, err := systemctl.GetService(name)
	if err != nil {
		utils.Error(ctx, c, constant.CodeNotFound, "服务不存在: "+err.Error())
		return
	}

	utils.Success(ctx, c, service)
}

// StartService 启动服务
func (h *SystemHandler) StartService(ctx context.Context, c *app.RequestContext) {
	// 获取服务名称
	name := c.Param("name")
	if name == "" {
		utils.Error(ctx, c, constant.CodeParamError, "服务名称不能为空")
		return
	}

	// 启动服务
	if err := systemctl.StartService(name); err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "启动服务失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, nil)
}

// StopService 停止服务
func (h *SystemHandler) StopService(ctx context.Context, c *app.RequestContext) {
	// 获取服务名称
	name := c.Param("name")
	if name == "" {
		utils.Error(ctx, c, constant.CodeParamError, "服务名称不能为空")
		return
	}

	// 停止服务
	if err := systemctl.StopService(name); err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "停止服务失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, nil)
}

// RestartService 重启服务
func (h *SystemHandler) RestartService(ctx context.Context, c *app.RequestContext) {
	// 获取服务名称
	name := c.Param("name")
	if name == "" {
		utils.Error(ctx, c, constant.CodeParamError, "服务名称不能为空")
		return
	}

	// 重启服务
	if err := systemctl.RestartService(name); err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "重启服务失败: "+err.Error())
		return
	}

	utils.Success(ctx, c, nil)
}

// GetRealtime 获取实时数据
func (h *SystemHandler) GetRealtime(ctx context.Context, c *app.RequestContext) {
	// 解析请求参数
	var req struct {
		Nets  []string `json:"nets"`
		Disks []string `json:"disks"`
	}

	if err := c.BindJSON(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, "参数解析失败: "+err.Error())
		return
	}

	// 获取CPU使用率
	cpuUsage, err := os.GetCPUUsage()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取CPU使用率失败: "+err.Error())
		return
	}

	// 获取内存使用率
	memoryUsage, err := os.GetMemoryUsage()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取内存使用率失败: "+err.Error())
		return
	}

	// 获取负载均衡
	loadAverage, err := os.GetLoadAverage()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取负载均衡失败: "+err.Error())
		return
	}

	// 获取磁盘使用率
	diskUsage, err := os.GetDiskUsage()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取磁盘使用率失败: "+err.Error())
		return
	}

	// 获取系统信息以获取内存和磁盘详细信息
	systemInfo, err := os.GetSystemInfo()
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "获取系统信息失败: "+err.Error())
		return
	}

	// 构建响应
	response := map[string]interface{}{
		"percent": cpuUsage,
		"load": map[string]interface{}{
			"load1":  loadAverage[0],
			"load5":  loadAverage[1],
			"load15": loadAverage[2],
		},
		"mem": map[string]interface{}{
			"total":       systemInfo.Memory.Total,
			"used":        systemInfo.Memory.Used,
			"usedPercent": memoryUsage,
		},
		"disk_usage": []map[string]interface{}{
			{
				"path":        "/",
				"total":       systemInfo.Disk.Total,
				"used":        systemInfo.Disk.Used,
				"usedPercent": diskUsage,
			},
		},
		"net":     []interface{}{},
		"disk_io": []interface{}{},
		"time":    "2024-01-01T00:00:00Z", // 当前时间
	}

	utils.Success(ctx, c, response)
}

// GetCountInfo 获取统计信息
func (h *SystemHandler) GetCountInfo(ctx context.Context, c *app.RequestContext) {
	// 这里应该从数据库或其他数据源获取实际的统计信息
	// 目前返回模拟数据
	response := map[string]interface{}{
		"website":  0,
		"database": 0,
		"ftp":      0,
		"cron":     0,
	}

	utils.Success(ctx, c, response)
}
