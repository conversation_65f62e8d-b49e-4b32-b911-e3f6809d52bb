package handler

import (
	"context"
	"time"

	"github.com/cloudwego/hertz/pkg/app"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"panel/server/biz/model"
	"panel/server/config"
	"panel/server/infra/middleware"
	"panel/server/pkg/constant"
	"panel/server/pkg/utils"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	db  *gorm.DB
	cfg *config.Config
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(db *gorm.DB, cfg *config.Config) *AuthHandler {
	return &AuthHandler{
		db:  db,
		cfg: cfg,
	}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username  string `json:"username" vd:"len($) > 0"`
	Password  string `json:"password" vd:"len($) > 0"`
	RememberMe bool   `json:"remember_me"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	UserInfo  struct {
		ID       uint   `json:"id"`
		Username string `json:"username"`
		Email    string `json:"email"`
		IsAdmin  bool   `json:"is_admin"`
	} `json:"user_info"`
}

// Login 用户登录
func (h *AuthHandler) Login(ctx context.Context, c *app.RequestContext) {
	var req LoginRequest
	if err := c.BindAndValidate(&req); err != nil {
		utils.Error(ctx, c, constant.CodeParamError, err.Error())
		return
	}

	// 查询用户
	var user model.User
	if err := h.db.Where("username = ?", req.Username).First(&user).Error; err != nil {
		utils.Error(ctx, c, constant.CodeAuthFailed, "用户名或密码错误")
		return
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		utils.Error(ctx, c, constant.CodeAuthFailed, "用户名或密码错误")
		return
	}

	// 生成令牌
	expiresIn := h.cfg.JWT.ExpiresIn
	if req.RememberMe {
		expiresIn = expiresIn * 7 // 记住我，有效期延长7倍
	}

	// 更新最后登录时间
	now := time.Now()
	h.db.Model(&user).Update("last_login", &now)

	// 生成JWT令牌
	token, expiresAt, err := middleware.GenerateToken(h.cfg, user.ID, user.Username, user.IsAdmin)
	if err != nil {
		utils.Error(ctx, c, constant.CodeSystemError, "生成令牌失败")
		return
	}

	// 保存令牌到数据库
	userToken := model.UserToken{
		UserID:    user.ID,
		Token:     token,
		ExpiresAt: expiresAt,
	}
	h.db.Create(&userToken)

	// 构建响应
	resp := LoginResponse{
		Token:     token,
		ExpiresAt: expiresAt,
	}
	resp.UserInfo.ID = user.ID
	resp.UserInfo.Username = user.Username
	resp.UserInfo.Email = user.Email
	resp.UserInfo.IsAdmin = user.IsAdmin

	utils.Success(ctx, c, resp)
}

// Logout 用户登出
func (h *AuthHandler) Logout(ctx context.Context, c *app.RequestContext) {
	// 从上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Error(ctx, c, constant.CodeAuthFailed, "未认证")
		return
	}

	// 获取令牌
	authHeader := string(c.Request.Header.Get("Authorization"))
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		token := authHeader[7:]
		// 删除令牌
		h.db.Where("user_id = ? AND token = ?", userID, token).Delete(&model.UserToken{})
	}

	utils.Success(ctx, c, nil)
}

// GetProfile 获取用户信息
func (h *AuthHandler) GetProfile(ctx context.Context, c *app.RequestContext) {
	// 从上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Error(ctx, c, constant.CodeAuthFailed, "未认证")
		return
	}

	// 查询用户
	var user model.User
	if err := h.db.First(&user, userID).Error; err != nil {
		utils.Error(ctx, c, constant.CodeNotFound, "用户不存在")
		return
	}

	// 构建响应
	userInfo := struct {
		ID       uint      `json:"id"`
		Username string    `json:"username"`
		Email    string    `json:"email"`
		IsAdmin  bool      `json:"is_admin"`
		LastLogin *time.Time `json:"last_login"`
		CreatedAt time.Time `json:"created_at"`
	}{
		ID:        user.ID,
		Username:  user.Username,
		Email:     user.Email,
		IsAdmin:   user.IsAdmin,
		LastLogin: user.LastLogin,
		CreatedAt: user.CreatedAt,
	}

	utils.Success(ctx, c, userInfo)
}
