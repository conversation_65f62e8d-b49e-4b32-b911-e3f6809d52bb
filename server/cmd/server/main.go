package main

import (
	"fmt"
	"os"
	
	"github.com/cloudwego/hertz/pkg/app/server"
	"github.com/cloudwego/hertz/pkg/common/hlog"
	
	"panel/server/config"
	"panel/server/infra/database"
	"panel/server/router"
)

func main() {
	// 加载配置
	cfg := config.Load()
	
	// 初始化数据库
	db, err := database.Init(cfg)
	if err != nil {
		hlog.Fatal("Failed to initialize database:", err)
		os.Exit(1)
	}
	
	// 创建Hertz服务器实例
	h := server.New(
		server.WithHostPorts(fmt.Sprintf("%s:%d", cfg.Server.Address, cfg.Server.Port)),
		server.WithMaxRequestBodySize(20 * 1024 * 1024), // 20MB
	)
	
	// 注册路由
	router.Register(h, cfg, db)
	
	// 启动服务器
	h.Spin()
}
