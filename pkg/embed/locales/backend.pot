msgid ""
msgstr ""
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: \n"
"X-Generator: xgotext\n"

#: internal/data/website.go:270
#: internal/data/website.go:643
msgid "# Rewrite rule"
msgstr ""

#: internal/service/cli.go:286
msgid "2FA disabled for user %s"
msgstr ""

#: internal/service/cli.go:294
msgid "2FA url: %s"
msgstr ""

#: internal/bootstrap/cli.go:18
msgid "AUTHOR"
msgstr ""

#: internal/apps/php/app.go:131
msgid "Accepted Connections"
msgstr ""

#: internal/apps/mysql/app.go:121
msgid "Active Connections"
msgstr ""

#: internal/apps/php/app.go:136
msgid "Active Processes"
msgstr ""

#: internal/apps/nginx/app.go:120
msgid "Active connections"
msgstr ""

#: internal/route/cli.go:237
msgid "Add database server"
msgstr ""

#: internal/route/cli.go:455
msgid "Add panel application mark (use only under guidance)"
msgstr ""

#: internal/service/cert.go:60
msgid "Aliyun"
msgstr ""

#: internal/service/cli.go:936
msgid "Already initialized"
msgstr ""

#: internal/data/app.go:347
msgid "App %s %s"
msgstr ""

#: internal/service/cli.go:785
msgid "App %s installed successfully"
msgstr ""

#: internal/service/cli.go:799
msgid "App %s uninstalled successfully"
msgstr ""

#: internal/service/cli.go:813
msgid "App %s updated successfully"
msgstr ""

#: internal/service/cli.go:782
msgid "App install failed: %v"
msgstr ""

#: internal/service/cli.go:796
msgid "App uninstall failed: %v"
msgstr ""

#: internal/service/cli.go:810
msgid "App update failed: %v"
msgstr ""

#: internal/apps/php/app.go:128
msgid "Application Pool"
msgstr ""

#: internal/route/cli.go:436
msgid "Application management"
msgstr ""

#: internal/route/cli.go:313
msgid "Backup database"
msgstr ""

#: internal/route/cli.go:373
msgid "Backup directory (default path if not filled)"
msgstr ""

#: internal/service/cli.go:664
#: internal/service/cli.go:680
#: internal/service/cli.go:694
msgid "Backup failed: %v"
msgstr ""

#: internal/route/cli.go:361
msgid "Backup file"
msgstr ""

#: internal/route/cli.go:337
msgid "Backup panel"
msgstr ""

#: internal/route/cli.go:355
msgid "Backup type"
msgstr ""

#: internal/route/cli.go:295
msgid "Backup website"
msgstr ""

#: internal/service/cli.go:486
msgid "Bind IP disabled"
msgstr ""

#: internal/service/cli.go:511
msgid "Bind UA disabled"
msgstr ""

#: internal/service/cli.go:461
msgid "Bind domain disabled"
msgstr ""

#: internal/apps/mysql/app.go:120
#: internal/apps/mysql/app.go:139
msgid "Bytes Received"
msgstr ""

#: internal/apps/mysql/app.go:119
#: internal/apps/mysql/app.go:139
msgid "Bytes Sent"
msgstr ""

#: internal/apps/php/app.go:353
msgid "Bzip2 is a library for compressing and decompressing files"
msgstr ""

#: internal/bootstrap/cli.go:24
msgid "CATEGORY"
msgstr ""

#: internal/bootstrap/cli.go:19
#: internal/bootstrap/cli.go:30
msgid "COMMANDS"
msgstr ""

#: internal/bootstrap/cli.go:21
msgid "COPYRIGHT"
msgstr ""

#: internal/apps/php/app.go:393
msgid "Calendar is a library for handling dates"
msgstr ""

#: internal/route/cli.go:157
msgid "Change panel port"
msgstr ""

#: internal/route/cli.go:80
msgid "Change user 2FA"
msgstr ""

#: internal/route/cli.go:75
msgid "Change user password"
msgstr ""

#: internal/route/cli.go:70
msgid "Change username"
msgstr ""

#: internal/service/cli.go:718
msgid "Cleaning failed: %v"
msgstr ""

#: internal/data/backup.go:633
msgid "Cleaning temporary directory failed: %v"
msgstr ""

#: internal/data/backup.go:671
msgid "Cleaning temporary file failed: %v"
msgstr ""

#: internal/data/backup.go:198
msgid "Cleanup failed: %v"
msgstr ""

#: internal/route/cli.go:349
msgid "Clear backups"
msgstr ""

#: internal/route/cli.go:499
msgid "Clear panel task queue (use only under guidance)"
msgstr ""

#: internal/route/cli.go:404
msgid "Clear rotated logs"
msgstr ""

#: internal/service/cert.go:104
msgid "ClouDNS"
msgstr ""

#: internal/service/cert.go:76
msgid "CloudFlare"
msgstr ""

#: internal/apps/redis/app.go:86
msgid "Commands Per Second"
msgstr ""

#: internal/apps/redis/app.go:79
msgid "Connected Clients"
msgstr ""

#: internal/route/cli.go:166
msgid "Create new website"
msgstr ""

#: internal/service/cli.go:752
msgid "Currently only website log rotation is supported"
msgstr ""

#: internal/bootstrap/cli.go:17
#: internal/bootstrap/cli.go:25
msgid "DESCRIPTION"
msgstr ""

#: internal/route/cli.go:291
msgid "Data backup"
msgstr ""

#: internal/service/cli.go:121
msgid "Data synchronized successfully"
msgstr ""

#: internal/route/cli.go:233
msgid "Database management"
msgstr ""

#: internal/route/cli.go:325
msgid "Database name"
msgstr ""

#: internal/service/cli.go:639
msgid "Database server %s added successfully"
msgstr ""

#: internal/service/cli.go:653
msgid "Database server %s deleted successfully"
msgstr ""

#: internal/route/cli.go:319
msgid "Database type"
msgstr ""

#: internal/route/cli.go:276
msgid "Delete database server"
msgstr ""

#: internal/route/cli.go:212
msgid "Delete website (including website directory, database with the same name)"
msgstr ""

#: internal/route/cli.go:96
msgid "Disable HTTPS"
msgstr ""

#: internal/route/cli.go:139
msgid "Disable IP binding"
msgstr ""

#: internal/route/cli.go:150
msgid "Disable UA binding"
msgstr ""

#: internal/route/cli.go:117
msgid "Disable access entrance"
msgstr ""

#: internal/route/cli.go:128
msgid "Disable domain binding"
msgstr ""

#: internal/service/cli.go:107
msgid "Download URL is empty"
msgstr ""

#: internal/data/backup.go:731
#: internal/data/backup.go:734
msgid "Download failed: %v"
msgstr ""

#: internal/data/backup.go:737
msgid "Download file check failed"
msgstr ""

#: internal/service/file.go:295
msgid "Download remote file %v"
msgstr ""

#: internal/service/cert.go:108
msgid "Duck DNS"
msgstr ""

#: internal/route/cli.go:91
msgid "Enable HTTPS"
msgstr ""

#: internal/route/cli.go:112
msgid "Enable access entrance"
msgstr ""

#: internal/apps/php/app.go:383
msgid "Enchant is a spell-checking library"
msgstr ""

#: internal/service/cli.go:436
msgid "Entrance disabled"
msgstr ""

#: internal/service/cli.go:410
msgid "Entrance enabled"
msgstr ""

#: internal/service/cli.go:165
#: internal/service/cli.go:411
msgid "Entrance: %s"
msgstr ""

#: internal/apps/php/app.go:363
msgid "Event is a library for handling events"
msgstr ""

#: internal/apps/php/app.go:318
msgid "Exif is a library for reading and writing image metadata"
msgstr ""

#: internal/service/cli.go:284
msgid "Failed to change 2FA status: %v"
msgstr ""

#: internal/service/cli.go:258
msgid "Failed to change password: %v"
msgstr ""

#: internal/service/cli.go:226
msgid "Failed to change username: %v"
msgstr ""

#: internal/service/cli.go:873
msgid "Failed to clear tasks: %v"
msgstr ""

#: internal/service/cli.go:848
msgid "Failed to delete app: %v"
msgstr ""

#: internal/service/cli.go:927
msgid "Failed to delete setting: %v"
msgstr ""

#: internal/service/cli.go:292
msgid "Failed to generate 2FA: %v"
msgstr ""

#: internal/service/cli.go:139
#: internal/service/cli.go:254
msgid "Failed to generate password: %v"
msgstr ""

#: internal/service/cli.go:828
msgid "Failed to get app: %v"
msgstr ""

#: internal/service/cli.go:159
msgid "Failed to get entrance"
msgstr ""

#: internal/service/cli.go:102
msgid "Failed to get latest version: %v"
msgstr ""

#: internal/service/cli.go:155
msgid "Failed to get port"
msgstr ""

#: internal/service/cli.go:891
#: internal/service/cli.go:908
msgid "Failed to get setting: %v"
msgstr ""

#: internal/service/cli.go:133
msgid "Failed to get user info: %v"
msgstr ""

#: internal/service/cli.go:195
msgid "Failed to get user list: %v"
msgstr ""

#: internal/service/cli.go:220
#: internal/service/cli.go:248
#: internal/service/cli.go:276
msgid "Failed to get user: %v"
msgstr ""

#: internal/service/cli.go:299
msgid "Failed to read input: %v"
msgstr ""

#: internal/service/cli.go:835
msgid "Failed to save app: %v"
msgstr ""

#: internal/service/cli.go:914
msgid "Failed to save setting: %v"
msgstr ""

#: internal/service/cli.go:145
msgid "Failed to save user info: %v"
msgstr ""

#: internal/service/cli.go:115
msgid "Failed to synchronize app data: %v"
msgstr ""

#: internal/service/cli.go:118
msgid "Failed to synchronize rewrite rules: %v"
msgstr ""

#: internal/service/cli.go:302
msgid "Failed to update 2FA: %v"
msgstr ""

#: internal/apps/php/app.go:288
msgid "Fileinfo is a library used to identify file types"
msgstr ""

#: internal/data/backup.go:593
msgid "Files are normal and do not need to be repaired, please run panel-cli update to update the panel"
msgstr ""

#: internal/route/cli.go:51
msgid "Fix panel"
msgstr ""

#: internal/bootstrap/cli.go:34
msgid "Forum：https://bbs.haozi.net"
msgstr ""

#: internal/apps/mysql/app.go:127
msgid "Full Joins without Index"
msgstr ""

#: internal/apps/mysql/app.go:128
msgid "Full Range Joins without Index"
msgstr ""

#: internal/bootstrap/cli.go:20
msgid "GLOBAL OPTIONS"
msgstr ""

#: internal/apps/php/app.go:398
msgid "GMP is a library for handling large integers"
msgstr ""

#: internal/service/cert.go:84
msgid "Gcore"
msgstr ""

#: internal/route/cli.go:101
msgid "Generate HTTPS certificate"
msgstr ""

#: internal/route/cli.go:474
msgid "Get panel setting (use only under guidance)"
msgstr ""

#: internal/apps/php/app.go:418
msgid "Gettext is a library for handling multilingual support"
msgstr ""

#: internal/service/cert.go:80
msgid "Godaddy"
msgstr ""

#: internal/service/cli.go:385
msgid "HTTPS certificate generated"
msgstr ""

#: internal/service/cli.go:354
msgid "HTTPS disabled"
msgstr ""

#: internal/service/cli.go:329
msgid "HTTPS enabled"
msgstr ""

#: internal/service/cert.go:112
msgid "Hetzner"
msgstr ""

#: internal/service/cert.go:68
msgid "Huawei Cloud"
msgstr ""

#: internal/service/cli.go:199
msgid "ID: %d, Username: %s, Email: %s, Created At: %s"
msgstr ""

#: internal/apps/php/app.go:343
msgid "IMAP extension allows PHP to read, search, delete, download, and manage emails"
msgstr ""

#: internal/apps/php/app.go:135
msgid "Idle Processes"
msgstr ""

#: internal/service/cli.go:185
msgid "If you cannot access, please check whether the server's security group and firewall allow port %s"
msgstr ""

#: internal/service/cli.go:186
msgid "If you still cannot access, try running panel-cli https off to turn off panel HTTPS"
msgstr ""

#: internal/apps/php/app.go:298
msgid "Igbinary is a library for serializing and deserializing data"
msgstr ""

#: internal/apps/php/app.go:313
msgid "ImageMagick is free software for creating, editing, and composing images"
msgstr ""

#: internal/apps/mysql/app.go:123
msgid "Index Hit Rate"
msgstr ""

#: internal/service/cli.go:951
#: internal/service/cli.go:956
#: internal/service/cli.go:961
#: internal/service/cli.go:965
msgid "Initialization failed: %v"
msgstr ""

#: internal/route/cli.go:505
msgid "Initialize panel (use only under guidance)"
msgstr ""

#: internal/apps/mysql/app.go:124
msgid "Innodb Index Hit Rate"
msgstr ""

#: internal/apps/php/app.go:240
msgid "Install PHP-%d %s extension"
msgstr ""

#: internal/data/app.go:191
msgid "Install app %s"
msgstr ""

#: internal/route/cli.go:440
msgid "Install application"
msgstr ""

#: internal/data/backup.go:515
#: internal/data/backup.go:541
msgid "Insufficient backup directory space"
msgstr ""

#: internal/apps/php/app.go:413
msgid "Intl is a library for handling internationalization and localization"
msgstr ""

#: internal/apps/redis/app.go:87
msgid "Keyspace Hits"
msgstr ""

#: internal/apps/redis/app.go:88
msgid "Keyspace Misses"
msgstr ""

#: internal/apps/php/app.go:378
msgid "LDAP is a protocol for accessing directory services"
msgstr ""

#: internal/apps/redis/app.go:89
msgid "Latest Fork Time (ms)"
msgstr ""

#: internal/service/cert.go:116
msgid "Linode"
msgstr ""

#: internal/route/cli.go:65
msgid "List all users"
msgstr ""

#: internal/route/cli.go:177
msgid "List of domains associated with the website"
msgstr ""

#: internal/route/cli.go:183
msgid "List of listening addresses associated with the website"
msgstr ""

#: internal/apps/php/app.go:132
msgid "Listen Queue"
msgstr ""

#: internal/apps/php/app.go:134
msgid "Listen Queue Length"
msgstr ""

#: internal/service/cli.go:169
msgid "Local IPv4: %s://%s:%s%s"
msgstr ""

#: internal/service/cli.go:173
msgid "Local IPv6: %s://[%s]:%s%s"
msgstr ""

#: internal/route/cli.go:381
msgid "Log rotation"
msgstr ""

#: internal/apps/php/app.go:138
msgid "Max Active Processes"
msgstr ""

#: internal/apps/php/app.go:139
msgid "Max Children Reached"
msgstr ""

#: internal/apps/php/app.go:133
msgid "Max Listen Queue"
msgstr ""

#: internal/apps/php/app.go:308
msgid "Memcached is a driver for connecting to Memcached servers"
msgstr ""

#: internal/apps/nginx/app.go:113
msgid "Memory"
msgstr ""

#: internal/apps/redis/app.go:83
msgid "Memory Fragmentation Ratio"
msgstr ""

#: internal/data/backup.go:653
msgid "Move panel config failed: %v"
msgstr ""

#: internal/data/backup.go:648
msgid "Move panel file failed: %v"
msgstr ""

#: internal/data/backup.go:658
msgid "Move panel-cli file failed: %v"
msgstr ""

#: internal/apps/mysql/app.go:88
msgid "MySQL root password is empty"
msgstr ""

#: internal/bootstrap/cli.go:14
#: internal/bootstrap/cli.go:22
#: internal/bootstrap/cli.go:27
msgid "NAME"
msgstr ""

#: internal/service/cert.go:100
msgid "Name.com"
msgstr ""

#: internal/service/cert.go:96
msgid "NameSilo"
msgstr ""

#: internal/service/cert.go:92
msgid "Namecheap"
msgstr ""

#: internal/service/cli.go:213
msgid "New username cannot be empty"
msgstr ""

#: internal/data/backup.go:621
msgid "No backup file found, unable to automatically repair"
msgstr ""

#: internal/service/cli.go:620
msgid "Not supported"
msgstr ""

#: internal/service/dashboard.go:200
#: internal/service/dashboard.go:201
msgid "Not used"
msgstr ""

#: internal/route/cli.go:367
msgid "Number of backups to keep"
msgstr ""

#: internal/route/cli.go:422
msgid "Number of logs to keep"
msgstr ""

#: internal/bootstrap/cli.go:26
#: internal/bootstrap/cli.go:31
msgid "OPTIONS"
msgstr ""

#: internal/apps/php/app.go:293
msgid "OPcache stores precompiled PHP script bytecode in shared memory to improve PHP performance"
msgstr ""

#: internal/service/cli.go:210
msgid "Old username cannot be empty"
msgstr ""

#: internal/apps/mysql/app.go:126
msgid "Open Tables"
msgstr ""

#: internal/route/cli.go:87
msgid "Operate panel HTTPS"
msgstr ""

#: internal/route/cli.go:135
msgid "Operate panel IP binding"
msgstr ""

#: internal/route/cli.go:146
msgid "Operate panel UA binding"
msgstr ""

#: internal/route/cli.go:108
msgid "Operate panel access entrance"
msgstr ""

#: internal/route/cli.go:124
msgid "Operate panel domain binding"
msgstr ""

#: internal/route/cli.go:61
msgid "Operate panel users"
msgstr ""

#: internal/route/cli.go:56
msgid "Output panel basic information and generate new password"
msgstr ""

#: internal/route/cli.go:193
msgid "PHP version used by the website (not used if not filled)"
msgstr ""

#: internal/service/cli.go:76
msgid "Panel service restarted"
msgstr ""

#: internal/service/cli.go:94
msgid "Panel service started"
msgstr ""

#: internal/service/cli.go:85
msgid "Panel service stopped"
msgstr ""

#: internal/service/cli.go:778
#: internal/service/cli.go:792
#: internal/service/cli.go:806
#: internal/service/cli.go:822
#: internal/service/cli.go:844
#: internal/service/cli.go:883
#: internal/service/cli.go:902
#: internal/service/cli.go:923
msgid "Parameters cannot be empty"
msgstr ""

#: internal/service/cli.go:261
msgid "Password for user %s changed successfully"
msgstr ""

#: internal/service/cli.go:241
msgid "Password length cannot be less than 6"
msgstr ""

#: internal/service/cli.go:163
msgid "Password: %s"
msgstr ""

#: internal/route/cli.go:189
msgid "Path where the website is hosted (default path if not filled)"
msgstr ""

#: internal/apps/mysql/app.go:122
msgid "Peak Connections"
msgstr ""

#: internal/apps/redis/app.go:82
msgid "Peak Memory Usage"
msgstr ""

#: internal/apps/php/app.go:303
msgid "PhpRedis connects to and operates on data in Redis databases (requires the igbinary extension installed above)"
msgstr ""

#: internal/service/cli.go:184
msgid "Please choose the appropriate address to access the panel based on your network situation"
msgstr ""

#: internal/service/cli.go:296
msgid "Please enter the 2FA code: "
msgstr ""

#: internal/service/cert.go:88
msgid "Porkbun"
msgstr ""

#: internal/service/cli.go:532
msgid "Port already in use"
msgstr ""

#: internal/service/cli.go:560
msgid "Port changed to %d"
msgstr ""

#: internal/service/cli.go:518
msgid "Port range error"
msgstr ""

#: internal/service/cli.go:164
msgid "Port: %s"
msgstr ""

#: internal/apps/postgresql/app.go:142
msgid "Process Count"
msgstr ""

#: internal/apps/php/app.go:129
msgid "Process Manager"
msgstr ""

#: internal/apps/postgresql/app.go:141
msgid "Process PID"
msgstr ""

#: internal/apps/php/app.go:388
msgid "Pspell is a spell-checking library"
msgstr ""

#: internal/service/cli.go:177
msgid "Public IPv4: %s://%s:%s%s"
msgstr ""

#: internal/service/cli.go:181
msgid "Public IPv6: %s://[%s]:%s%s"
msgstr ""

#: internal/bootstrap/cli.go:35
msgid "QQ Group：12370907"
msgstr ""

#: internal/service/dashboard.go:58
msgid "Rat Panel"
msgstr ""

#: internal/bootstrap/cli.go:39
msgid "Rat Panel CLI Tool"
msgstr ""

#: internal/apps/nginx/app.go:144
msgid "Reading"
msgstr ""

#: internal/apps/php/app.go:368
msgid "Readline is a library for processing text"
msgstr ""

#: internal/route/cli.go:461
msgid "Remove panel application mark (use only under guidance)"
msgstr ""

#: internal/data/backup.go:645
msgid "Remove panel file failed: %v"
msgstr ""

#: internal/route/cli.go:486
msgid "Remove panel setting (use only under guidance)"
msgstr ""

#: internal/route/cli.go:199
msgid "Remove website"
msgstr ""

#: internal/route/cli.go:26
msgid "Restart panel service"
msgstr ""

#: internal/apps/mysql/app.go:118
msgid "Rollbacks per Second"
msgstr ""

#: internal/route/cli.go:428
msgid "Rotation directory (default path if not filled)"
msgstr ""

#: internal/route/cli.go:416
msgid "Rotation file"
msgstr ""

#: internal/route/cli.go:410
msgid "Rotation type"
msgstr ""

#: internal/apps/php/app.go:373
msgid "SNMP is a protocol for network management"
msgstr ""

#: internal/apps/php/app.go:358
msgid "SSH2 is a library for connecting to SSH servers"
msgstr ""

#: internal/route/cli.go:307
#: internal/route/cli.go:331
#: internal/route/cli.go:343
#: internal/route/cli.go:398
msgid "Save directory (default path if not filled)"
msgstr ""

#: internal/route/cli.go:252
msgid "Server address"
msgstr ""

#: internal/route/cli.go:247
#: internal/route/cli.go:281
msgid "Server name"
msgstr ""

#: internal/route/cli.go:266
msgid "Server password"
msgstr ""

#: internal/route/cli.go:257
msgid "Server port"
msgstr ""

#: internal/route/cli.go:270
msgid "Server remark"
msgstr ""

#: internal/route/cli.go:242
msgid "Server type"
msgstr ""

#: internal/route/cli.go:262
msgid "Server username"
msgstr ""

#: internal/route/cli.go:469
msgid "Setting management"
msgstr ""

#: internal/service/cli.go:889
msgid "Setting not exists"
msgstr ""

#: internal/apps/php/app.go:140
msgid "Slow Requests"
msgstr ""

#: internal/apps/mysql/app.go:130
msgid "Sort Merge Passes"
msgstr ""

#: internal/apps/php/app.go:130
#: internal/apps/postgresql/app.go:140
msgid "Start Time"
msgstr ""

#: internal/route/cli.go:36
msgid "Start panel service"
msgstr ""

#: internal/route/cli.go:31
msgid "Stop panel service"
msgstr ""

#: internal/apps/postgresql/app.go:144
msgid "Storage Usage"
msgstr ""

#: internal/apps/mysql/app.go:129
msgid "Subqueries without Index"
msgstr ""

#: internal/apps/php/app.go:478
msgid "Swoole is a PHP extension for building high-performance asynchronous concurrent servers"
msgstr ""

#: internal/apps/php/app.go:487
msgid "Swow is a PHP extension for building high-performance asynchronous concurrent servers"
msgstr ""

#: internal/route/cli.go:46
msgid "Sync panel data"
msgstr ""

#: internal/route/cli.go:494
msgid "Sync system time"
msgstr ""

#: internal/apps/php/app.go:458
msgid "Sysvmsg is a library for handling System V message queues"
msgstr ""

#: internal/apps/php/app.go:463
msgid "Sysvsem is a library for handling System V semaphores"
msgstr ""

#: internal/apps/php/app.go:468
msgid "Sysvshm is a library for handling System V shared memory"
msgstr ""

#: internal/apps/redis/app.go:77
msgid "TCP Port"
msgstr ""

#: internal/apps/mysql/app.go:131
msgid "Table Locks Waited"
msgstr ""

#: internal/service/cli.go:876
msgid "Tasks cleared successfully"
msgstr ""

#: internal/apps/mysql/app.go:125
msgid "Temporary Tables Created on Disk"
msgstr ""

#: internal/data/backup.go:751
msgid "Temporary file detected in /tmp, this may be caused by the last update failure, please run panel-cli fix to repair and try again"
msgstr ""

#: internal/service/cert.go:64
msgid "Tencent Cloud"
msgstr ""

#: internal/data/backup.go:561
msgid "The number of files contained in the compressed file is not 1, actual %d"
msgstr ""

#: internal/service/cli.go:864
msgid "Time synchronized successfully"
msgstr ""

#: internal/apps/redis/app.go:80
msgid "Total Allocated Memory"
msgstr ""

#: internal/apps/redis/app.go:85
msgid "Total Commands Processed"
msgstr ""

#: internal/apps/mysql/app.go:116
#: internal/apps/postgresql/app.go:143
msgid "Total Connections"
msgstr ""

#: internal/apps/redis/app.go:84
msgid "Total Connections Received"
msgstr ""

#: internal/apps/redis/app.go:81
msgid "Total Memory Usage"
msgstr ""

#: internal/apps/php/app.go:137
msgid "Total Processes"
msgstr ""

#: internal/apps/mysql/app.go:115
msgid "Total Queries"
msgstr ""

#: internal/apps/nginx/app.go:128
msgid "Total connections"
msgstr ""

#: internal/apps/nginx/app.go:132
msgid "Total handshakes"
msgstr ""

#: internal/apps/nginx/app.go:136
msgid "Total requests"
msgstr ""

#: internal/apps/mysql/app.go:117
msgid "Transactions per Second"
msgstr ""

#: internal/bootstrap/cli.go:15
#: internal/bootstrap/cli.go:23
#: internal/bootstrap/cli.go:28
#: internal/bootstrap/cli.go:29
msgid "USAGE"
msgstr ""

#: internal/service/app.go:173
msgid "Unable to update app list cache in offline mode"
msgstr ""

#: internal/apps/php/app.go:271
msgid "Uninstall PHP-%d %s extension"
msgstr ""

#: internal/data/app.go:246
msgid "Uninstall app %s"
msgstr ""

#: internal/route/cli.go:445
msgid "Uninstall application"
msgstr ""

#: internal/data/backup.go:636
msgid "Unzip backup file failed: %v"
msgstr ""

#: internal/data/backup.go:668
msgid "Unzip panel data failed: %v"
msgstr ""

#: internal/data/app.go:301
msgid "Update app %s"
msgstr ""

#: internal/route/cli.go:450
msgid "Update application"
msgstr ""

#: internal/route/cli.go:41
msgid "Update panel"
msgstr ""

#: internal/apps/mysql/app.go:114
msgid "Uptime"
msgstr ""

#: internal/apps/redis/app.go:78
msgid "Uptime in Days"
msgstr ""

#: internal/service/cli.go:218
#: internal/service/cli.go:246
#: internal/service/cli.go:274
msgid "User not exists"
msgstr ""

#: internal/service/cli.go:229
msgid "Username %s changed to %s successfully"
msgstr ""

#: internal/service/cli.go:238
msgid "Username and password cannot be empty"
msgstr ""

#: internal/service/cli.go:269
msgid "Username cannot be empty"
msgstr ""

#: internal/service/cli.go:162
msgid "Username: %s"
msgstr ""

#: internal/bootstrap/cli.go:16
msgid "VERSION"
msgstr ""

#: internal/service/cert.go:120
msgid "Vercel"
msgstr ""

#: internal/data/backup.go:744
msgid "Verify download file failed: %v"
msgstr ""

#: internal/apps/nginx/app.go:152
msgid "Waiting"
msgstr ""

#: internal/service/cli.go:187
msgid "Warning: After turning off panel HTTPS, the security of the panel will be greatly reduced, please operate with caution"
msgstr ""

#: internal/route/cli.go:385
msgid "Website"
msgstr ""

#: internal/service/cli.go:579
msgid "Website %s created successfully"
msgstr ""

#: internal/service/cli.go:615
msgid "Website %s deleted successfully"
msgstr ""

#: internal/service/cli.go:596
msgid "Website %s removed successfully"
msgstr ""

#: internal/route/cli.go:162
msgid "Website management"
msgstr ""

#: internal/route/cli.go:171
#: internal/route/cli.go:204
#: internal/route/cli.go:217
#: internal/route/cli.go:301
#: internal/route/cli.go:391
msgid "Website name"
msgstr ""

#: internal/bootstrap/cli.go:33
msgid "Website：https://panel.haozi.net"
msgstr ""

#: internal/service/cert.go:72
msgid "West.cn"
msgstr ""

#: internal/apps/nginx/app.go:102
msgid "Workers"
msgstr ""

#: internal/route/cli.go:480
msgid "Write panel setting (use only under guidance)"
msgstr ""

#: internal/route/cli.go:225
msgid "Write website data (use only under guidance)"
msgstr ""

#: internal/apps/nginx/app.go:148
msgid "Writing"
msgstr ""

#: internal/apps/php/app.go:403
msgid "XLSWriter is a high-performance library for reading and writing Excel files"
msgstr ""

#: internal/apps/php/app.go:408
msgid "XSL is a library for processing XML documents"
msgstr ""

#: internal/apps/php/app.go:348
msgid "Zip is a library for handling ZIP files"
msgstr ""

#: internal/data/app.go:159
msgid "app %s already installed"
msgstr ""

#: internal/data/app.go:56
msgid "app %s not found"
msgstr ""

#: internal/data/app.go:210
#: internal/data/app.go:265
#: internal/http/middleware/must_install.go:48
msgid "app %s not installed"
msgstr ""

#: internal/data/app.go:179
#: internal/data/app.go:289
msgid "app %s not support current panel version"
msgstr ""

#: internal/data/app.go:170
#: internal/data/app.go:225
#: internal/data/app.go:280
msgid "app %s requires panel version %s, current version %s"
msgstr ""

#: internal/http/middleware/must_install.go:29
msgid "app not found"
msgstr ""

#: internal/data/setting.go:282
#: internal/data/setting.go:343
#: internal/data/setting.go:356
msgid "background task is running, modifying some settings is prohibited, please try again later"
msgstr ""

#: internal/service/dashboard.go:339
msgid "background task is running, restart is prohibited, please try again later"
msgstr ""

#: internal/service/dashboard.go:307
msgid "background task is running, updating is prohibited, please try again later"
msgstr ""

#: internal/data/backup.go:383
#: internal/data/backup.go:410
#: internal/data/backup.go:456
msgid "backup file %s not exists"
msgstr ""

#: internal/service/file.go:277
msgid "can't download a directory"
msgstr ""

#: internal/data/website.go:371
msgid "can't find %s database server, please add it first"
msgstr ""

#: internal/data/database_server.go:106
#: internal/data/database_server.go:86
msgid "check server connection failed"
msgstr ""

#: internal/http/middleware/must_login.go:93
msgid "client ip/ua changed, please login again"
msgstr ""

#: internal/data/backup.go:569
msgid "could not find .sql backup file"
msgstr ""

#: internal/service/file.go:166
msgid "create directory error: %v"
msgstr ""

#: internal/data/cron.go:95
msgid "cron directory %s not exists"
msgstr ""

#: internal/data/cron.go:98
msgid "cron log directory %s not exists"
msgstr ""

#: internal/data/backup.go:263
#: internal/data/backup.go:309
#: internal/data/backup.go:425
#: internal/data/backup.go:467
msgid "database does not exist: %s"
msgstr ""

#: internal/data/website.go:725
msgid "default document comment count is incorrect, expected 1, actual %d"
msgstr ""

#: internal/data/website.go:722
msgid "default document comment not found"
msgstr ""

#: internal/apps/pureftpd/app.go:84
msgid "directory %s does not exist"
msgstr ""

#: internal/service/toolbox_system.go:139
msgid "disk space is insufficient, current free %s"
msgstr ""

#: internal/data/task.go:65
msgid "duplicate submission, please wait for the previous task to end"
msgstr ""

#: internal/apps/s3fs/app.go:62
msgid "endpoint should not contain bucket"
msgstr ""

#: internal/service/user_token.go:54
#: internal/service/user_token.go:89
msgid "expiration time must be greater than current time"
msgstr ""

#: internal/service/user_token.go:58
#: internal/service/user_token.go:93
msgid "expiration time must be less than 10 years"
msgstr ""

#: internal/apps/php/app.go:229
#: internal/apps/php/app.go:260
msgid "extension %s does not exist"
msgstr ""

#: internal/data/ssh.go:53
#: internal/data/ssh.go:77
msgid "failed to check ssh connection: %v"
msgstr ""

#: internal/data/backup.go:603
msgid "failed to clean temporary files: %v"
msgstr ""

#: internal/apps/s3fs/app.go:69
msgid "failed to create mount path: %v"
msgstr ""

#: internal/apps/s3fs/app.go:94
msgid "failed to create passwd file: %v"
msgstr ""

#: internal/service/systemctl.go:77
msgid "failed to disable %s service: %v"
msgstr ""

#: internal/service/systemctl.go:62
msgid "failed to enable %s service: %v"
msgstr ""

#: internal/service/systemctl.go:47
msgid "failed to get %s service enable status: %v"
msgstr ""

#: internal/service/systemctl.go:31
msgid "failed to get %s service running status: %v"
msgstr ""

#: internal/data/website.go:306
msgid "failed to get 404 template file: %v"
msgstr ""

#: internal/data/cert_account.go:202
msgid "failed to get Google EAB: %s"
msgstr ""

#: internal/data/cert_account.go:198
msgid "failed to get Google EAB: %v"
msgstr ""

#: internal/apps/memcached/app.go:37
msgid "failed to get Memcached status: %v"
msgstr ""

#: internal/apps/mysql/app.go:104
msgid "failed to get MySQL status: %v"
msgstr ""

#: internal/apps/postgresql/app.go:120
msgid "failed to get PostgreSQL backend pid: %v"
msgstr ""

#: internal/apps/postgresql/app.go:130
msgid "failed to get PostgreSQL connections: %v"
msgstr ""

#: internal/apps/postgresql/app.go:135
msgid "failed to get PostgreSQL database size: %v"
msgstr ""

#: internal/apps/postgresql/app.go:125
msgid "failed to get PostgreSQL process: %v"
msgstr ""

#: internal/apps/postgresql/app.go:115
msgid "failed to get PostgreSQL start time: %v"
msgstr ""

#: internal/service/toolbox_system.go:77
#: internal/service/toolbox_system.go:90
msgid "failed to get SWAP: %v"
msgstr ""

#: internal/data/cert_account.go:227
msgid "failed to get ZeroSSL EAB"
msgstr ""

#: internal/data/cert_account.go:223
msgid "failed to get ZeroSSL EAB: %v"
msgstr ""

#: internal/service/toolbox_system.go:191
msgid "failed to get available timezones: %v"
msgstr ""

#: internal/apps/fail2ban/app.go:268
msgid "failed to get banned ip list"
msgstr ""

#: internal/apps/fail2ban/app.go:258
msgid "failed to get current banned list"
msgstr ""

#: internal/service/toolbox_system.go:135
msgid "failed to get disk space: %v"
msgstr ""

#: internal/service/dashboard.go:70
msgid "failed to get home apps: %v"
msgstr ""

#: internal/data/website.go:294
msgid "failed to get index template file: %v"
msgstr ""

#: internal/apps/supervisor/app.go:218
#: internal/apps/supervisor/app.go:241
#: internal/apps/supervisor/app.go:358
#: internal/apps/supervisor/app.go:368
msgid "failed to get log path for process %s: %v"
msgstr ""

#: internal/apps/nginx/app.go:108
#: internal/apps/nginx/app.go:98
msgid "failed to get nginx workers: %v"
msgstr ""

#: internal/apps/pureftpd/app.go:152
msgid "failed to get port: %v"
msgstr ""

#: internal/data/cert_account.go:113
msgid "failed to get private key"
msgstr ""

#: internal/data/cert_account.go:172
msgid "failed to get private key: %v"
msgstr ""

#: internal/apps/redis/app.go:62
msgid "failed to get redis info: %v"
msgstr ""

#: internal/apps/redis/app.go:39
msgid "failed to get redis status: %v"
msgstr ""

#: internal/apps/s3fs/app.go:125
#: internal/apps/s3fs/app.go:40
#: internal/apps/s3fs/app.go:80
msgid "failed to get s3fs list: %v"
msgstr ""

#: internal/service/dashboard.go:90
msgid "failed to get system info: %v"
msgstr ""

#: internal/service/dashboard.go:320
msgid "failed to get the latest version download link"
msgstr ""

#: internal/service/dashboard.go:236
#: internal/service/dashboard.go:272
#: internal/service/dashboard.go:314
msgid "failed to get the latest version: %v"
msgstr ""

#: internal/apps/rsync/app.go:82
msgid "failed to get the secret key for module %s"
msgstr ""

#: internal/service/dashboard.go:136
msgid "failed to get the total number of websites: %v"
msgstr ""

#: internal/service/dashboard.go:293
msgid "failed to get the update information: %v"
msgstr ""

#: internal/service/toolbox_system.go:180
msgid "failed to get timezone: %v"
msgstr ""

#: internal/apps/fail2ban/app.go:263
msgid "failed to get total banned list"
msgstr ""

#: internal/data/app.go:234
msgid "failed to get uninstall script for app %s"
msgstr ""

#: internal/apps/mysql/app.go:188
#: internal/apps/mysql/app.go:83
msgid "failed to load MySQL root password: %v"
msgstr ""

#: internal/data/cert.go:92
#: internal/data/setting.go:287
#: internal/data/setting.go:359
#: internal/data/website.go:475
#: internal/data/website.go:764
msgid "failed to parse certificate: %v"
msgstr ""

#: internal/data/cert.go:95
#: internal/data/setting.go:290
#: internal/data/setting.go:362
#: internal/data/website.go:478
#: internal/data/website.go:767
msgid "failed to parse private key: %v"
msgstr ""

#: internal/apps/fail2ban/app.go:327
#: internal/apps/fail2ban/app.go:355
msgid "failed to parse the ignoreip of fail2ban"
msgstr ""

#: internal/service/dashboard.go:242
#: internal/service/dashboard.go:247
#: internal/service/dashboard.go:278
#: internal/service/dashboard.go:283
msgid "failed to parse version: %v"
msgstr ""

#: internal/service/ws.go:122
msgid "failed to read command output: %v"
msgstr ""

#: internal/service/ws.go:103
msgid "failed to read command: %v"
msgstr ""

#: internal/apps/memcached/app.go:77
msgid "failed to read from Memcached: %v"
msgstr ""

#: internal/data/cert_account.go:108
#: internal/data/cert_account.go:167
msgid "failed to register account: %v"
msgstr ""

#: internal/service/systemctl.go:107
msgid "failed to reload %s service: %v"
msgstr ""

#: internal/apps/postgresql/app.go:65
#: internal/apps/postgresql/app.go:98
msgid "failed to reload PostgreSQL: %v"
msgstr ""

#: internal/apps/nginx/app.go:65
#: internal/apps/phpmyadmin/app.go:108
#: internal/apps/phpmyadmin/app.go:139
msgid "failed to reload nginx: %v"
msgstr ""

#: internal/service/systemctl.go:92
msgid "failed to restart %s service: %v"
msgstr ""

#: internal/apps/supervisor/app.go:108
msgid "failed to restart %s: %v"
msgstr ""

#: internal/apps/mysql/app.go:72
msgid "failed to restart MySQL: %v"
msgstr ""

#: internal/service/ws.go:110
msgid "failed to run command: %v"
msgstr ""

#: internal/apps/mysql/app.go:99
msgid "failed to set MYSQL_PWD env: %v"
msgstr ""

#: internal/service/toolbox_system.go:159
msgid "failed to set SWAP permission: %v"
msgstr ""

#: internal/service/toolbox_system.go:276
msgid "failed to set hostname: %v"
msgstr ""

#: internal/service/toolbox_system.go:299
msgid "failed to set hosts: %v"
msgstr ""

#: internal/service/toolbox_system.go:316
msgid "failed to set root password: %v"
msgstr ""

#: internal/service/systemctl.go:122
msgid "failed to start %s service: %v"
msgstr ""

#: internal/service/systemctl.go:137
msgid "failed to stop %s service: %v"
msgstr ""

#: internal/apps/s3fs/app.go:145
msgid "failed to unmount: %v"
msgstr ""

#: internal/service/toolbox_system.go:63
msgid "failed to update DNS: %v"
msgstr ""

#: internal/apps/memcached/app.go:56
msgid "failed to write to Memcached: %v"
msgstr ""

#: internal/service/file.go:84
msgid "file is too large, please download it to view"
msgstr ""

#: internal/apps/php/app.go:423
msgid "gRPC is a high-performance, open-source, and general-purpose RPC framework"
msgstr ""

#: internal/apps/fail2ban/app.go:184
msgid "get service port failed, please check if it is installed"
msgstr ""

#: internal/data/user.go:175
#: internal/data/user.go:198
#: internal/service/user.go:102
msgid "invalid 2FA code"
msgstr ""

#: internal/http/middleware/entrance.go:114
msgid "invalid access entrance"
msgstr ""

#: internal/data/user_token.go:101
msgid "invalid header: %v"
msgstr ""

#: internal/service/user.go:84
msgid "invalid key, please refresh the page"
msgstr ""

#: internal/http/middleware/entrance.go:57
msgid "invalid request domain: %s"
msgstr ""

#: internal/data/user_token.go:148
#: internal/http/middleware/entrance.go:70
msgid "invalid request ip: %s"
msgstr ""

#: internal/http/middleware/entrance.go:79
msgid "invalid request user agent: %s"
msgstr ""

#: internal/data/user_token.go:104
#: internal/data/user_token.go:110
#: internal/data/user_token.go:133
msgid "invalid signature"
msgstr ""

#: internal/http/middleware/must_login.go:107
msgid "invalid user id, please login again"
msgstr ""

#: internal/apps/php/app.go:473
msgid "ionCube is a professional-grade PHP encryption and decryption tool (must be installed after OPcache)"
msgstr ""

#: internal/data/backup.go:139
msgid "log file %s not exists"
msgstr ""

#: internal/apps/rsync/app.go:117
msgid "module %s already exists"
msgstr ""

#: internal/apps/rsync/app.go:162
#: internal/apps/rsync/app.go:204
msgid "module %s does not exist"
msgstr ""

#: internal/apps/s3fs/app.go:108
msgid "mount failed: %v"
msgstr ""

#: internal/apps/s3fs/app.go:137
msgid "mount not found"
msgstr ""

#: internal/apps/s3fs/app.go:86
msgid "mount path already exists"
msgstr ""

#: internal/apps/s3fs/app.go:74
msgid "mount path is not empty"
msgstr ""

#: internal/data/database.go:185
msgid "mysql not support database comment"
msgstr ""

#: internal/data/website.go:795
msgid "not support one-key obtain wildcard certificate, please use Cert menu to obtain it with DNS method"
msgstr ""

#: internal/service/file.go:174
msgid "open file error: %v"
msgstr ""

#: internal/http/middleware/status.go:38
msgid "panel is closed"
msgstr ""

#: internal/http/middleware/status.go:30
msgid "panel is maintaining, please refresh later"
msgstr ""

#: internal/http/middleware/status.go:22
msgid "panel is upgrading, please refresh later"
msgstr ""

#: internal/http/middleware/status.go:46
msgid "panel run error, please check or contact support"
msgstr ""

#: internal/apps/php/app.go:328
msgid "pdo_pgsql is a PDO driver for connecting to PostgreSQL (requires PostgreSQL installed)"
msgstr ""

#: internal/apps/php/app.go:338
msgid "pdo_sqlsrv is a PDO driver for connecting to SQL Server"
msgstr ""

#: internal/apps/php/app.go:323
msgid "pgsql is a driver for connecting to PostgreSQL (requires PostgreSQL installed)"
msgstr ""

#: internal/apps/phpmyadmin/app.go:43
#: internal/apps/phpmyadmin/app.go:54
msgid "phpMyAdmin directory not found"
msgstr ""

#: internal/apps/phpmyadmin/app.go:65
msgid "phpMyAdmin port not found"
msgstr ""

#: internal/data/user.go:97
#: internal/service/file.go:135
#: internal/service/file.go:222
#: internal/service/file.go:251
msgid "please don't do this"
msgstr ""

#: internal/data/cert.go:210
msgid "please retry the manual obtain operation"
msgstr ""

#: internal/data/setting.go:311
msgid "port is already in use"
msgstr ""

#: internal/apps/php/app.go:428
msgid "protobuf is a library for serializing and deserializing data"
msgstr ""

#: internal/apps/php/app.go:433
msgid "rdkafka is a library for connecting to Apache Kafka"
msgstr ""

#: internal/apps/fail2ban/app.go:109
msgid "rule already exists"
msgstr ""

#: internal/apps/fail2ban/app.go:228
msgid "rule not found"
msgstr ""

#: internal/data/website.go:712
msgid "runtime directory comment count is incorrect, expected 1, actual %d"
msgstr ""

#: internal/data/website.go:709
msgid "runtime directory comment not found"
msgstr ""

#: internal/data/website.go:449
#: internal/data/website.go:716
msgid "runtime directory does not exist"
msgstr ""

#: internal/http/middleware/must_login.go:78
msgid "session expired, please login again"
msgstr ""

#: internal/data/user_token.go:138
msgid "signature expired"
msgstr ""

#: internal/apps/php/app.go:333
msgid "sqlsrv is a driver for connecting to SQL Server"
msgstr ""

#: internal/data/database_server.go:161
#: internal/data/database_server.go:187
msgid "sync from server %s"
msgstr ""

#: internal/service/backup.go:87
msgid "target backup %s already exists"
msgstr ""

#: internal/service/file.go:80
msgid "target is a directory"
msgstr ""

#: internal/service/file.go:160
msgid "target path %s already exists"
msgstr ""

#: internal/service/dashboard.go:287
msgid "the current version is the latest version"
msgstr ""

#: internal/data/cert.go:351
msgid "this certificate has not been obtained successfully and cannot be deployed"
msgstr ""

#: internal/data/cert.go:276
msgid "this certificate has not been obtained successfully and cannot be renewed"
msgstr ""

#: internal/data/cert.go:168
#: internal/data/cert.go:283
msgid "this certificate is not associated with a website and cannot be obtained. You can try to obtain it manually"
msgstr ""

#: internal/data/cert.go:406
msgid "this certificate is not associated with an ACME account and cannot be obtained"
msgstr ""

#: internal/data/user_token.go:113
msgid "token expired"
msgstr ""

#: internal/service/dashboard.go:228
#: internal/service/dashboard.go:264
msgid "unable to check for updates in offline mode"
msgstr ""

#: internal/service/dashboard.go:302
msgid "unable to update in offline mode"
msgstr ""

#: internal/data/backup.go:131
#: internal/data/backup.go:212
#: internal/data/backup.go:95
msgid "unknown backup type"
msgstr ""

#: internal/apps/fail2ban/app.go:180
msgid "unknown service"
msgstr ""

#: internal/service/toolbox_benchmark.go:77
msgid "unknown test type"
msgstr ""

#: internal/data/cert_account.go:104
#: internal/data/cert_account.go:163
msgid "unsupported CA"
msgstr ""

#: internal/service/backup.go:77
msgid "unsupported file type"
msgstr ""

#: internal/data/cron.go:220
msgid "unsupported system"
msgstr ""

#: internal/data/cert.go:132
msgid "upload certificate cannot be set to auto renew"
msgstr ""

#: internal/service/file.go:156
msgid "upload file error: %v"
msgstr ""

#: internal/data/user.go:117
#: internal/data/user.go:124
#: internal/data/user.go:134
msgid "username or password error"
msgstr ""

#: internal/data/website.go:560
msgid "website %s has bound certificates, please delete the certificate first"
msgstr ""

#: internal/data/website.go:456
msgid "website directory does not exist"
msgstr ""

#: internal/data/cert.go:172
#: internal/data/cert.go:287
msgid "wildcard domains cannot use HTTP verification"
msgstr ""

#: internal/service/file.go:179
msgid "write file error: %v"
msgstr ""

#: internal/http/middleware/must_login.go:58
msgid "ws not allowed"
msgstr ""

#: internal/apps/php/app.go:443
msgid "xdebug is a library for debugging and profiling PHP code"
msgstr ""

#: internal/apps/php/app.go:438
msgid "xhprof is a library for performance profiling"
msgstr ""

#: internal/apps/php/app.go:448
msgid "yaml is a library for handling YAML"
msgstr ""

#: internal/apps/php/app.go:453
msgid "zstd is a library for compressing and decompressing files"
msgstr ""

#: internal/data/backup.go:244
#: internal/data/backup.go:294
#: internal/data/backup.go:334
#: internal/data/backup.go:374
msgid "|-Backed up to file: %s"
msgstr ""

#: internal/data/backup.go:511
#: internal/data/backup.go:537
msgid "|-Backup directory available Inode: %d"
msgstr ""

#: internal/data/backup.go:510
#: internal/data/backup.go:536
msgid "|-Backup directory available space: %s"
msgstr ""

#: internal/data/backup.go:625
msgid "|-Backup file used: %s"
msgstr ""

#: internal/data/backup.go:765
msgid "|-Backup panel data failed, missing file"
msgstr ""

#: internal/data/backup.go:759
#: internal/data/backup.go:762
msgid "|-Backup panel data failed: %v"
msgstr ""

#: internal/data/backup.go:755
msgid "|-Backup panel data..."
msgstr ""

#: internal/service/cli.go:662
#: internal/service/cli.go:678
msgid "|-Backup target: %s"
msgstr ""

#: internal/data/backup.go:243
#: internal/data/backup.go:293
#: internal/data/backup.go:333
#: internal/data/backup.go:373
msgid "|-Backup time: %s"
msgstr ""

#: internal/service/cli.go:676
msgid "|-Backup type: database"
msgstr ""

#: internal/service/cli.go:692
msgid "|-Backup type: panel"
msgstr ""

#: internal/service/cli.go:661
msgid "|-Backup type: website"
msgstr ""

#: internal/data/backup.go:785
msgid "|-Clean up temporary file failed: %v"
msgstr ""

#: internal/data/backup.go:747
msgid "|-Clean up verification file failed: %v"
msgstr ""

#: internal/data/backup.go:606
msgid "|-Cleaned up temporary files, please run panel-cli update to update the panel"
msgstr ""

#: internal/data/backup.go:195
msgid "|-Cleaning expired file: %s"
msgstr ""

#: internal/data/backup.go:772
msgid "|-Cleaning old version failed: %v"
msgstr ""

#: internal/data/backup.go:769
msgid "|-Cleaning old version..."
msgstr ""

#: internal/service/cli.go:715
#: internal/service/cli.go:763
msgid "|-Cleaning target: %s"
msgstr ""

#: internal/service/cli.go:714
#: internal/service/cli.go:762
msgid "|-Cleaning type: %s"
msgstr ""

#: internal/service/cli.go:677
msgid "|-Database: %s"
msgstr ""

#: internal/data/backup.go:723
msgid "|-Download link: %s"
msgstr ""

#: internal/data/backup.go:805
msgid "|-Download panel service file failed: %v"
msgstr ""

#: internal/data/backup.go:728
msgid "|-Downloading..."
msgstr ""

#: internal/data/backup.go:724
msgid "|-File name: %s"
msgstr ""

#: internal/data/backup.go:704
msgid "|-Fix completed"
msgstr ""

#: internal/service/cli.go:716
#: internal/service/cli.go:764
msgid "|-Keep count: %d"
msgstr ""

#: internal/data/backup.go:641
msgid "|-Move backup file..."
msgstr ""

#: internal/data/backup.go:811
msgid "|-Move panel-cli tool failed: %v"
msgstr ""

#: internal/data/backup.go:795
msgid "|-Restore panel data failed, missing file"
msgstr ""

#: internal/data/backup.go:792
msgid "|-Restore panel data failed: %v"
msgstr ""

#: internal/data/backup.go:664
#: internal/data/backup.go:789
msgid "|-Restore panel data..."
msgstr ""

#: internal/service/cli.go:740
msgid "|-Rotation target: %s"
msgstr ""

#: internal/service/cli.go:739
msgid "|-Rotation type: website"
msgstr ""

#: internal/data/backup.go:802
msgid "|-Run post-update script failed: %v"
msgstr ""

#: internal/data/backup.go:799
msgid "|-Run post-update script..."
msgstr ""

#: internal/data/backup.go:684
#: internal/data/backup.go:815
msgid "|-Set key file permissions..."
msgstr ""

#: internal/data/backup.go:577
msgid "|-Start fixing the panel..."
msgstr ""

#: internal/data/backup.go:509
msgid "|-Target file count: %d"
msgstr ""

#: internal/data/backup.go:508
#: internal/data/backup.go:535
msgid "|-Target size: %s"
msgstr ""

#: internal/data/backup.go:722
msgid "|-Target version: %s"
msgstr ""

#: internal/data/backup.go:630
msgid "|-Unzip backup file..."
msgstr ""

#: internal/data/backup.go:782
msgid "|-Unzip new version failed, missing file"
msgstr ""

#: internal/data/backup.go:779
msgid "|-Unzip new version failed: %v"
msgstr ""

#: internal/data/backup.go:776
msgid "|-Unzip new version..."
msgstr ""

#: internal/data/backup.go:822
msgid "|-Update completed"
msgstr ""

#: internal/data/backup.go:741
msgid "|-Verify download file..."
msgstr ""

#: internal/data/backup.go:808
msgid "|-Write new panel version failed: %v"
msgstr ""

#: internal/service/cli.go:659
#: internal/service/cli.go:674
#: internal/service/cli.go:690
msgid "★ Start backup [%s]"
msgstr ""

#: internal/service/cli.go:712
msgid "★ Start cleaning [%s]"
msgstr ""

#: internal/service/cli.go:760
msgid "★ Start cleaning rotated logs [%s]"
msgstr ""

#: internal/service/cli.go:737
msgid "★ Start log rotation [%s]"
msgstr ""

#: internal/service/cli.go:667
#: internal/service/cli.go:683
#: internal/service/cli.go:697
msgid "☆ Backup successful [%s]"
msgstr ""

#: internal/service/cli.go:721
#: internal/service/cli.go:769
msgid "☆ Cleaning successful [%s]"
msgstr ""

#: internal/service/cli.go:745
msgid "☆ Rotation successful [%s]"
msgstr ""