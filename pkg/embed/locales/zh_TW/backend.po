msgid ""
msgstr ""
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_TW\n"
"X-Generator: xgotext\n"
"X-Crowdin-Project: ratpanel\n"
"X-Crowdin-Project-ID: 778640\n"
"X-Crowdin-Language: zh-TW\n"
"X-Crowdin-File: /main/pkg/embed/locales/backend.pot\n"
"X-Crowdin-File-ID: 922\n"
"Project-Id-Version: ratpanel\n"
"Language-Team: Chinese Traditional\n"
"PO-Revision-Date: 2025-05-18 18:01\n"

#: internal/data/website.go:270
#: internal/data/website.go:643
msgid "# Rewrite rule"
msgstr "# 重寫規則"

#: internal/service/cli.go:286
msgid "2FA disabled for user %s"
msgstr "用戶 %s 已禁用兩步驗證"

#: internal/service/cli.go:294
msgid "2FA url: %s"
msgstr "兩步驗證 URL： %s"

#: internal/bootstrap/cli.go:18
msgid "AUTHOR"
msgstr "作者"

#: internal/apps/php/app.go:131
msgid "Accepted Connections"
msgstr "已接受連接"

#: internal/apps/mysql/app.go:121
msgid "Active Connections"
msgstr "活動連接"

#: internal/apps/php/app.go:136
msgid "Active Processes"
msgstr "活動進程"

#: internal/apps/nginx/app.go:120
msgid "Active connections"
msgstr "活動連接"

#: internal/route/cli.go:237
msgid "Add database server"
msgstr "添加數據庫伺服器"

#: internal/route/cli.go:455
msgid "Add panel application mark (use only under guidance)"
msgstr "添加面板應用標記（僅在指導下使用）"

#: internal/service/cert.go:60
msgid "Aliyun"
msgstr "阿里雲"

#: internal/service/cli.go:936
msgid "Already initialized"
msgstr "已經初始化過了"

#: internal/data/app.go:347
msgid "App %s %s"
msgstr "應用 %s %s"

#: internal/service/cli.go:785
msgid "App %s installed successfully"
msgstr "應用程式 %s 安裝成功"

#: internal/service/cli.go:799
msgid "App %s uninstalled successfully"
msgstr "應用程式 %s 解除安裝成功"

#: internal/service/cli.go:813
msgid "App %s updated successfully"
msgstr "應用程式 %s 更新成功"

#: internal/service/cli.go:782
msgid "App install failed: %v"
msgstr "應用程式安裝失敗：%v"

#: internal/service/cli.go:796
msgid "App uninstall failed: %v"
msgstr "應用程式解除安裝失敗：%v"

#: internal/service/cli.go:810
msgid "App update failed: %v"
msgstr "應用程式更新失敗：%v"

#: internal/apps/php/app.go:128
msgid "Application Pool"
msgstr "應用程序池"

#: internal/route/cli.go:436
msgid "Application management"
msgstr "應用管理"

#: internal/route/cli.go:313
msgid "Backup database"
msgstr "備份數據庫"

#: internal/route/cli.go:373
msgid "Backup directory (default path if not filled)"
msgstr "備份目錄（不填則使用默認路徑）"

#: internal/service/cli.go:664
#: internal/service/cli.go:680
#: internal/service/cli.go:694
msgid "Backup failed: %v"
msgstr "備份失敗：%v"

#: internal/route/cli.go:361
msgid "Backup file"
msgstr "備份文件"

#: internal/route/cli.go:337
msgid "Backup panel"
msgstr "備份面板"

#: internal/route/cli.go:355
msgid "Backup type"
msgstr "備份類型"

#: internal/route/cli.go:295
msgid "Backup website"
msgstr "備份網站"

#: internal/service/cli.go:486
msgid "Bind IP disabled"
msgstr "繫結 IP 已禁用"

#: internal/service/cli.go:511
msgid "Bind UA disabled"
msgstr "繫結 UA 已禁用"

#: internal/service/cli.go:461
msgid "Bind domain disabled"
msgstr "繫結網域已禁用"

#: internal/apps/mysql/app.go:120
#: internal/apps/mysql/app.go:139
msgid "Bytes Received"
msgstr "已接收字節"

#: internal/apps/mysql/app.go:119
#: internal/apps/mysql/app.go:139
msgid "Bytes Sent"
msgstr "已發送字節"

#: internal/apps/php/app.go:353
msgid "Bzip2 is a library for compressing and decompressing files"
msgstr "Bzip2 是一個用於壓縮和解壓縮文件的庫"

#: internal/bootstrap/cli.go:24
msgid "CATEGORY"
msgstr "分類"

#: internal/bootstrap/cli.go:19
#: internal/bootstrap/cli.go:30
msgid "COMMANDS"
msgstr "命令"

#: internal/bootstrap/cli.go:21
msgid "COPYRIGHT"
msgstr "版權"

#: internal/apps/php/app.go:393
msgid "Calendar is a library for handling dates"
msgstr "Calendar 是一個用於處理日期的庫"

#: internal/route/cli.go:157
msgid "Change panel port"
msgstr "更改面板端口"

#: internal/route/cli.go:80
msgid "Change user 2FA"
msgstr "更改用戶兩步驗證"

#: internal/route/cli.go:75
msgid "Change user password"
msgstr "更改用戶密碼"

#: internal/route/cli.go:70
msgid "Change username"
msgstr "更改用戶名"

#: internal/service/cli.go:718
msgid "Cleaning failed: %v"
msgstr "清理失敗：%v"

#: internal/data/backup.go:633
msgid "Cleaning temporary directory failed: %v"
msgstr "清理臨時目錄失敗：%v"

#: internal/data/backup.go:671
msgid "Cleaning temporary file failed: %v"
msgstr "清理臨時檔案失敗：%v"

#: internal/data/backup.go:198
msgid "Cleanup failed: %v"
msgstr "清理失敗：%v"

#: internal/route/cli.go:349
msgid "Clear backups"
msgstr "清理備份"

#: internal/route/cli.go:499
msgid "Clear panel task queue (use only under guidance)"
msgstr "清理面板任務佇列（僅在指導下使用）"

#: internal/route/cli.go:404
msgid "Clear rotated logs"
msgstr "清除旋轉日誌"

#: internal/service/cert.go:104
msgid "ClouDNS"
msgstr "ClouDNS"

#: internal/service/cert.go:76
msgid "CloudFlare"
msgstr "CloudFlare"

#: internal/apps/redis/app.go:86
msgid "Commands Per Second"
msgstr "每秒執行命令數"

#: internal/apps/redis/app.go:79
msgid "Connected Clients"
msgstr "已連接客戶端數"

#: internal/route/cli.go:166
msgid "Create new website"
msgstr "創建新網站"

#: internal/service/cli.go:752
msgid "Currently only website log rotation is supported"
msgstr "目前僅支援網站日誌輪替"

#: internal/bootstrap/cli.go:17
#: internal/bootstrap/cli.go:25
msgid "DESCRIPTION"
msgstr "描述"

#: internal/route/cli.go:291
msgid "Data backup"
msgstr "數據備份"

#: internal/service/cli.go:121
msgid "Data synchronized successfully"
msgstr "數據同步成功"

#: internal/route/cli.go:233
msgid "Database management"
msgstr "數據庫管理"

#: internal/route/cli.go:325
msgid "Database name"
msgstr "數據庫名稱"

#: internal/service/cli.go:639
msgid "Database server %s added successfully"
msgstr "數據庫伺服器 %s 添加成功"

#: internal/service/cli.go:653
msgid "Database server %s deleted successfully"
msgstr "數據庫伺服器 %s 刪除成功"

#: internal/route/cli.go:319
msgid "Database type"
msgstr "數據庫類型"

#: internal/route/cli.go:276
msgid "Delete database server"
msgstr "刪除數據庫服務器"

#: internal/route/cli.go:212
msgid "Delete website (including website directory, database with the same name)"
msgstr "刪除網站（包括網站目錄、同名數據庫）"

#: internal/route/cli.go:96
msgid "Disable HTTPS"
msgstr "禁用 HTTPS"

#: internal/route/cli.go:139
msgid "Disable IP binding"
msgstr "禁用繫結 IP"

#: internal/route/cli.go:150
msgid "Disable UA binding"
msgstr "禁用繫結 UA"

#: internal/route/cli.go:117
msgid "Disable access entrance"
msgstr "禁用訪問入口"

#: internal/route/cli.go:128
msgid "Disable domain binding"
msgstr "禁用繫結網域"

#: internal/service/cli.go:107
msgid "Download URL is empty"
msgstr "下載 URL 為空"

#: internal/data/backup.go:731
#: internal/data/backup.go:734
msgid "Download failed: %v"
msgstr "下載失敗：%v"

#: internal/data/backup.go:737
msgid "Download file check failed"
msgstr "下載文件檢查失敗"

#: internal/service/file.go:295
msgid "Download remote file %v"
msgstr "下載遠程文件 %v"

#: internal/service/cert.go:108
msgid "Duck DNS"
msgstr "Duck DNS"

#: internal/route/cli.go:91
msgid "Enable HTTPS"
msgstr "啟用 HTTPS"

#: internal/route/cli.go:112
msgid "Enable access entrance"
msgstr "啟用訪問入口"

#: internal/apps/php/app.go:383
msgid "Enchant is a spell-checking library"
msgstr "Enchant 是一個拼寫檢查庫"

#: internal/service/cli.go:436
msgid "Entrance disabled"
msgstr "入口已禁用"

#: internal/service/cli.go:410
msgid "Entrance enabled"
msgstr "入口已啟用"

#: internal/service/cli.go:165
#: internal/service/cli.go:411
msgid "Entrance: %s"
msgstr "入口：%s"

#: internal/apps/php/app.go:363
msgid "Event is a library for handling events"
msgstr "Event 是一個用於處理事件的庫"

#: internal/apps/php/app.go:318
msgid "Exif is a library for reading and writing image metadata"
msgstr "Exif 是一個用於讀取和寫入圖像元數據的庫"

#: internal/service/cli.go:284
msgid "Failed to change 2FA status: %v"
msgstr "修改兩步驗證狀態失敗：%v"

#: internal/service/cli.go:258
msgid "Failed to change password: %v"
msgstr "修改密碼失敗：%v"

#: internal/service/cli.go:226
msgid "Failed to change username: %v"
msgstr "修改用戶名失敗：%v"

#: internal/service/cli.go:873
msgid "Failed to clear tasks: %v"
msgstr "清除任務失敗：%v"

#: internal/service/cli.go:848
msgid "Failed to delete app: %v"
msgstr "刪除應用失敗：%v"

#: internal/service/cli.go:927
msgid "Failed to delete setting: %v"
msgstr "刪除設置失敗：%v"

#: internal/service/cli.go:292
msgid "Failed to generate 2FA: %v"
msgstr "生成兩步驗證失敗：%v"

#: internal/service/cli.go:139
#: internal/service/cli.go:254
msgid "Failed to generate password: %v"
msgstr "生成密碼失敗：%v"

#: internal/service/cli.go:828
msgid "Failed to get app: %v"
msgstr "獲取應用失敗：%v"

#: internal/service/cli.go:159
msgid "Failed to get entrance"
msgstr "獲取入口失敗"

#: internal/service/cli.go:102
msgid "Failed to get latest version: %v"
msgstr "無法獲取最新版本：%v"

#: internal/service/cli.go:155
msgid "Failed to get port"
msgstr "無法獲取端口"

#: internal/service/cli.go:891
#: internal/service/cli.go:908
msgid "Failed to get setting: %v"
msgstr "無法獲取設置：%v"

#: internal/service/cli.go:133
msgid "Failed to get user info: %v"
msgstr "無法獲取用戶信息：%v"

#: internal/service/cli.go:195
msgid "Failed to get user list: %v"
msgstr "無法獲取用戶列表：%v"

#: internal/service/cli.go:220
#: internal/service/cli.go:248
#: internal/service/cli.go:276
msgid "Failed to get user: %v"
msgstr "無法獲取用戶：%v"

#: internal/service/cli.go:299
msgid "Failed to read input: %v"
msgstr "讀取輸入失敗： %v"

#: internal/service/cli.go:835
msgid "Failed to save app: %v"
msgstr "無法保存應用：%v"

#: internal/service/cli.go:914
msgid "Failed to save setting: %v"
msgstr "無法保存設置：%v"

#: internal/service/cli.go:145
msgid "Failed to save user info: %v"
msgstr "無法保存用戶信息：%v"

#: internal/service/cli.go:115
msgid "Failed to synchronize app data: %v"
msgstr "同步應用程式資料失敗：%v"

#: internal/service/cli.go:118
msgid "Failed to synchronize rewrite rules: %v"
msgstr "同步重寫規則失敗：%v"

#: internal/service/cli.go:302
msgid "Failed to update 2FA: %v"
msgstr "更新兩步驗證失敗： %v"

#: internal/apps/php/app.go:288
msgid "Fileinfo is a library used to identify file types"
msgstr "Fileinfo 是一個用於識別文件類型的庫"

#: internal/data/backup.go:593
msgid "Files are normal and do not need to be repaired, please run panel-cli update to update the panel"
msgstr "文件正常，無需修復，請運行 panel-cli update 更新面板"

#: internal/route/cli.go:51
msgid "Fix panel"
msgstr "修復面板"

#: internal/bootstrap/cli.go:34
msgid "Forum：https://bbs.haozi.net"
msgstr "論壇：https://bbs.haozi.net"

#: internal/apps/mysql/app.go:127
msgid "Full Joins without Index"
msgstr "沒有使用索引的 Join"

#: internal/apps/mysql/app.go:128
msgid "Full Range Joins without Index"
msgstr "沒有使用索引的範圍 Join"

#: internal/bootstrap/cli.go:20
msgid "GLOBAL OPTIONS"
msgstr "全局選項"

#: internal/apps/php/app.go:398
msgid "GMP is a library for handling large integers"
msgstr "GMP 是一個用於處理大整數的庫"

#: internal/service/cert.go:84
msgid "Gcore"
msgstr "Gcore"

#: internal/route/cli.go:101
msgid "Generate HTTPS certificate"
msgstr "生成 HTTPS 證書"

#: internal/route/cli.go:474
msgid "Get panel setting (use only under guidance)"
msgstr "獲取面板設置（僅在指導下使用）"

#: internal/apps/php/app.go:418
msgid "Gettext is a library for handling multilingual support"
msgstr "Gettext 是一個處理多語言支持的庫"

#: internal/service/cert.go:80
msgid "Godaddy"
msgstr "Godaddy"

#: internal/service/cli.go:385
msgid "HTTPS certificate generated"
msgstr "HTTPS 憑證已生成"

#: internal/service/cli.go:354
msgid "HTTPS disabled"
msgstr "HTTPS 已停用"

#: internal/service/cli.go:329
msgid "HTTPS enabled"
msgstr "HTTPS 已啟用"

#: internal/service/cert.go:112
msgid "Hetzner"
msgstr "Hetzner"

#: internal/service/cert.go:68
msgid "Huawei Cloud"
msgstr "華為雲"

#: internal/service/cli.go:199
msgid "ID: %d, Username: %s, Email: %s, Created At: %s"
msgstr "ID: %d, 用戶名: %s, 電子郵件: %s, 創建時間: %s"

#: internal/apps/php/app.go:343
msgid "IMAP extension allows PHP to read, search, delete, download, and manage emails"
msgstr "IMAP 擴展允許 PHP 讀取、搜索、刪除、下載和管理電子郵件"

#: internal/apps/php/app.go:135
msgid "Idle Processes"
msgstr "空閒進程"

#: internal/service/cli.go:185
msgid "If you cannot access, please check whether the server's security group and firewall allow port %s"
msgstr "如果您無法訪問，請檢查服務器的安全組和防火牆是否允許端口 %s"

#: internal/service/cli.go:186
msgid "If you still cannot access, try running panel-cli https off to turn off panel HTTPS"
msgstr "如果您仍然無法訪問，請嘗試運行 panel-cli https off 關閉面板 HTTPS"

#: internal/apps/php/app.go:298
msgid "Igbinary is a library for serializing and deserializing data"
msgstr "Igbinary 是一個用於序列化和反序列化數據的庫"

#: internal/apps/php/app.go:313
msgid "ImageMagick is free software for creating, editing, and composing images"
msgstr "ImageMagick 是一個創建、編輯、合成圖片的免費軟件"

#: internal/apps/mysql/app.go:123
msgid "Index Hit Rate"
msgstr "索引命中率"

#: internal/service/cli.go:951
#: internal/service/cli.go:956
#: internal/service/cli.go:961
#: internal/service/cli.go:965
msgid "Initialization failed: %v"
msgstr "初始化失敗：%v"

#: internal/route/cli.go:505
msgid "Initialize panel (use only under guidance)"
msgstr "初始化面板（僅在指導下使用）"

#: internal/apps/mysql/app.go:124
msgid "Innodb Index Hit Rate"
msgstr "Innodb 索引命中率"

#: internal/apps/php/app.go:240
msgid "Install PHP-%d %s extension"
msgstr "安裝 PHP-%d %s 擴展"

#: internal/data/app.go:191
msgid "Install app %s"
msgstr "安裝應用 %s"

#: internal/route/cli.go:440
msgid "Install application"
msgstr "安裝應用程式"

#: internal/data/backup.go:515
#: internal/data/backup.go:541
msgid "Insufficient backup directory space"
msgstr "備份目錄空間不足"

#: internal/apps/php/app.go:413
msgid "Intl is a library for handling internationalization and localization"
msgstr "Intl 是一個處理國際化和本地化的庫"

#: internal/apps/redis/app.go:87
msgid "Keyspace Hits"
msgstr "查找數據庫鍵成功次數"

#: internal/apps/redis/app.go:88
msgid "Keyspace Misses"
msgstr "查找數據庫鍵失敗次數"

#: internal/apps/php/app.go:378
msgid "LDAP is a protocol for accessing directory services"
msgstr "LDAP 是一種用於訪問目錄服務的協議"

#: internal/apps/redis/app.go:89
msgid "Latest Fork Time (ms)"
msgstr "最近一次 fork() 操作耗費的毫秒數"

#: internal/service/cert.go:116
msgid "Linode"
msgstr "Linode"

#: internal/route/cli.go:65
msgid "List all users"
msgstr "列出所有用戶"

#: internal/route/cli.go:177
msgid "List of domains associated with the website"
msgstr "與網站關聯的域名列表"

#: internal/route/cli.go:183
msgid "List of listening addresses associated with the website"
msgstr "與網站關聯的監聽地址列表"

#: internal/apps/php/app.go:132
msgid "Listen Queue"
msgstr "監聽佇列"

#: internal/apps/php/app.go:134
msgid "Listen Queue Length"
msgstr "監聽佇列長度"

#: internal/service/cli.go:169
msgid "Local IPv4: %s://%s:%s%s"
msgstr "本地 IPv4：%s://%s:%s%s"

#: internal/service/cli.go:173
msgid "Local IPv6: %s://[%s]:%s%s"
msgstr "本地 IPv6：%s://[%s]:%s%s"

#: internal/route/cli.go:381
msgid "Log rotation"
msgstr "日誌輪替"

#: internal/apps/php/app.go:138
msgid "Max Active Processes"
msgstr "最大活躍進程數量"

#: internal/apps/php/app.go:139
msgid "Max Children Reached"
msgstr "達到進程上限次數"

#: internal/apps/php/app.go:133
msgid "Max Listen Queue"
msgstr "最大監聽佇列"

#: internal/apps/php/app.go:308
msgid "Memcached is a driver for connecting to Memcached servers"
msgstr "Memcached 是一個用於連接 Memcached 伺服器的驅動程式"

#: internal/apps/nginx/app.go:113
msgid "Memory"
msgstr "記憶體"

#: internal/apps/redis/app.go:83
msgid "Memory Fragmentation Ratio"
msgstr "記憶體碎片比率"

#: internal/data/backup.go:653
msgid "Move panel config failed: %v"
msgstr "移動面板配置失敗：%v"

#: internal/data/backup.go:648
msgid "Move panel file failed: %v"
msgstr "移動面板檔案失敗：%v"

#: internal/data/backup.go:658
msgid "Move panel-cli file failed: %v"
msgstr "移動 panel-cli 檔案失敗：%v"

#: internal/apps/mysql/app.go:88
msgid "MySQL root password is empty"
msgstr "MySQL root 密碼為空"

#: internal/bootstrap/cli.go:14
#: internal/bootstrap/cli.go:22
#: internal/bootstrap/cli.go:27
msgid "NAME"
msgstr "名稱"

#: internal/service/cert.go:100
msgid "Name.com"
msgstr "Name.com"

#: internal/service/cert.go:96
msgid "NameSilo"
msgstr "NameSilo"

#: internal/service/cert.go:92
msgid "Namecheap"
msgstr "Namecheap"

#: internal/service/cli.go:213
msgid "New username cannot be empty"
msgstr "新用戶名不能為空"

#: internal/data/backup.go:621
msgid "No backup file found, unable to automatically repair"
msgstr "未找到備份檔案，無法自動修復"

#: internal/service/cli.go:620
msgid "Not supported"
msgstr "不支援"

#: internal/service/dashboard.go:200
#: internal/service/dashboard.go:201
msgid "Not used"
msgstr "未使用"

#: internal/route/cli.go:367
msgid "Number of backups to keep"
msgstr "要保留的備份數量"

#: internal/route/cli.go:422
msgid "Number of logs to keep"
msgstr "要保留的日誌數量"

#: internal/bootstrap/cli.go:26
#: internal/bootstrap/cli.go:31
msgid "OPTIONS"
msgstr "選項"

#: internal/apps/php/app.go:293
msgid "OPcache stores precompiled PHP script bytecode in shared memory to improve PHP performance"
msgstr "OPcache 將 PHP 腳本預編譯的位元組碼儲存到共享記憶體中來提升 PHP 的效能"

#: internal/service/cli.go:210
msgid "Old username cannot be empty"
msgstr "舊用戶名不能為空"

#: internal/apps/mysql/app.go:126
msgid "Open Tables"
msgstr "已開啟的表"

#: internal/route/cli.go:87
msgid "Operate panel HTTPS"
msgstr "操作面板 HTTPS"

#: internal/route/cli.go:135
msgid "Operate panel IP binding"
msgstr "操作面板繫結 IP"

#: internal/route/cli.go:146
msgid "Operate panel UA binding"
msgstr "操作面板繫結 UA"

#: internal/route/cli.go:108
msgid "Operate panel access entrance"
msgstr "操作面板訪問入口"

#: internal/route/cli.go:124
msgid "Operate panel domain binding"
msgstr "操作面板繫結網域"

#: internal/route/cli.go:61
msgid "Operate panel users"
msgstr "操作面板用戶"

#: internal/route/cli.go:56
msgid "Output panel basic information and generate new password"
msgstr "輸出面板基本信息並生成新密碼"

#: internal/route/cli.go:193
msgid "PHP version used by the website (not used if not filled)"
msgstr "網站使用的 PHP 版本（不填則不使用）"

#: internal/service/cli.go:76
msgid "Panel service restarted"
msgstr "面板服務已重新啟動"

#: internal/service/cli.go:94
msgid "Panel service started"
msgstr "面板服務已啟動"

#: internal/service/cli.go:85
msgid "Panel service stopped"
msgstr "面板服務已停止"

#: internal/service/cli.go:778
#: internal/service/cli.go:792
#: internal/service/cli.go:806
#: internal/service/cli.go:822
#: internal/service/cli.go:844
#: internal/service/cli.go:883
#: internal/service/cli.go:902
#: internal/service/cli.go:923
msgid "Parameters cannot be empty"
msgstr "參數不能為空"

#: internal/service/cli.go:261
msgid "Password for user %s changed successfully"
msgstr "用戶 %s 的密碼已成功更改"

#: internal/service/cli.go:241
msgid "Password length cannot be less than 6"
msgstr "密碼長度不能少於6位"

#: internal/service/cli.go:163
msgid "Password: %s"
msgstr "密碼：%s"

#: internal/route/cli.go:189
msgid "Path where the website is hosted (default path if not filled)"
msgstr "網站託管的路徑（不填則使用默認路徑）"

#: internal/apps/mysql/app.go:122
msgid "Peak Connections"
msgstr "峰值連接數"

#: internal/apps/redis/app.go:82
msgid "Peak Memory Usage"
msgstr "佔用記憶體峰值"

#: internal/apps/php/app.go:303
msgid "PhpRedis connects to and operates on data in Redis databases (requires the igbinary extension installed above)"
msgstr "PhpRedis 連接並操作 Redis 資料庫上的資料（需先安裝上面 igbinary 擴展）"

#: internal/service/cli.go:184
msgid "Please choose the appropriate address to access the panel based on your network situation"
msgstr "請根據您的網絡情況選擇合適的地址訪問面板"

#: internal/service/cli.go:296
msgid "Please enter the 2FA code: "
msgstr "請輸入兩步驗證代碼： "

#: internal/service/cert.go:88
msgid "Porkbun"
msgstr "Porkbun"

#: internal/service/cli.go:532
msgid "Port already in use"
msgstr "端口已被佔用"

#: internal/service/cli.go:560
msgid "Port changed to %d"
msgstr "端口已更改為 %d"

#: internal/service/cli.go:518
msgid "Port range error"
msgstr "端口範圍錯誤"

#: internal/service/cli.go:164
msgid "Port: %s"
msgstr "端口：%s"

#: internal/apps/postgresql/app.go:142
msgid "Process Count"
msgstr "進程數"

#: internal/apps/php/app.go:129
msgid "Process Manager"
msgstr "進程管理方式"

#: internal/apps/postgresql/app.go:141
msgid "Process PID"
msgstr "進程 PID"

#: internal/apps/php/app.go:388
msgid "Pspell is a spell-checking library"
msgstr "Pspell 是一個拼寫檢查庫"

#: internal/service/cli.go:177
msgid "Public IPv4: %s://%s:%s%s"
msgstr "公用 IPv4：%s://%s:%s%s"

#: internal/service/cli.go:181
msgid "Public IPv6: %s://[%s]:%s%s"
msgstr "公用 IPv6：%s://[%s]:%s%s"

#: internal/bootstrap/cli.go:35
msgid "QQ Group：12370907"
msgstr "QQ 群：12370907"

#: internal/service/dashboard.go:58
msgid "Rat Panel"
msgstr "耗子面板"

#: internal/bootstrap/cli.go:39
msgid "Rat Panel CLI Tool"
msgstr "耗子面板命令列工具"

#: internal/apps/nginx/app.go:144
msgid "Reading"
msgstr "正在讀取"

#: internal/apps/php/app.go:368
msgid "Readline is a library for processing text"
msgstr "Readline 是一個處理文本的庫"

#: internal/route/cli.go:461
msgid "Remove panel application mark (use only under guidance)"
msgstr "移除面板應用標記（僅在指導下使用）"

#: internal/data/backup.go:645
msgid "Remove panel file failed: %v"
msgstr "刪除面板檔案失敗：%v"

#: internal/route/cli.go:486
msgid "Remove panel setting (use only under guidance)"
msgstr "移除面板設置（僅在指導下使用）"

#: internal/route/cli.go:199
msgid "Remove website"
msgstr "移除網站"

#: internal/route/cli.go:26
msgid "Restart panel service"
msgstr "重新啟動面板服務"

#: internal/apps/mysql/app.go:118
msgid "Rollbacks per Second"
msgstr "每秒回滾"

#: internal/route/cli.go:428
msgid "Rotation directory (default path if not filled)"
msgstr "旋轉目錄（如果未填寫則使用默認路徑）"

#: internal/route/cli.go:416
msgid "Rotation file"
msgstr "旋轉文件"

#: internal/route/cli.go:410
msgid "Rotation type"
msgstr "旋轉類型"

#: internal/apps/php/app.go:373
msgid "SNMP is a protocol for network management"
msgstr "SNMP 是一種用於網絡管理的協議"

#: internal/apps/php/app.go:358
msgid "SSH2 is a library for connecting to SSH servers"
msgstr "SSH2 是一個用於連接 SSH 伺服器的程式庫"

#: internal/route/cli.go:307
#: internal/route/cli.go:331
#: internal/route/cli.go:343
#: internal/route/cli.go:398
msgid "Save directory (default path if not filled)"
msgstr "保存目錄（如果未填寫則使用默認路徑）"

#: internal/route/cli.go:252
msgid "Server address"
msgstr "伺服器地址"

#: internal/route/cli.go:247
#: internal/route/cli.go:281
msgid "Server name"
msgstr "伺服器名稱"

#: internal/route/cli.go:266
msgid "Server password"
msgstr "伺服器密碼"

#: internal/route/cli.go:257
msgid "Server port"
msgstr "伺服器端口"

#: internal/route/cli.go:270
msgid "Server remark"
msgstr "伺服器備註"

#: internal/route/cli.go:242
msgid "Server type"
msgstr "伺服器類型"

#: internal/route/cli.go:262
msgid "Server username"
msgstr "伺服器用戶名"

#: internal/route/cli.go:469
msgid "Setting management"
msgstr "設置管理"

#: internal/service/cli.go:889
msgid "Setting not exists"
msgstr "設置不存在"

#: internal/apps/php/app.go:140
msgid "Slow Requests"
msgstr "慢請求"

#: internal/apps/mysql/app.go:130
msgid "Sort Merge Passes"
msgstr "排序後的合併次數"

#: internal/apps/php/app.go:130
#: internal/apps/postgresql/app.go:140
msgid "Start Time"
msgstr "啟動時間"

#: internal/route/cli.go:36
msgid "Start panel service"
msgstr "啟動面板服務"

#: internal/route/cli.go:31
msgid "Stop panel service"
msgstr "停止面板服務"

#: internal/apps/postgresql/app.go:144
msgid "Storage Usage"
msgstr "存儲使用"

#: internal/apps/mysql/app.go:129
msgid "Subqueries without Index"
msgstr "沒有索引的子查詢"

#: internal/apps/php/app.go:478
msgid "Swoole is a PHP extension for building high-performance asynchronous concurrent servers"
msgstr "Swoole 是構建高性能異步並發伺服器的 PHP 擴展"

#: internal/apps/php/app.go:487
msgid "Swow is a PHP extension for building high-performance asynchronous concurrent servers"
msgstr "Swow 是構建高性能異步並發伺服器的 PHP 擴展"

#: internal/route/cli.go:46
msgid "Sync panel data"
msgstr "同步面板數據"

#: internal/route/cli.go:494
msgid "Sync system time"
msgstr "同步系統時間"

#: internal/apps/php/app.go:458
msgid "Sysvmsg is a library for handling System V message queues"
msgstr "Sysvmsg 是一個處理系統 V 訊息佇列的庫"

#: internal/apps/php/app.go:463
msgid "Sysvsem is a library for handling System V semaphores"
msgstr "Sysvsem 是一個處理系統 V 信號的庫"

#: internal/apps/php/app.go:468
msgid "Sysvshm is a library for handling System V shared memory"
msgstr "Sysvshm 是一個處理系統 V 共享記憶體的庫"

#: internal/apps/redis/app.go:77
msgid "TCP Port"
msgstr "TCP 連接埠"

#: internal/apps/mysql/app.go:131
msgid "Table Locks Waited"
msgstr "等待表鎖定"

#: internal/service/cli.go:876
msgid "Tasks cleared successfully"
msgstr "任務已成功清除"

#: internal/apps/mysql/app.go:125
msgid "Temporary Tables Created on Disk"
msgstr "在磁碟上創建的臨時表"

#: internal/data/backup.go:751
msgid "Temporary file detected in /tmp, this may be caused by the last update failure, please run panel-cli fix to repair and try again"
msgstr "在 /tmp 中檢測到臨時檔案，這可能是上次更新失敗導致的，請運行 panel-cli fix 進行修復並重試"

#: internal/service/cert.go:64
msgid "Tencent Cloud"
msgstr "騰訊雲"

#: internal/data/backup.go:561
msgid "The number of files contained in the compressed file is not 1, actual %d"
msgstr "壓縮檔案包含的檔案數量不為1，實際為 %d"

#: internal/service/cli.go:864
msgid "Time synchronized successfully"
msgstr "時間同步成功"

#: internal/apps/redis/app.go:80
msgid "Total Allocated Memory"
msgstr "已分配記憶體總量"

#: internal/apps/redis/app.go:85
msgid "Total Commands Processed"
msgstr "已處理的命令總數"

#: internal/apps/mysql/app.go:116
#: internal/apps/postgresql/app.go:143
msgid "Total Connections"
msgstr "總連接數"

#: internal/apps/redis/app.go:84
msgid "Total Connections Received"
msgstr "已接收的連接總數"

#: internal/apps/redis/app.go:81
msgid "Total Memory Usage"
msgstr "記憶體使用總量"

#: internal/apps/php/app.go:137
msgid "Total Processes"
msgstr "總進程數"

#: internal/apps/mysql/app.go:115
msgid "Total Queries"
msgstr "查詢總數"

#: internal/apps/nginx/app.go:128
msgid "Total connections"
msgstr "總連接數"

#: internal/apps/nginx/app.go:132
msgid "Total handshakes"
msgstr "總握手數"

#: internal/apps/nginx/app.go:136
msgid "Total requests"
msgstr "總請求數"

#: internal/apps/mysql/app.go:117
msgid "Transactions per Second"
msgstr "每秒事務"

#: internal/bootstrap/cli.go:15
#: internal/bootstrap/cli.go:23
#: internal/bootstrap/cli.go:28
#: internal/bootstrap/cli.go:29
msgid "USAGE"
msgstr "使用"

#: internal/service/app.go:173
msgid "Unable to update app list cache in offline mode"
msgstr "無法在離線模式下更新應用程式列表快取"

#: internal/apps/php/app.go:271
msgid "Uninstall PHP-%d %s extension"
msgstr "卸載 PHP-%d %s 擴展"

#: internal/data/app.go:246
msgid "Uninstall app %s"
msgstr "卸載應用 %s"

#: internal/route/cli.go:445
msgid "Uninstall application"
msgstr "卸載應用"

#: internal/data/backup.go:636
msgid "Unzip backup file failed: %v"
msgstr "解壓備份檔案失敗：%v"

#: internal/data/backup.go:668
msgid "Unzip panel data failed: %v"
msgstr "解壓面板數據失敗：%v"

#: internal/data/app.go:301
msgid "Update app %s"
msgstr "更新應用 %s"

#: internal/route/cli.go:450
msgid "Update application"
msgstr "更新應用"

#: internal/route/cli.go:41
msgid "Update panel"
msgstr "更新面板"

#: internal/apps/mysql/app.go:114
msgid "Uptime"
msgstr "運行時間"

#: internal/apps/redis/app.go:78
msgid "Uptime in Days"
msgstr "已運行天數"

#: internal/service/cli.go:218
#: internal/service/cli.go:246
#: internal/service/cli.go:274
msgid "User not exists"
msgstr "使用者不存在"

#: internal/service/cli.go:229
msgid "Username %s changed to %s successfully"
msgstr "使用者名稱 %s 已成功更改為 %s"

#: internal/service/cli.go:238
msgid "Username and password cannot be empty"
msgstr "使用者名稱和密碼不能為空"

#: internal/service/cli.go:269
msgid "Username cannot be empty"
msgstr "用戶名不能為空"

#: internal/service/cli.go:162
msgid "Username: %s"
msgstr "使用者名稱：%s"

#: internal/bootstrap/cli.go:16
msgid "VERSION"
msgstr "版本"

#: internal/service/cert.go:120
msgid "Vercel"
msgstr "Vercel"

#: internal/data/backup.go:744
msgid "Verify download file failed: %v"
msgstr "驗證下載文件失敗：%v"

#: internal/apps/nginx/app.go:152
msgid "Waiting"
msgstr "正在駐留等待"

#: internal/service/cli.go:187
msgid "Warning: After turning off panel HTTPS, the security of the panel will be greatly reduced, please operate with caution"
msgstr "警告：關閉面板 HTTPS 後，面板的安全性將大大降低，請謹慎操作"

#: internal/route/cli.go:385
msgid "Website"
msgstr "網站"

#: internal/service/cli.go:579
msgid "Website %s created successfully"
msgstr "網站 %s 創建成功"

#: internal/service/cli.go:615
msgid "Website %s deleted successfully"
msgstr "網站 %s 刪除成功"

#: internal/service/cli.go:596
msgid "Website %s removed successfully"
msgstr "網站 %s 移除成功"

#: internal/route/cli.go:162
msgid "Website management"
msgstr "網站管理"

#: internal/route/cli.go:171
#: internal/route/cli.go:204
#: internal/route/cli.go:217
#: internal/route/cli.go:301
#: internal/route/cli.go:391
msgid "Website name"
msgstr "網站名稱"

#: internal/bootstrap/cli.go:33
msgid "Website：https://panel.haozi.net"
msgstr "網站：https://panel.haozi.net"

#: internal/service/cert.go:72
msgid "West.cn"
msgstr "西部數碼"

#: internal/apps/nginx/app.go:102
msgid "Workers"
msgstr "工作進程"

#: internal/route/cli.go:480
msgid "Write panel setting (use only under guidance)"
msgstr "寫入面板設置（僅在指導下使用）"

#: internal/route/cli.go:225
msgid "Write website data (use only under guidance)"
msgstr "寫入網站數據（僅在指導下使用）"

#: internal/apps/nginx/app.go:148
msgid "Writing"
msgstr "正在寫入"

#: internal/apps/php/app.go:403
msgid "XLSWriter is a high-performance library for reading and writing Excel files"
msgstr "XLSWriter 是一個高性能讀寫 Excel 文件的庫"

#: internal/apps/php/app.go:408
msgid "XSL is a library for processing XML documents"
msgstr "XSL 是用於處理 XML 文檔的庫"

#: internal/apps/php/app.go:348
msgid "Zip is a library for handling ZIP files"
msgstr "Zip 是一個用於處理 ZIP 文件的庫"

#: internal/data/app.go:159
msgid "app %s already installed"
msgstr "應用 %s 已安裝"

#: internal/data/app.go:56
msgid "app %s not found"
msgstr "找不到應用 %s"

#: internal/data/app.go:210
#: internal/data/app.go:265
#: internal/http/middleware/must_install.go:48
msgid "app %s not installed"
msgstr "應用程式 %s 未安裝"

#: internal/data/app.go:179
#: internal/data/app.go:289
msgid "app %s not support current panel version"
msgstr "應用程式 %s 不支援目前面板版本"

#: internal/data/app.go:170
#: internal/data/app.go:225
#: internal/data/app.go:280
msgid "app %s requires panel version %s, current version %s"
msgstr "應用程式 %s 需要面板版本 %s，目前版本 %s"

#: internal/http/middleware/must_install.go:29
msgid "app not found"
msgstr "找不到應用"

#: internal/data/setting.go:282
#: internal/data/setting.go:343
#: internal/data/setting.go:356
msgid "background task is running, modifying some settings is prohibited, please try again later"
msgstr "後台任務正在運行，禁止修改某些設置，請稍後再試"

#: internal/service/dashboard.go:339
msgid "background task is running, restart is prohibited, please try again later"
msgstr "後台任務正在運行，禁止重啟，請稍後再試"

#: internal/service/dashboard.go:307
msgid "background task is running, updating is prohibited, please try again later"
msgstr "後台任務正在運行，禁止更新，請稍後再試"

#: internal/data/backup.go:383
#: internal/data/backup.go:410
#: internal/data/backup.go:456
msgid "backup file %s not exists"
msgstr "備份檔案 %s 不存在"

#: internal/service/file.go:277
msgid "can't download a directory"
msgstr "無法下載目錄"

#: internal/data/website.go:371
msgid "can't find %s database server, please add it first"
msgstr "找不到 %s 數據庫服務器，請先添加"

#: internal/data/database_server.go:106
#: internal/data/database_server.go:86
msgid "check server connection failed"
msgstr "檢查伺服器連接失敗"

#: internal/http/middleware/must_login.go:93
msgid "client ip/ua changed, please login again"
msgstr "客戶端 IP/UA 已更改，請重新登入"

#: internal/data/backup.go:569
msgid "could not find .sql backup file"
msgstr "找不到 .sql 備份檔案"

#: internal/service/file.go:166
msgid "create directory error: %v"
msgstr "創建目錄錯誤：%v"

#: internal/data/cron.go:95
msgid "cron directory %s not exists"
msgstr "cron 目錄 %s 不存在"

#: internal/data/cron.go:98
msgid "cron log directory %s not exists"
msgstr "cron 日誌目錄 %s 不存在"

#: internal/data/backup.go:263
#: internal/data/backup.go:309
#: internal/data/backup.go:425
#: internal/data/backup.go:467
msgid "database does not exist: %s"
msgstr "資料庫不存在：%s"

#: internal/data/website.go:725
msgid "default document comment count is incorrect, expected 1, actual %d"
msgstr "默認文檔註釋數量不正確，預期為1，實際為 %d"

#: internal/data/website.go:722
msgid "default document comment not found"
msgstr "未找到預設文件註釋"

#: internal/apps/pureftpd/app.go:84
msgid "directory %s does not exist"
msgstr "目錄 %s 不存在"

#: internal/service/toolbox_system.go:139
msgid "disk space is insufficient, current free %s"
msgstr "磁盤空間不足，當前可用 %s"

#: internal/data/task.go:65
msgid "duplicate submission, please wait for the previous task to end"
msgstr "重複提交，請等待前一個任務結束"

#: internal/apps/s3fs/app.go:62
msgid "endpoint should not contain bucket"
msgstr "端點不應包含 bucket"

#: internal/service/user_token.go:54
#: internal/service/user_token.go:89
msgid "expiration time must be greater than current time"
msgstr "到期時間必須大於當前時間"

#: internal/service/user_token.go:58
#: internal/service/user_token.go:93
msgid "expiration time must be less than 10 years"
msgstr "到期時間必須小於 10 年"

#: internal/apps/php/app.go:229
#: internal/apps/php/app.go:260
msgid "extension %s does not exist"
msgstr "擴展 %s 不存在"

#: internal/data/ssh.go:53
#: internal/data/ssh.go:77
msgid "failed to check ssh connection: %v"
msgstr "檢查 ssh 連接失敗：%v"

#: internal/data/backup.go:603
msgid "failed to clean temporary files: %v"
msgstr "清理臨時文件失敗：%v"

#: internal/apps/s3fs/app.go:69
msgid "failed to create mount path: %v"
msgstr "創建掛載路徑失敗：%v"

#: internal/apps/s3fs/app.go:94
msgid "failed to create passwd file: %v"
msgstr "創建 passwd 文件失敗：%v"

#: internal/service/systemctl.go:77
msgid "failed to disable %s service: %v"
msgstr "禁用 %s 服務失敗：%v"

#: internal/service/systemctl.go:62
msgid "failed to enable %s service: %v"
msgstr "啟用 %s 服務失敗：%v"

#: internal/service/systemctl.go:47
msgid "failed to get %s service enable status: %v"
msgstr "無法獲取 %s 服務啟用狀態：%v"

#: internal/service/systemctl.go:31
msgid "failed to get %s service running status: %v"
msgstr "無法獲取 %s 服務運行狀態：%v"

#: internal/data/website.go:306
msgid "failed to get 404 template file: %v"
msgstr "獲取404模板文件失敗：%v"

#: internal/data/cert_account.go:202
msgid "failed to get Google EAB: %s"
msgstr "無法獲取 Google EAB：%s"

#: internal/data/cert_account.go:198
msgid "failed to get Google EAB: %v"
msgstr "無法獲取 Google EAB：%v"

#: internal/apps/memcached/app.go:37
msgid "failed to get Memcached status: %v"
msgstr "獲取 Memcached 狀態失敗：%v"

#: internal/apps/mysql/app.go:104
msgid "failed to get MySQL status: %v"
msgstr "獲取 MySQL 狀態失敗：%v"

#: internal/apps/postgresql/app.go:120
msgid "failed to get PostgreSQL backend pid: %v"
msgstr "無法獲取 PostgreSQL 後端 pid：%v"

#: internal/apps/postgresql/app.go:130
msgid "failed to get PostgreSQL connections: %v"
msgstr "無法獲取 PostgreSQL 連接：%v"

#: internal/apps/postgresql/app.go:135
msgid "failed to get PostgreSQL database size: %v"
msgstr "無法獲取 PostgreSQL 數據庫大小：%v"

#: internal/apps/postgresql/app.go:125
msgid "failed to get PostgreSQL process: %v"
msgstr "無法獲取 PostgreSQL 進程：%v"

#: internal/apps/postgresql/app.go:115
msgid "failed to get PostgreSQL start time: %v"
msgstr "無法獲取 PostgreSQL 啟動時間：%v"

#: internal/service/toolbox_system.go:77
#: internal/service/toolbox_system.go:90
msgid "failed to get SWAP: %v"
msgstr "無法獲取 SWAP：%v"

#: internal/data/cert_account.go:227
msgid "failed to get ZeroSSL EAB"
msgstr "無法獲取 ZeroSSL EAB"

#: internal/data/cert_account.go:223
msgid "failed to get ZeroSSL EAB: %v"
msgstr "無法獲取 ZeroSSL EAB：%v"

#: internal/service/toolbox_system.go:191
msgid "failed to get available timezones: %v"
msgstr "無法獲取可用時區：%v"

#: internal/apps/fail2ban/app.go:268
msgid "failed to get banned ip list"
msgstr "無法獲取被封禁的 ip 列表"

#: internal/apps/fail2ban/app.go:258
msgid "failed to get current banned list"
msgstr "無法獲取當前被禁止的列表"

#: internal/service/toolbox_system.go:135
msgid "failed to get disk space: %v"
msgstr "無法獲取磁盤空間：%v"

#: internal/service/dashboard.go:70
msgid "failed to get home apps: %v"
msgstr "無法獲取主頁應用：%v"

#: internal/data/website.go:294
msgid "failed to get index template file: %v"
msgstr "獲取首頁模板文件失敗：%v"

#: internal/apps/supervisor/app.go:218
#: internal/apps/supervisor/app.go:241
#: internal/apps/supervisor/app.go:358
#: internal/apps/supervisor/app.go:368
msgid "failed to get log path for process %s: %v"
msgstr "無法獲取進程 %s 的日誌路徑：%v"

#: internal/apps/nginx/app.go:108
#: internal/apps/nginx/app.go:98
msgid "failed to get nginx workers: %v"
msgstr "無法獲取 nginx 工作進程：%v"

#: internal/apps/pureftpd/app.go:152
msgid "failed to get port: %v"
msgstr "無法獲取端口：%v"

#: internal/data/cert_account.go:113
msgid "failed to get private key"
msgstr "無法獲取私鑰"

#: internal/data/cert_account.go:172
msgid "failed to get private key: %v"
msgstr "無法獲取私鑰：%v"

#: internal/apps/redis/app.go:62
msgid "failed to get redis info: %v"
msgstr "無法獲取 redis 信息：%v"

#: internal/apps/redis/app.go:39
msgid "failed to get redis status: %v"
msgstr "無法獲取 redis 狀態：%v"

#: internal/apps/s3fs/app.go:125
#: internal/apps/s3fs/app.go:40
#: internal/apps/s3fs/app.go:80
msgid "failed to get s3fs list: %v"
msgstr "無法獲取 s3fs 列表：%v"

#: internal/service/dashboard.go:90
msgid "failed to get system info: %v"
msgstr "無法獲取系統信息：%v"

#: internal/service/dashboard.go:320
msgid "failed to get the latest version download link"
msgstr "無法獲取最新版本下載鏈接"

#: internal/service/dashboard.go:236
#: internal/service/dashboard.go:272
#: internal/service/dashboard.go:314
msgid "failed to get the latest version: %v"
msgstr "無法獲取最新版本：%v"

#: internal/apps/rsync/app.go:82
msgid "failed to get the secret key for module %s"
msgstr "無法獲取模組 %s 的密鑰"

#: internal/service/dashboard.go:136
msgid "failed to get the total number of websites: %v"
msgstr "無法獲取網站總數：%v"

#: internal/service/dashboard.go:293
msgid "failed to get the update information: %v"
msgstr "無法獲取更新信息：%v"

#: internal/service/toolbox_system.go:180
msgid "failed to get timezone: %v"
msgstr "無法獲取時區：%v"

#: internal/apps/fail2ban/app.go:263
msgid "failed to get total banned list"
msgstr "無法獲取全部被禁止的列表"

#: internal/data/app.go:234
msgid "failed to get uninstall script for app %s"
msgstr "無法取得應用程式 %s 的解除安裝腳本"

#: internal/apps/mysql/app.go:188
#: internal/apps/mysql/app.go:83
msgid "failed to load MySQL root password: %v"
msgstr "無法載入 MySQL root 密碼：%v"

#: internal/data/cert.go:92
#: internal/data/setting.go:287
#: internal/data/setting.go:359
#: internal/data/website.go:475
#: internal/data/website.go:764
msgid "failed to parse certificate: %v"
msgstr "無法解析證書：%v"

#: internal/data/cert.go:95
#: internal/data/setting.go:290
#: internal/data/setting.go:362
#: internal/data/website.go:478
#: internal/data/website.go:767
msgid "failed to parse private key: %v"
msgstr "解析私鑰失敗：%v"

#: internal/apps/fail2ban/app.go:327
#: internal/apps/fail2ban/app.go:355
msgid "failed to parse the ignoreip of fail2ban"
msgstr "無法解析 fail2ban 的 ignoreip"

#: internal/service/dashboard.go:242
#: internal/service/dashboard.go:247
#: internal/service/dashboard.go:278
#: internal/service/dashboard.go:283
msgid "failed to parse version: %v"
msgstr "解析版本失敗：%v"

#: internal/service/ws.go:122
msgid "failed to read command output: %v"
msgstr "讀取命令輸出失敗：%v"

#: internal/service/ws.go:103
msgid "failed to read command: %v"
msgstr "讀取命令失敗：%v"

#: internal/apps/memcached/app.go:77
msgid "failed to read from Memcached: %v"
msgstr "從 Memcached 讀取失敗：%v"

#: internal/data/cert_account.go:108
#: internal/data/cert_account.go:167
msgid "failed to register account: %v"
msgstr "註冊帳戶失敗：%v"

#: internal/service/systemctl.go:107
msgid "failed to reload %s service: %v"
msgstr "重新載入 %s 服務失敗：%v"

#: internal/apps/postgresql/app.go:65
#: internal/apps/postgresql/app.go:98
msgid "failed to reload PostgreSQL: %v"
msgstr "重新載入 PostgreSQL 失敗：%v"

#: internal/apps/nginx/app.go:65
#: internal/apps/phpmyadmin/app.go:108
#: internal/apps/phpmyadmin/app.go:139
msgid "failed to reload nginx: %v"
msgstr "重新載入 nginx 失敗：%v"

#: internal/service/systemctl.go:92
msgid "failed to restart %s service: %v"
msgstr "重新啟動 %s 服務失敗：%v"

#: internal/apps/supervisor/app.go:108
msgid "failed to restart %s: %v"
msgstr "重新啟動 %s 失敗：%v"

#: internal/apps/mysql/app.go:72
msgid "failed to restart MySQL: %v"
msgstr "重新啟動 MySQL 失敗：%v"

#: internal/service/ws.go:110
msgid "failed to run command: %v"
msgstr "運行命令失敗：%v"

#: internal/apps/mysql/app.go:99
msgid "failed to set MYSQL_PWD env: %v"
msgstr "設置 MYSQL_PWD 環境變數失敗：%v"

#: internal/service/toolbox_system.go:159
msgid "failed to set SWAP permission: %v"
msgstr "設置 SWAP 權限失敗： %v"

#: internal/service/toolbox_system.go:276
msgid "failed to set hostname: %v"
msgstr "設置主機名失敗： %v"

#: internal/service/toolbox_system.go:299
msgid "failed to set hosts: %v"
msgstr "設置 hosts 失敗： %v"

#: internal/service/toolbox_system.go:316
msgid "failed to set root password: %v"
msgstr "設置 root 密碼失敗： %v"

#: internal/service/systemctl.go:122
msgid "failed to start %s service: %v"
msgstr "啟動 %s 服務失敗：%v"

#: internal/service/systemctl.go:137
msgid "failed to stop %s service: %v"
msgstr "停止 %s 服務失敗：%v"

#: internal/apps/s3fs/app.go:145
msgid "failed to unmount: %v"
msgstr "卸載失敗：%v"

#: internal/service/toolbox_system.go:63
msgid "failed to update DNS: %v"
msgstr "更新 DNS 失敗： %v"

#: internal/apps/memcached/app.go:56
msgid "failed to write to Memcached: %v"
msgstr "寫入 Memcached 失敗： %v"

#: internal/service/file.go:84
msgid "file is too large, please download it to view"
msgstr "檔案太大，請下載後查看"

#: internal/apps/php/app.go:423
msgid "gRPC is a high-performance, open-source, and general-purpose RPC framework"
msgstr "gRPC 是一個高性能、開源和通用的 RPC 框架"

#: internal/apps/fail2ban/app.go:184
msgid "get service port failed, please check if it is installed"
msgstr "獲取服務端口失敗，請檢查是否安裝"

#: internal/data/user.go:175
#: internal/data/user.go:198
#: internal/service/user.go:102
msgid "invalid 2FA code"
msgstr "無效的兩步驗證代碼"

#: internal/http/middleware/entrance.go:114
msgid "invalid access entrance"
msgstr "無效的訪問入口"

#: internal/data/user_token.go:101
msgid "invalid header: %v"
msgstr "無效的請求標頭：%v"

#: internal/service/user.go:84
msgid "invalid key, please refresh the page"
msgstr "無效的密鑰，請重新整理頁面"

#: internal/http/middleware/entrance.go:57
msgid "invalid request domain: %s"
msgstr "無效的請求域名：%s"

#: internal/data/user_token.go:148
#: internal/http/middleware/entrance.go:70
msgid "invalid request ip: %s"
msgstr "無效的請求 IP：%s"

#: internal/http/middleware/entrance.go:79
msgid "invalid request user agent: %s"
msgstr "無效的請求UA：%s"

#: internal/data/user_token.go:104
#: internal/data/user_token.go:110
#: internal/data/user_token.go:133
msgid "invalid signature"
msgstr "無效的簽名"

#: internal/http/middleware/must_login.go:107
msgid "invalid user id, please login again"
msgstr "無效的用戶ID，請重新登錄"

#: internal/apps/php/app.go:473
msgid "ionCube is a professional-grade PHP encryption and decryption tool (must be installed after OPcache)"
msgstr "ionCube 是一個專業級的 PHP 加密解密工具（需在 OPcache 之後安裝）"

#: internal/data/backup.go:139
msgid "log file %s not exists"
msgstr "日誌檔案 %s 不存在"

#: internal/apps/rsync/app.go:117
msgid "module %s already exists"
msgstr "模組 %s 已存在"

#: internal/apps/rsync/app.go:162
#: internal/apps/rsync/app.go:204
msgid "module %s does not exist"
msgstr "模組 %s 不存在"

#: internal/apps/s3fs/app.go:108
msgid "mount failed: %v"
msgstr "掛載失敗： %v"

#: internal/apps/s3fs/app.go:137
msgid "mount not found"
msgstr "找不到掛載"

#: internal/apps/s3fs/app.go:86
msgid "mount path already exists"
msgstr "掛載路徑已存在"

#: internal/apps/s3fs/app.go:74
msgid "mount path is not empty"
msgstr "掛載路徑不為空"

#: internal/data/database.go:185
msgid "mysql not support database comment"
msgstr "mysql 不支援資料庫註釋"

#: internal/data/website.go:795
msgid "not support one-key obtain wildcard certificate, please use Cert menu to obtain it with DNS method"
msgstr "不支持一鍵獲取通配符證書，請在證書菜單通過 DNS 方法獲取"

#: internal/service/file.go:174
msgid "open file error: %v"
msgstr "開啟檔案錯誤：%v"

#: internal/http/middleware/status.go:38
msgid "panel is closed"
msgstr "面板已關閉"

#: internal/http/middleware/status.go:30
msgid "panel is maintaining, please refresh later"
msgstr "面板正在維護，請稍後刷新"

#: internal/http/middleware/status.go:22
msgid "panel is upgrading, please refresh later"
msgstr "面板正在升級，請稍後刷新"

#: internal/http/middleware/status.go:46
msgid "panel run error, please check or contact support"
msgstr "面板運行錯誤，請檢查或聯繫技術支持"

#: internal/apps/php/app.go:328
msgid "pdo_pgsql is a PDO driver for connecting to PostgreSQL (requires PostgreSQL installed)"
msgstr "pdo_pgsql 是一個用於連接 PostgreSQL 的 PDO 驅動程序（需先安裝 PostgreSQL）"

#: internal/apps/php/app.go:338
msgid "pdo_sqlsrv is a PDO driver for connecting to SQL Server"
msgstr "pdo_sqlsrv 是一個用於連接 SQL Server 的 PDO 驅動程序"

#: internal/apps/php/app.go:323
msgid "pgsql is a driver for connecting to PostgreSQL (requires PostgreSQL installed)"
msgstr "pgsql 是一個用於連接 PostgreSQL 的驅動程序（需先安裝 PostgreSQL）"

#: internal/apps/phpmyadmin/app.go:43
#: internal/apps/phpmyadmin/app.go:54
msgid "phpMyAdmin directory not found"
msgstr "未找到 phpMyAdmin 目錄"

#: internal/apps/phpmyadmin/app.go:65
msgid "phpMyAdmin port not found"
msgstr "未找到 phpMyAdmin 端口"

#: internal/data/user.go:97
#: internal/service/file.go:135
#: internal/service/file.go:222
#: internal/service/file.go:251
msgid "please don't do this"
msgstr "請不要這樣做"

#: internal/data/cert.go:210
msgid "please retry the manual obtain operation"
msgstr "請重新操作手動簽發"

#: internal/data/setting.go:311
msgid "port is already in use"
msgstr "端口已被佔用"

#: internal/apps/php/app.go:428
msgid "protobuf is a library for serializing and deserializing data"
msgstr "protobuf 是一個用於序列化和反序列化數據的庫"

#: internal/apps/php/app.go:433
msgid "rdkafka is a library for connecting to Apache Kafka"
msgstr "rdkafka 是一個用於連接 Apache Kafka 的庫"

#: internal/apps/fail2ban/app.go:109
msgid "rule already exists"
msgstr "規則已存在"

#: internal/apps/fail2ban/app.go:228
msgid "rule not found"
msgstr "找不到規則"

#: internal/data/website.go:712
msgid "runtime directory comment count is incorrect, expected 1, actual %d"
msgstr "運行時目錄註釋數量不正確，預期為1，實際為%d"

#: internal/data/website.go:709
msgid "runtime directory comment not found"
msgstr "未找到運行目錄註釋"

#: internal/data/website.go:449
#: internal/data/website.go:716
msgid "runtime directory does not exist"
msgstr "運行目錄不存在"

#: internal/http/middleware/must_login.go:78
msgid "session expired, please login again"
msgstr "會話已過期，請重新登錄"

#: internal/data/user_token.go:138
msgid "signature expired"
msgstr "簽名已過期"

#: internal/apps/php/app.go:333
msgid "sqlsrv is a driver for connecting to SQL Server"
msgstr "sqlsrv 是一個用於連接 SQL Server 的驅動程序"

#: internal/data/database_server.go:161
#: internal/data/database_server.go:187
msgid "sync from server %s"
msgstr "從伺服器 %s 同步"

#: internal/service/backup.go:87
msgid "target backup %s already exists"
msgstr "目標備份 %s 已存在"

#: internal/service/file.go:80
msgid "target is a directory"
msgstr "目標是一個目錄"

#: internal/service/file.go:160
msgid "target path %s already exists"
msgstr "目標路徑 %s 已存在"

#: internal/service/dashboard.go:287
msgid "the current version is the latest version"
msgstr "當前版本是最新版本"

#: internal/data/cert.go:351
msgid "this certificate has not been obtained successfully and cannot be deployed"
msgstr "此證書尚未成功簽發，無法部署"

#: internal/data/cert.go:276
msgid "this certificate has not been obtained successfully and cannot be renewed"
msgstr "此證書尚未成功簽發，無法續期"

#: internal/data/cert.go:168
#: internal/data/cert.go:283
msgid "this certificate is not associated with a website and cannot be obtained. You can try to obtain it manually"
msgstr "此證書未與任何網站關聯，無法簽發。您可以嘗試使用手動簽發"

#: internal/data/cert.go:406
msgid "this certificate is not associated with an ACME account and cannot be obtained"
msgstr "此證書未與 ACME 帳戶關聯，無法簽發"

#: internal/data/user_token.go:113
msgid "token expired"
msgstr "令牌已過期"

#: internal/service/dashboard.go:228
#: internal/service/dashboard.go:264
msgid "unable to check for updates in offline mode"
msgstr "離線模式下無法檢查更新"

#: internal/service/dashboard.go:302
msgid "unable to update in offline mode"
msgstr "離線模式下無法更新"

#: internal/data/backup.go:131
#: internal/data/backup.go:212
#: internal/data/backup.go:95
msgid "unknown backup type"
msgstr "未知備份類型"

#: internal/apps/fail2ban/app.go:180
msgid "unknown service"
msgstr "未知服務"

#: internal/service/toolbox_benchmark.go:77
msgid "unknown test type"
msgstr "未知測試類型"

#: internal/data/cert_account.go:104
#: internal/data/cert_account.go:163
msgid "unsupported CA"
msgstr "不支援的 CA"

#: internal/service/backup.go:77
msgid "unsupported file type"
msgstr "不支援的檔案類型"

#: internal/data/cron.go:220
msgid "unsupported system"
msgstr "不支援的系統"

#: internal/data/cert.go:132
msgid "upload certificate cannot be set to auto renew"
msgstr "上傳的憑證無法設定為自動續簽"

#: internal/service/file.go:156
msgid "upload file error: %v"
msgstr "上傳檔案錯誤：%v"

#: internal/data/user.go:117
#: internal/data/user.go:124
#: internal/data/user.go:134
msgid "username or password error"
msgstr "使用者名稱或密碼錯誤"

#: internal/data/website.go:560
msgid "website %s has bound certificates, please delete the certificate first"
msgstr "網站 %s 已綁定證書，請先刪除證書"

#: internal/data/website.go:456
msgid "website directory does not exist"
msgstr "網站目錄不存在"

#: internal/data/cert.go:172
#: internal/data/cert.go:287
msgid "wildcard domains cannot use HTTP verification"
msgstr "萬用字元網域無法使用 HTTP 驗證"

#: internal/service/file.go:179
msgid "write file error: %v"
msgstr "寫入檔案錯誤：%v"

#: internal/http/middleware/must_login.go:58
msgid "ws not allowed"
msgstr "不允許 ws"

#: internal/apps/php/app.go:443
msgid "xdebug is a library for debugging and profiling PHP code"
msgstr "xdebug 是一個用於調試和分析 PHP 代碼的庫"

#: internal/apps/php/app.go:438
msgid "xhprof is a library for performance profiling"
msgstr "xhprof 是一個用於性能分析的庫"

#: internal/apps/php/app.go:448
msgid "yaml is a library for handling YAML"
msgstr "yaml 是一個用於處理 YAML 的庫"

#: internal/apps/php/app.go:453
msgid "zstd is a library for compressing and decompressing files"
msgstr "zstd 是一個壓縮和解壓文件的庫"

#: internal/data/backup.go:244
#: internal/data/backup.go:294
#: internal/data/backup.go:334
#: internal/data/backup.go:374
msgid "|-Backed up to file: %s"
msgstr "|-備份到檔案：%s"

#: internal/data/backup.go:511
#: internal/data/backup.go:537
msgid "|-Backup directory available Inode: %d"
msgstr "|-備份目錄可用 Inode：%d"

#: internal/data/backup.go:510
#: internal/data/backup.go:536
msgid "|-Backup directory available space: %s"
msgstr "|-備份目錄可用空間：%s"

#: internal/data/backup.go:625
msgid "|-Backup file used: %s"
msgstr "|-使用的備份檔案：%s"

#: internal/data/backup.go:765
msgid "|-Backup panel data failed, missing file"
msgstr "|-備份面板數據失敗，缺少檔案"

#: internal/data/backup.go:759
#: internal/data/backup.go:762
msgid "|-Backup panel data failed: %v"
msgstr "|-備份面板數據失敗：%v"

#: internal/data/backup.go:755
msgid "|-Backup panel data..."
msgstr "|-備份面板數據……"

#: internal/service/cli.go:662
#: internal/service/cli.go:678
msgid "|-Backup target: %s"
msgstr "|-備份目標：%s"

#: internal/data/backup.go:243
#: internal/data/backup.go:293
#: internal/data/backup.go:333
#: internal/data/backup.go:373
msgid "|-Backup time: %s"
msgstr "|-備份時間：%s"

#: internal/service/cli.go:676
msgid "|-Backup type: database"
msgstr "|-備份類型：數據庫"

#: internal/service/cli.go:692
msgid "|-Backup type: panel"
msgstr "|-備份類型：面板"

#: internal/service/cli.go:661
msgid "|-Backup type: website"
msgstr "|-備份類型：網站"

#: internal/data/backup.go:785
msgid "|-Clean up temporary file failed: %v"
msgstr "|-清理臨時文件失敗：%v"

#: internal/data/backup.go:747
msgid "|-Clean up verification file failed: %v"
msgstr "|-清理驗證文件失敗：%v"

#: internal/data/backup.go:606
msgid "|-Cleaned up temporary files, please run panel-cli update to update the panel"
msgstr "|-已清理臨時文件，請運行 panel-cli update 更新面板"

#: internal/data/backup.go:195
msgid "|-Cleaning expired file: %s"
msgstr "|-清理過期文件：%s"

#: internal/data/backup.go:772
msgid "|-Cleaning old version failed: %v"
msgstr "|-清理舊版本失敗：%v"

#: internal/data/backup.go:769
msgid "|-Cleaning old version..."
msgstr "|-清理舊版本……"

#: internal/service/cli.go:715
#: internal/service/cli.go:763
msgid "|-Cleaning target: %s"
msgstr "|-清理目標：%s"

#: internal/service/cli.go:714
#: internal/service/cli.go:762
msgid "|-Cleaning type: %s"
msgstr "|-清理類型：%s"

#: internal/service/cli.go:677
msgid "|-Database: %s"
msgstr "|-數據庫：%s"

#: internal/data/backup.go:723
msgid "|-Download link: %s"
msgstr "|-下載鏈接：%s"

#: internal/data/backup.go:805
msgid "|-Download panel service file failed: %v"
msgstr "|-下載面板服務文件失敗：%v"

#: internal/data/backup.go:728
msgid "|-Downloading..."
msgstr "|-下載中……"

#: internal/data/backup.go:724
msgid "|-File name: %s"
msgstr "|-文件名：%s"

#: internal/data/backup.go:704
msgid "|-Fix completed"
msgstr "|-修復完成"

#: internal/service/cli.go:716
#: internal/service/cli.go:764
msgid "|-Keep count: %d"
msgstr "|-保留數量：%d"

#: internal/data/backup.go:641
msgid "|-Move backup file..."
msgstr "|-移動備份檔案……"

#: internal/data/backup.go:811
msgid "|-Move panel-cli tool failed: %v"
msgstr "|-移動 panel-cli 工具失敗：%v"

#: internal/data/backup.go:795
msgid "|-Restore panel data failed, missing file"
msgstr "|-還原面板資料失敗，缺少檔案"

#: internal/data/backup.go:792
msgid "|-Restore panel data failed: %v"
msgstr "|-還原面板數據失敗：%v"

#: internal/data/backup.go:664
#: internal/data/backup.go:789
msgid "|-Restore panel data..."
msgstr "|-還原面板資料……"

#: internal/service/cli.go:740
msgid "|-Rotation target: %s"
msgstr "|-輪換目標：%s"

#: internal/service/cli.go:739
msgid "|-Rotation type: website"
msgstr "|-旋轉類型：website"

#: internal/data/backup.go:802
msgid "|-Run post-update script failed: %v"
msgstr "|-運行更新後腳本失敗：%v"

#: internal/data/backup.go:799
msgid "|-Run post-update script..."
msgstr "|-執行更新後腳本……"

#: internal/data/backup.go:684
#: internal/data/backup.go:815
msgid "|-Set key file permissions..."
msgstr "|-設定金鑰檔案權限……"

#: internal/data/backup.go:577
msgid "|-Start fixing the panel..."
msgstr "|-開始修復面板……"

#: internal/data/backup.go:509
msgid "|-Target file count: %d"
msgstr "|-目標檔案數量：%d"

#: internal/data/backup.go:508
#: internal/data/backup.go:535
msgid "|-Target size: %s"
msgstr "|-目標大小：%s"

#: internal/data/backup.go:722
msgid "|-Target version: %s"
msgstr "|-目標版本：%s"

#: internal/data/backup.go:630
msgid "|-Unzip backup file..."
msgstr "|-解壓備份檔案……"

#: internal/data/backup.go:782
msgid "|-Unzip new version failed, missing file"
msgstr "|-解壓新版本失敗，缺少檔案"

#: internal/data/backup.go:779
msgid "|-Unzip new version failed: %v"
msgstr "|-解壓新版本失敗：%v"

#: internal/data/backup.go:776
msgid "|-Unzip new version..."
msgstr "|-解壓新版本……"

#: internal/data/backup.go:822
msgid "|-Update completed"
msgstr "|-更新完成"

#: internal/data/backup.go:741
msgid "|-Verify download file..."
msgstr "|-驗證下載檔案……"

#: internal/data/backup.go:808
msgid "|-Write new panel version failed: %v"
msgstr "|-寫入新面板版本失敗：%v"

#: internal/service/cli.go:659
#: internal/service/cli.go:674
#: internal/service/cli.go:690
msgid "★ Start backup [%s]"
msgstr "★ 開始備份 [%s]"

#: internal/service/cli.go:712
msgid "★ Start cleaning [%s]"
msgstr "★ 開始清理 [%s]"

#: internal/service/cli.go:760
msgid "★ Start cleaning rotated logs [%s]"
msgstr "★ 開始清理已旋轉的日誌 [%s]"

#: internal/service/cli.go:737
msgid "★ Start log rotation [%s]"
msgstr "★ 開始日誌旋轉 [%s]"

#: internal/service/cli.go:667
#: internal/service/cli.go:683
#: internal/service/cli.go:697
msgid "☆ Backup successful [%s]"
msgstr "☆ 備份成功 [%s]"

#: internal/service/cli.go:721
#: internal/service/cli.go:769
msgid "☆ Cleaning successful [%s]"
msgstr "☆ 清理成功 [%s]"

#: internal/service/cli.go:745
msgid "☆ Rotation successful [%s]"
msgstr "☆ 旋轉成功 [%s]"

