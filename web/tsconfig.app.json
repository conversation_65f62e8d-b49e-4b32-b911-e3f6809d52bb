{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"composite": true, "noEmit": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ESNext", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "paths": {"~/*": ["./*"], "@/*": ["./src/*"]}, "resolveJsonModule": true, "types": ["node", "vite/client", "unplugin-icons/types/vue"], "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true}, "include": ["env.d.ts", "src/**/*", "types/**/*.d.ts", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"]}