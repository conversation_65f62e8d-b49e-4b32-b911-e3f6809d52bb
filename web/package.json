{"type": "module", "private": true, "author": {"name": "HaoZi Tech", "email": "<EMAIL>", "url": "https://git.haozi.net/org"}, "license": "MIT", "repository": {"url": "https://github.com/tnb-labs/panel"}, "scripts": {"dev": "vite", "build": "run-s gen-auto-import type-check build-only copy", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "run-s gen-auto-import lint-only", "lint-only": "eslint . --fix", "format": "prettier --write src/", "copy": "cpx \"dist/**/*\" \"../pkg/embed/frontend\" -C", "gen-auto-import": "tsx gen-auto-import.ts", "gettext:extract": "vue-gettext-extract", "gettext:compile": "vue-gettext-compile"}, "dependencies": {"@alova/adapter-xhr": "^2.1.1", "@eslint/eslintrc": "^3.2.0", "@fontsource-variable/jetbrains-mono": "^5.1.1", "@guolao/vue-monaco-editor": "^1.5.4", "@vavt/copy2clipboard": "^1.0.1", "@vue-js-cron/naive-ui": "^2.0.9", "@vueuse/core": "^13.0.0", "@xterm/addon-attach": "^0.11.0", "@xterm/addon-clipboard": "^0.1.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-unicode11": "^0.8.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/addon-webgl": "^0.18.0", "@xterm/xterm": "^5.5.0", "alova": "^3.2.7", "cronstrue": "^2.52.0", "echarts": "^5.6.0", "install": "^0.13.0", "lodash-es": "^4.17.21", "luxon": "^3.5.0", "marked": "^15.0.4", "mitt": "^3.0.1", "node-forge": "^1.3.1", "pinia": "^3.0.0", "pinia-plugin-persistedstate": "^4.2.0", "remove": "^0.1.5", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0", "vue3-gettext": "4.0.0-alpha.8"}, "devDependencies": {"@iconify/json": "^2.2.290", "@iconify/vue": "^5.0.0", "@rushstack/eslint-patch": "^1.10.4", "@tsconfig/node22": "^22.0.0", "@types/lodash-es": "^4.17.12", "@types/luxon": "^3.4.2", "@types/node": "^22.10.2", "@types/node-forge": "^1.3.11", "@unocss/eslint-config": "^66.0.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.2.0", "@vue/tsconfig": "^0.7.0", "colord": "^2.9.3", "cpx2": "^8.0.0", "eslint": "^9.17.0", "eslint-plugin-vue": "^10.0.0", "md-editor-v3": "^5.1.1", "monaco-editor": "^0.52.2", "naive-ui": "^2.40.4", "npm-run-all2": "^8.0.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "sass": "^1.83.0", "tsx": "^4.19.2", "typescript": "5.8.3", "unocss": "^66.0.0", "unplugin-auto-import": "^19.0.0", "unplugin-icons": "^22.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^6.0.6", "vite-plugin-html": "^3.2.2", "vite-plugin-mock": "^3.0.2", "vite-plugin-static-copy": "^3.0.0", "vite-plugin-vue-devtools": "^7.6.8", "vue-tsc": "^2.2.0"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}