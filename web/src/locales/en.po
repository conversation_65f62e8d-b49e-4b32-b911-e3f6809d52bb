msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/layout/sidebar/components/SideSetting.vue:63
#: src/layout/sidebar/components/SideSetting.vue:68
msgid "Menu Settings"
msgstr ""

#: src/layout/sidebar/components/SideSetting.vue:80
msgid ""
"Settings are saved in the browser and will be reset after clearing the "
"browser cache"
msgstr ""

#: src/layout/sidebar/components/SideSetting.vue:85
msgid "Custom Logo"
msgstr ""

#: src/layout/sidebar/components/SideSetting.vue:88
msgid "Please enter the complete URL"
msgstr ""

#: src/layout/sidebar/components/SideSetting.vue:91
msgid "Hide Menu"
msgstr ""

#: src/router/routes/index.ts:18
msgid "Login Page"
msgstr ""

#: src/views/app/IndexView.vue:19 src/views/app/IndexView.vue:159
#: src/views/app/IndexView.vue:164 src/views/apps/php/PhpView.vue:91
msgid "Install"
msgstr ""

#: src/views/app/IndexView.vue:37
msgid "App Name"
msgstr ""

#: src/views/app/IndexView.vue:44 src/views/apps/php/PhpView.vue:59
msgid "Description"
msgstr ""

#: src/views/app/IndexView.vue:51
msgid "Installed Version"
msgstr ""

#: src/views/app/IndexView.vue:57
msgid "Show in Home"
msgstr ""

#: src/views/app/IndexView.vue:71 src/views/apps/fail2ban/IndexView.vue:70
#: src/views/apps/fail2ban/IndexView.vue:132 src/views/apps/php/PhpView.vue:66
#: src/views/apps/pureftpd/IndexView.vue:55
#: src/views/apps/s3fs/IndexView.vue:33
#: src/views/apps/supervisor/IndexView.vue:87 src/views/backup/ListView.vue:58
#: src/views/cert/AccountView.vue:79 src/views/cert/CertView.vue:182
#: src/views/cert/DnsView.vue:64 src/views/container/ComposeView.vue:74
#: src/views/container/ContainerCreate.vue:170
#: src/views/container/ContainerCreate.vue:247
#: src/views/container/ContainerView.vue:87
#: src/views/container/ImageView.vue:69 src/views/container/NetworkView.vue:109
#: src/views/container/VolumeView.vue:63 src/views/database/DatabaseList.vue:74
#: src/views/database/ServerList.vue:135 src/views/database/UserList.vue:152
#: src/views/file/ListTable.vue:185 src/views/file/SearchModal.vue:48
#: src/views/firewall/ForwardView.vue:80 src/views/firewall/IpRuleView.vue:122
#: src/views/firewall/RuleView.vue:159 src/views/task/CronView.vue:103
#: src/views/task/SystemView.vue:96 src/views/task/TaskView.vue:55
#: src/views/website/IndexView.vue:95
msgid "Actions"
msgstr ""

#: src/views/app/IndexView.vue:91
msgid ""
"Updating app %{ app } may reset related configurations to default state, are "
"you sure to continue?"
msgstr ""

#: src/views/app/IndexView.vue:101 src/views/dashboard/IndexView.vue:448
#: src/views/dashboard/route.ts:32
msgid "Update"
msgstr ""

#: src/views/app/IndexView.vue:120
msgid "Manage"
msgstr ""

#: src/views/app/IndexView.vue:133
msgid "Are you sure to uninstall app %{ app }?"
msgstr ""

#: src/views/app/IndexView.vue:143
msgid "Uninstall"
msgstr ""

#: src/views/app/IndexView.vue:189
#, fuzzy
msgid "Setup successfully"
msgstr "Saved successfully"

#: src/views/app/IndexView.vue:195 src/views/app/IndexView.vue:201
#: src/views/app/VersionModal.vue:31
msgid "Task submitted, please check the progress in background tasks"
msgstr ""

#: src/views/app/IndexView.vue:212
#, fuzzy
msgid "Cache updated successfully"
msgstr "Saved successfully"

#: src/views/app/IndexView.vue:226
msgid "Update Cache"
msgstr ""

#: src/views/app/IndexView.vue:230
msgid ""
"Before updating apps, it is strongly recommended to backup/snapshot first, "
"so you can roll back immediately if there are any issues!"
msgstr ""

#: src/views/app/route.ts:19
msgid "Apps"
msgstr ""

#: src/views/app/VersionModal.vue:71
msgid "Channel"
msgstr ""

#: src/views/app/VersionModal.vue:78
msgid "Version"
msgstr ""

#: src/views/app/VersionModal.vue:79
msgid "Please select a channel"
msgstr ""

#: src/views/app/VersionModal.vue:89 src/views/apps/fail2ban/IndexView.vue:451
#: src/views/apps/pureftpd/IndexView.vue:320
#: src/views/apps/pureftpd/IndexView.vue:340
#: src/views/apps/s3fs/IndexView.vue:174
#: src/views/apps/supervisor/IndexView.vue:513
#: src/views/backup/ListView.vue:237 src/views/backup/ListView.vue:257
#: src/views/cert/AccountView.vue:258 src/views/cert/CertView.vue:513
#: src/views/cert/CertView.vue:537 src/views/cert/CreateAccountModal.vue:114
#: src/views/cert/CreateCertModal.vue:111 src/views/cert/CreateDnsModal.vue:173
#: src/views/cert/DnsView.vue:370 src/views/cert/ObtainModal.vue:126
#: src/views/cert/UploadCertModal.vue:55
#: src/views/container/ComposeView.vue:326
#: src/views/container/ComposeView.vue:356
#: src/views/container/ContainerCreate.vue:355
#: src/views/container/ContainerView.vue:478
#: src/views/container/ImageView.vue:216
#: src/views/container/NetworkView.vue:310
#: src/views/container/VolumeView.vue:210
#: src/views/database/CreateDatabaseModal.vue:127
#: src/views/database/CreateServerModal.vue:120
#: src/views/database/CreateUserModal.vue:115
#: src/views/database/UpdateServerModal.vue:112
#: src/views/database/UpdateUserModal.vue:70 src/views/file/ToolBar.vue:261
#: src/views/file/ToolBar.vue:282 src/views/firewall/CreateForwardModal.vue:88
#: src/views/firewall/CreateIpModal.vue:122
#: src/views/firewall/CreateModal.vue:143 src/views/ssh/CreateModal.vue:98
#: src/views/ssh/UpdateModal.vue:104 src/views/task/CreateModal.vue:159
#: src/views/website/ProxyBuilderModal.vue:189
msgid "Submit"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:12
#: src/views/apps/benchmark/IndexView.vue:165
msgid "CPU"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:67
#: src/views/apps/benchmark/IndexView.vue:68
#: src/views/apps/benchmark/IndexView.vue:74
#: src/views/apps/benchmark/IndexView.vue:75
#: src/views/apps/benchmark/IndexView.vue:76
#: src/views/apps/benchmark/IndexView.vue:77
#: src/views/apps/benchmark/IndexView.vue:80
#: src/views/apps/benchmark/IndexView.vue:81
#: src/views/apps/benchmark/IndexView.vue:82
#: src/views/apps/benchmark/IndexView.vue:83
#: src/views/apps/benchmark/IndexView.vue:86
#: src/views/apps/benchmark/IndexView.vue:87
#: src/views/apps/benchmark/IndexView.vue:88
#: src/views/apps/benchmark/IndexView.vue:89
#: src/views/apps/benchmark/IndexView.vue:92
#: src/views/apps/benchmark/IndexView.vue:93
#: src/views/apps/benchmark/IndexView.vue:94
#: src/views/apps/benchmark/IndexView.vue:95
#: src/views/apps/benchmark/IndexView.vue:156
#: src/views/apps/benchmark/IndexView.vue:256
#: src/views/apps/benchmark/IndexView.vue:287
msgid "Pending benchmark"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:129
msgid ""
"Benchmark results are for reference only and may differ from actual "
"performance due to system resource scheduling, caching, and other factors!"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:136
msgid "Benchmarking in progress, it may take some time..."
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:139
msgid "Current project: %{ current }"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:151
msgid "Single-core"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:153
msgid "Multi-core"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:170
msgid "Image Processing"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:173
#: src/views/apps/benchmark/IndexView.vue:184
#: src/views/apps/benchmark/IndexView.vue:195
#: src/views/apps/benchmark/IndexView.vue:206
#: src/views/apps/benchmark/IndexView.vue:217
#: src/views/apps/benchmark/IndexView.vue:228
#: src/views/apps/benchmark/IndexView.vue:239
msgid "Single-core %{ single } / Multi-core %{ multi }"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:181
msgid "Machine Learning"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:192
msgid "Program Compilation"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:203
msgid "AES Encryption"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:214
msgid "Compression/Decompression"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:225
msgid "Physics Simulation"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:236
msgid "JSON Parsing"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:265
#: src/views/container/ContainerCreate.vue:291
#: src/views/dashboard/IndexView.vue:530 src/views/monitor/IndexView.vue:205
#: src/views/monitor/IndexView.vue:216 src/views/monitor/IndexView.vue:238
#: src/views/task/SystemView.vue:78
msgid "Memory"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:270
msgid "Memory Bandwidth"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:274
msgid "Memory Latency"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:296
#: src/views/dashboard/IndexView.vue:795 src/views/dashboard/IndexView.vue:815
msgid "Disk"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:301
msgid "4KB Read"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:304
#: src/views/apps/benchmark/IndexView.vue:315
#: src/views/apps/benchmark/IndexView.vue:326
#: src/views/apps/benchmark/IndexView.vue:337
#: src/views/apps/benchmark/IndexView.vue:348
#: src/views/apps/benchmark/IndexView.vue:359
#: src/views/apps/benchmark/IndexView.vue:370
#: src/views/apps/benchmark/IndexView.vue:381
msgid "Speed %{ speed } / %{ iops } IOPS"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:312
msgid "4KB Write"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:323
msgid "64KB Read"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:334
msgid "64KB Write"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:345
msgid "512KB Read"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:356
msgid "512KB Write"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:367
msgid "1MB Read"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:378
msgid "1MB Write"
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:402
msgid "Benchmarking..."
msgstr ""

#: src/views/apps/benchmark/IndexView.vue:402
msgid "Start Benchmark"
msgstr ""

#: src/views/apps/benchmark/route.ts:17
msgid "Rat Benchmark"
msgstr ""

#: src/views/apps/docker/IndexView.vue:25
#: src/views/apps/fail2ban/IndexView.vue:42 src/views/apps/frp/IndexView.vue:30
#: src/views/apps/frp/IndexView.vue:31 src/views/apps/gitea/IndexView.vue:20
#: src/views/apps/memcached/IndexView.vue:22
#: src/views/apps/minio/IndexView.vue:20 src/views/apps/mysql/IndexView.vue:35
#: src/views/apps/nginx/IndexView.vue:33 src/views/apps/php/PhpView.vue:47
#: src/views/apps/podman/IndexView.vue:21
#: src/views/apps/postgresql/IndexView.vue:35
#: src/views/apps/pureftpd/IndexView.vue:25
#: src/views/apps/redis/IndexView.vue:29
#: src/views/apps/supervisor/IndexView.vue:54
#: src/views/dashboard/IndexView.vue:133
msgid "Running normally"
msgstr ""

#: src/views/apps/docker/IndexView.vue:25
#: src/views/apps/fail2ban/IndexView.vue:42 src/views/apps/frp/IndexView.vue:30
#: src/views/apps/frp/IndexView.vue:31 src/views/apps/gitea/IndexView.vue:20
#: src/views/apps/memcached/IndexView.vue:22
#: src/views/apps/minio/IndexView.vue:20 src/views/apps/mysql/IndexView.vue:35
#: src/views/apps/nginx/IndexView.vue:33 src/views/apps/php/PhpView.vue:47
#: src/views/apps/podman/IndexView.vue:21
#: src/views/apps/postgresql/IndexView.vue:35
#: src/views/apps/pureftpd/IndexView.vue:25
#: src/views/apps/redis/IndexView.vue:29
#: src/views/apps/supervisor/IndexView.vue:54 src/views/task/SystemView.vue:54
msgid "Stopped"
msgstr ""

#: src/views/apps/docker/IndexView.vue:38
#: src/views/apps/fail2ban/IndexView.vue:176
#: src/views/apps/frp/IndexView.vue:53 src/views/apps/gitea/IndexView.vue:37
#: src/views/apps/memcached/IndexView.vue:61
#: src/views/apps/minio/IndexView.vue:37 src/views/apps/mysql/IndexView.vue:64
#: src/views/apps/nginx/IndexView.vue:62 src/views/apps/php/PhpView.vue:162
#: src/views/apps/php/PhpView.vue:168
#: src/views/apps/phpmyadmin/IndexView.vue:37
#: src/views/apps/phpmyadmin/IndexView.vue:44
#: src/views/apps/podman/IndexView.vue:39
#: src/views/apps/podman/IndexView.vue:45
#: src/views/apps/postgresql/IndexView.vue:64
#: src/views/apps/postgresql/IndexView.vue:69
#: src/views/apps/pureftpd/IndexView.vue:133
#: src/views/apps/redis/IndexView.vue:58
#: src/views/apps/supervisor/IndexView.vue:241
#: src/views/apps/supervisor/IndexView.vue:341
#: src/views/apps/toolbox/IndexView.vue:51
#: src/views/apps/toolbox/IndexView.vue:57
#: src/views/apps/toolbox/IndexView.vue:66
#: src/views/apps/toolbox/IndexView.vue:72
#: src/views/apps/toolbox/IndexView.vue:81 src/views/setting/SettingBase.vue:35
#: src/views/setting/SettingHttps.vue:27 src/views/website/EditView.vue:115
msgid "Saved successfully"
msgstr "Saved successfully"

#: src/views/apps/docker/IndexView.vue:44
#: src/views/apps/fail2ban/IndexView.vue:211
#: src/views/apps/frp/IndexView.vue:60 src/views/apps/gitea/IndexView.vue:43
#: src/views/apps/memcached/IndexView.vue:67
#: src/views/apps/minio/IndexView.vue:43 src/views/apps/mysql/IndexView.vue:93
#: src/views/apps/nginx/IndexView.vue:85 src/views/apps/php/PhpView.vue:197
#: src/views/apps/podman/IndexView.vue:51
#: src/views/apps/postgresql/IndexView.vue:90
#: src/views/apps/pureftpd/IndexView.vue:139
#: src/views/apps/redis/IndexView.vue:64
#: src/views/apps/supervisor/IndexView.vue:268
#: src/views/apps/supervisor/IndexView.vue:297
#, fuzzy
msgid "Started successfully"
msgstr "Saved successfully"

#: src/views/apps/docker/IndexView.vue:51
#: src/views/apps/fail2ban/IndexView.vue:228
#: src/views/apps/frp/IndexView.vue:66 src/views/apps/gitea/IndexView.vue:49
#: src/views/apps/memcached/IndexView.vue:84
#: src/views/apps/minio/IndexView.vue:49 src/views/apps/mysql/IndexView.vue:99
#: src/views/apps/nginx/IndexView.vue:91 src/views/apps/php/PhpView.vue:203
#: src/views/apps/podman/IndexView.vue:57
#: src/views/apps/postgresql/IndexView.vue:96
#: src/views/apps/pureftpd/IndexView.vue:156
#: src/views/apps/redis/IndexView.vue:81
#: src/views/apps/supervisor/IndexView.vue:275
#: src/views/apps/supervisor/IndexView.vue:304
#, fuzzy
msgid "Stopped successfully"
msgstr "Saved successfully"

#: src/views/apps/docker/IndexView.vue:58
#: src/views/apps/fail2ban/IndexView.vue:234
#: src/views/apps/frp/IndexView.vue:72 src/views/apps/gitea/IndexView.vue:55
#: src/views/apps/memcached/IndexView.vue:90
#: src/views/apps/minio/IndexView.vue:55 src/views/apps/mysql/IndexView.vue:105
#: src/views/apps/nginx/IndexView.vue:97 src/views/apps/php/PhpView.vue:209
#: src/views/apps/podman/IndexView.vue:63
#: src/views/apps/postgresql/IndexView.vue:102
#: src/views/apps/pureftpd/IndexView.vue:162
#: src/views/apps/redis/IndexView.vue:87
#: src/views/apps/supervisor/IndexView.vue:282
#: src/views/apps/supervisor/IndexView.vue:311
#, fuzzy
msgid "Restarted successfully"
msgstr "Saved successfully"

#: src/views/apps/docker/IndexView.vue:66
#: src/views/apps/fail2ban/IndexView.vue:218
#: src/views/apps/frp/IndexView.vue:79 src/views/apps/gitea/IndexView.vue:62
#: src/views/apps/memcached/IndexView.vue:74
#: src/views/apps/minio/IndexView.vue:62 src/views/apps/mysql/IndexView.vue:83
#: src/views/apps/nginx/IndexView.vue:75 src/views/apps/php/PhpView.vue:187
#: src/views/apps/podman/IndexView.vue:70
#: src/views/apps/postgresql/IndexView.vue:80
#: src/views/apps/redis/IndexView.vue:71
#: src/views/apps/supervisor/IndexView.vue:255
#, fuzzy
msgid "Autostart enabled successfully"
msgstr "Saved successfully"

#: src/views/apps/docker/IndexView.vue:69
#: src/views/apps/fail2ban/IndexView.vue:221
#: src/views/apps/frp/IndexView.vue:82 src/views/apps/gitea/IndexView.vue:65
#: src/views/apps/memcached/IndexView.vue:77
#: src/views/apps/minio/IndexView.vue:65 src/views/apps/mysql/IndexView.vue:86
#: src/views/apps/nginx/IndexView.vue:78 src/views/apps/php/PhpView.vue:190
#: src/views/apps/podman/IndexView.vue:73
#: src/views/apps/postgresql/IndexView.vue:83
#: src/views/apps/redis/IndexView.vue:74
#: src/views/apps/supervisor/IndexView.vue:260
#, fuzzy
msgid "Autostart disabled successfully"
msgstr "Saved successfully"

#: src/views/apps/docker/IndexView.vue:90 src/views/apps/frp/IndexView.vue:135
#: src/views/apps/frp/IndexView.vue:191 src/views/apps/gitea/IndexView.vue:87
#: src/views/apps/memcached/IndexView.vue:110
#: src/views/apps/minio/IndexView.vue:82 src/views/apps/mysql/IndexView.vue:130
#: src/views/apps/nginx/IndexView.vue:123 src/views/apps/php/PhpView.vue:250
#: src/views/apps/php/PhpView.vue:259
#: src/views/apps/phpmyadmin/IndexView.vue:58
#: src/views/apps/phpmyadmin/IndexView.vue:67
#: src/views/apps/podman/IndexView.vue:95
#: src/views/apps/podman/IndexView.vue:104
#: src/views/apps/postgresql/IndexView.vue:128
#: src/views/apps/postgresql/IndexView.vue:137
#: src/views/apps/pureftpd/IndexView.vue:209
#: src/views/apps/redis/IndexView.vue:107
#: src/views/apps/supervisor/IndexView.vue:362
#: src/views/apps/toolbox/IndexView.vue:97
#: src/views/apps/toolbox/IndexView.vue:101
#: src/views/apps/toolbox/IndexView.vue:105
#: src/views/apps/toolbox/IndexView.vue:109 src/views/file/EditModal.vue:31
#: src/views/file/ListTable.vue:703 src/views/setting/SettingBase.vue:95
#: src/views/setting/SettingHttps.vue:56 src/views/website/EditView.vue:211
msgid "Save"
msgstr "Save"

#: src/views/apps/docker/IndexView.vue:94
#: src/views/apps/docker/IndexView.vue:96
#: src/views/apps/fail2ban/IndexView.vue:309
#: src/views/apps/fail2ban/IndexView.vue:311
#: src/views/apps/frp/IndexView.vue:99 src/views/apps/frp/IndexView.vue:155
#: src/views/apps/gitea/IndexView.vue:91 src/views/apps/gitea/IndexView.vue:92
#: src/views/apps/memcached/IndexView.vue:114
#: src/views/apps/memcached/IndexView.vue:116
#: src/views/apps/minio/IndexView.vue:86 src/views/apps/minio/IndexView.vue:87
#: src/views/apps/mysql/IndexView.vue:152
#: src/views/apps/mysql/IndexView.vue:154
#: src/views/apps/nginx/IndexView.vue:136
#: src/views/apps/nginx/IndexView.vue:137 src/views/apps/php/PhpView.vue:281
#: src/views/apps/php/PhpView.vue:283 src/views/apps/podman/IndexView.vue:108
#: src/views/apps/podman/IndexView.vue:113
#: src/views/apps/postgresql/IndexView.vue:145
#: src/views/apps/postgresql/IndexView.vue:147
#: src/views/apps/pureftpd/IndexView.vue:222
#: src/views/apps/pureftpd/IndexView.vue:224
#: src/views/apps/redis/IndexView.vue:111
#: src/views/apps/redis/IndexView.vue:113
#: src/views/apps/supervisor/IndexView.vue:379
#: src/views/apps/supervisor/IndexView.vue:381
#: src/views/container/ContainerView.vue:80
msgid "Running Status"
msgstr ""

#: src/views/apps/docker/IndexView.vue:99
#: src/views/apps/fail2ban/IndexView.vue:314
#: src/views/apps/frp/IndexView.vue:102 src/views/apps/frp/IndexView.vue:158
#: src/views/apps/gitea/IndexView.vue:95
#: src/views/apps/memcached/IndexView.vue:119
#: src/views/apps/minio/IndexView.vue:90 src/views/apps/mysql/IndexView.vue:157
#: src/views/apps/nginx/IndexView.vue:140 src/views/apps/php/PhpView.vue:286
#: src/views/apps/podman/IndexView.vue:116
#: src/views/apps/postgresql/IndexView.vue:150
#: src/views/apps/redis/IndexView.vue:116
#: src/views/apps/supervisor/IndexView.vue:384
msgid "Autostart On"
msgstr ""

#: src/views/apps/docker/IndexView.vue:100
#: src/views/apps/fail2ban/IndexView.vue:315
#: src/views/apps/frp/IndexView.vue:103 src/views/apps/frp/IndexView.vue:159
#: src/views/apps/gitea/IndexView.vue:96
#: src/views/apps/memcached/IndexView.vue:120
#: src/views/apps/minio/IndexView.vue:91 src/views/apps/mysql/IndexView.vue:158
#: src/views/apps/nginx/IndexView.vue:141 src/views/apps/php/PhpView.vue:287
#: src/views/apps/podman/IndexView.vue:117
#: src/views/apps/postgresql/IndexView.vue:151
#: src/views/apps/redis/IndexView.vue:117
#: src/views/apps/supervisor/IndexView.vue:385
msgid "Autostart Off"
msgstr ""

#: src/views/apps/docker/IndexView.vue:110
#: src/views/apps/fail2ban/IndexView.vue:325
#: src/views/apps/frp/IndexView.vue:113 src/views/apps/frp/IndexView.vue:169
#: src/views/apps/gitea/IndexView.vue:106
#: src/views/apps/memcached/IndexView.vue:130
#: src/views/apps/minio/IndexView.vue:101
#: src/views/apps/mysql/IndexView.vue:168
#: src/views/apps/nginx/IndexView.vue:151 src/views/apps/php/PhpView.vue:297
#: src/views/apps/podman/IndexView.vue:127
#: src/views/apps/postgresql/IndexView.vue:161
#: src/views/apps/pureftpd/IndexView.vue:238
#: src/views/apps/redis/IndexView.vue:127
#: src/views/apps/supervisor/IndexView.vue:131
#: src/views/apps/supervisor/IndexView.vue:395
#: src/views/container/ComposeView.vue:150
#: src/views/container/ContainerView.vue:127
#: src/views/container/ContainerView.vue:405
msgid "Start"
msgstr ""

#: src/views/apps/docker/IndexView.vue:116
#: src/views/apps/fail2ban/IndexView.vue:331
#: src/views/apps/frp/IndexView.vue:119 src/views/apps/frp/IndexView.vue:175
#: src/views/apps/gitea/IndexView.vue:112
#: src/views/apps/memcached/IndexView.vue:136
#: src/views/apps/minio/IndexView.vue:107
#: src/views/apps/mysql/IndexView.vue:174
#: src/views/apps/nginx/IndexView.vue:157 src/views/apps/php/PhpView.vue:303
#: src/views/apps/podman/IndexView.vue:133
#: src/views/apps/postgresql/IndexView.vue:167
#: src/views/apps/pureftpd/IndexView.vue:244
#: src/views/apps/redis/IndexView.vue:133
#: src/views/apps/supervisor/IndexView.vue:155
#: src/views/apps/supervisor/IndexView.vue:401
#: src/views/container/ComposeView.vue:179
#: src/views/container/ContainerView.vue:132
#: src/views/container/ContainerView.vue:406
msgid "Stop"
msgstr ""

#: src/views/apps/docker/IndexView.vue:119
msgid "Are you sure you want to stop Docker?"
msgstr ""

#: src/views/apps/docker/IndexView.vue:123
#: src/views/apps/fail2ban/IndexView.vue:338
#: src/views/apps/frp/IndexView.vue:126 src/views/apps/frp/IndexView.vue:182
#: src/views/apps/gitea/IndexView.vue:119
#: src/views/apps/memcached/IndexView.vue:143
#: src/views/apps/minio/IndexView.vue:114
#: src/views/apps/mysql/IndexView.vue:181
#: src/views/apps/nginx/IndexView.vue:164 src/views/apps/php/PhpView.vue:310
#: src/views/apps/podman/IndexView.vue:140
#: src/views/apps/postgresql/IndexView.vue:174
#: src/views/apps/pureftpd/IndexView.vue:251
#: src/views/apps/redis/IndexView.vue:140
#: src/views/apps/supervisor/IndexView.vue:182
#: src/views/apps/supervisor/IndexView.vue:408
#: src/views/container/ContainerView.vue:137
#: src/views/container/ContainerView.vue:407
#: src/views/dashboard/IndexView.vue:444
msgid "Restart"
msgstr ""

#: src/views/apps/docker/IndexView.vue:130 src/views/website/EditView.vue:388
msgid "Configuration"
msgstr ""

#: src/views/apps/docker/IndexView.vue:133
msgid "This modifies the Docker configuration file (/etc/docker/daemon.json)"
msgstr ""

#: src/views/apps/docker/IndexView.vue:149
#: src/views/apps/fail2ban/IndexView.vue:381
#: src/views/apps/gitea/IndexView.vue:144
#: src/views/apps/memcached/IndexView.vue:176
#: src/views/apps/minio/IndexView.vue:139
#: src/views/apps/mysql/IndexView.vue:227
#: src/views/apps/nginx/IndexView.vue:203 src/views/apps/php/PhpView.vue:382
#: src/views/apps/podman/IndexView.vue:185
#: src/views/apps/postgresql/IndexView.vue:233
#: src/views/apps/redis/IndexView.vue:176
#: src/views/apps/supervisor/IndexView.vue:458
msgid "Runtime Logs"
msgstr ""

#: src/views/apps/docker/route.ts:17
msgid "Docker"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:47
#: src/views/apps/supervisor/IndexView.vue:59
#: src/views/apps/supervisor/IndexView.vue:477
#: src/views/container/ComposeView.vue:31
#: src/views/container/NetworkView.vue:45 src/views/container/VolumeView.vue:26
#: src/views/database/CreateServerModal.vue:55
#: src/views/database/ServerList.vue:40
#: src/views/database/UpdateServerModal.vue:55 src/views/file/ListTable.vue:99
#: src/views/file/SearchModal.vue:20 src/views/file/ToolBar.vue:257
#: src/views/ssh/CreateModal.vue:57 src/views/ssh/UpdateModal.vue:63
#: src/views/task/SystemView.vue:18
msgid "Name"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:53
#: src/views/apps/phpmyadmin/IndexView.vue:71
#: src/views/apps/supervisor/IndexView.vue:66
#: src/views/container/ComposeView.vue:58
#: src/views/container/ContainerView.vue:32
#: src/views/database/ServerList.vue:114 src/views/database/UserList.vue:131
#: src/views/firewall/RuleView.vue:62 src/views/task/SystemView.vue:43
#: src/views/task/TaskView.vue:22
msgid "Status"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:66
#: src/views/apps/fail2ban/IndexView.vue:441
msgid "Max Retries"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:67
#: src/views/apps/fail2ban/IndexView.vue:447
msgid "Ban Time"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:68
#: src/views/apps/fail2ban/IndexView.vue:444
msgid "Find Time"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:89 src/views/cert/CertView.vue:265
msgid "View"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:100
msgid "Are you sure you want to delete rule %{ name }?"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:111 src/views/apps/php/PhpView.vue:117
#: src/views/apps/pureftpd/IndexView.vue:97
#: src/views/apps/supervisor/IndexView.vue:208 src/views/backup/ListView.vue:99
#: src/views/cert/AccountView.vue:128 src/views/cert/CertView.vue:316
#: src/views/cert/DnsView.vue:112 src/views/container/ComposeView.vue:208
#: src/views/container/ContainerCreate.vue:229
#: src/views/container/ContainerCreate.vue:276
#: src/views/container/ContainerView.vue:157
#: src/views/container/ContainerView.vue:411
#: src/views/container/ImageView.vue:95 src/views/container/NetworkView.vue:135
#: src/views/container/VolumeView.vue:89 src/views/database/DatabaseList.vue:99
#: src/views/database/ServerList.vue:218 src/views/database/UserList.vue:192
#: src/views/file/ListTable.vue:81 src/views/file/ListTable.vue:285
#: src/views/file/SearchModal.vue:98 src/views/file/ToolBar.vue:238
#: src/views/firewall/ForwardView.vue:105 src/views/firewall/IpRuleView.vue:147
#: src/views/firewall/RuleView.vue:184 src/views/ssh/IndexView.vue:93
#: src/views/task/CronView.vue:157 src/views/task/TaskView.vue:99
#: src/views/website/IndexView.vue:169
msgid "Delete"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:146
msgid "Are you sure you want to unban %{ ip }?"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:156
msgid "Unban"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:240
#: src/views/apps/nginx/IndexView.vue:103 src/views/apps/php/PhpView.vue:215
#: src/views/apps/postgresql/IndexView.vue:108
#, fuzzy
msgid "Reloaded successfully"
msgstr "Saved successfully"

#: src/views/apps/fail2ban/IndexView.vue:247
#: src/views/apps/pureftpd/IndexView.vue:175
#: src/views/apps/s3fs/IndexView.vue:83
#: src/views/apps/supervisor/IndexView.vue:290
#: src/views/database/CreateServerModal.vue:37
#, fuzzy
msgid "Added successfully"
msgstr "Saved successfully"

#: src/views/apps/fail2ban/IndexView.vue:255
#: src/views/apps/pureftpd/IndexView.vue:192
#: src/views/apps/s3fs/IndexView.vue:90
#: src/views/apps/supervisor/IndexView.vue:318
#: src/views/backup/ListView.vue:149 src/views/database/DatabaseList.vue:124
#: src/views/database/ServerList.vue:243 src/views/database/UserList.vue:217
#: src/views/file/ListTable.vue:268 src/views/file/ListTable.vue:595
#: src/views/file/SearchModal.vue:81 src/views/file/ToolBar.vue:189
#: src/views/firewall/ForwardView.vue:132
#: src/views/firewall/ForwardView.vue:150 src/views/firewall/IpRuleView.vue:174
#: src/views/firewall/IpRuleView.vue:192 src/views/firewall/RuleView.vue:211
#: src/views/firewall/RuleView.vue:229 src/views/task/CronView.vue:200
#: src/views/task/TaskView.vue:125 src/views/website/IndexView.vue:270
#: src/views/website/IndexView.vue:324
#, fuzzy
msgid "Deleted successfully"
msgstr "Saved successfully"

#: src/views/apps/fail2ban/IndexView.vue:268
#, fuzzy
msgid "Unbanned successfully"
msgstr "Saved successfully"

#: src/views/apps/fail2ban/IndexView.vue:296
msgid "Save Whitelist"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:305
#: src/views/apps/fail2ban/IndexView.vue:386
#: src/views/apps/fail2ban/IndexView.vue:387
msgid "Add Rule"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:334
msgid ""
"Stopping Fail2ban will disable all rules. Are you sure you want to stop?"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:342
#: src/views/apps/nginx/IndexView.vue:168 src/views/apps/php/PhpView.vue:314
#: src/views/apps/postgresql/IndexView.vue:178
msgid "Reload"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:347
msgid "IP Whitelist"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:352
msgid "IP whitelist, separated by commas"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:357
msgid "Rule Management"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:358
msgid "Rule List"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:390
msgid ""
"If an IP exceeds the maximum retries within the find time (seconds), it will "
"be banned for the ban time (seconds)"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:393
msgid ""
"Protected ports are automatically obtained. If you modify the port "
"corresponding to a rule, please delete and re-add the rule, otherwise "
"protection may not be effective"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:397 src/views/cert/CertView.vue:87
#: src/views/cert/DnsView.vue:38 src/views/cert/ObtainModal.vue:54
#: src/views/database/CreateServerModal.vue:63
#: src/views/database/DatabaseList.vue:12 src/views/database/ServerList.vue:17
#: src/views/database/UserList.vue:17
msgid "Type"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:401 src/views/backup/IndexView.vue:37
#: src/views/backup/ListView.vue:217 src/views/backup/ListView.vue:250
#: src/views/cert/CertView.vue:464 src/views/cert/CertView.vue:527
#: src/views/cert/CreateCertModal.vue:86 src/views/dashboard/IndexView.vue:418
#: src/views/task/CreateModal.vue:130
msgid "Website"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:402
#: src/views/apps/fail2ban/IndexView.vue:430
msgid "Service"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:407
#: src/views/apps/fail2ban/IndexView.vue:411 src/views/task/CreateModal.vue:140
#: src/views/task/CreateModal.vue:142
#, fuzzy
msgid "Select Website"
msgstr "Certificate"

#: src/views/apps/fail2ban/IndexView.vue:414
msgid "Protection Mode"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:419
#: src/views/apps/pureftpd/IndexView.vue:48
msgid "Path"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:426
#: src/views/apps/fail2ban/IndexView.vue:428
msgid "Protection Path"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:455
#: src/views/apps/fail2ban/IndexView.vue:456
#, fuzzy
msgid "View Rule"
msgstr "Certificate"

#: src/views/apps/fail2ban/IndexView.vue:458
#, fuzzy
msgid "Rule Information"
msgstr "Certificate"

#: src/views/apps/fail2ban/IndexView.vue:461
msgid "Currently Banned"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:465
msgid "Total Bans"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:470
msgid "Ban List"
msgstr ""

#: src/views/apps/fail2ban/route.ts:17
msgid "Fail2ban"
msgstr ""

#: src/views/apps/frp/IndexView.vue:122
msgid "Are you sure you want to stop Frps?"
msgstr ""

#: src/views/apps/frp/IndexView.vue:131 src/views/apps/frp/IndexView.vue:187
#: src/views/apps/gitea/IndexView.vue:125
#: src/views/apps/mysql/IndexView.vue:198
#: src/views/apps/nginx/IndexView.vue:174
#: src/views/apps/phpmyadmin/IndexView.vue:84
#, fuzzy
msgid "Modify Configuration"
msgstr "Certificate"

#: src/views/apps/frp/IndexView.vue:178
msgid "Are you sure you want to stop Frpc?"
msgstr ""

#: src/views/apps/frp/route.ts:17
msgid "Frp"
msgstr ""

#: src/views/apps/gitea/IndexView.vue:115
msgid "Are you sure you want to stop Gitea?"
msgstr ""

#: src/views/apps/gitea/IndexView.vue:128
msgid ""
"This modifies the Gitea configuration file. If you do not understand the "
"meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/gitea/route.ts:17
msgid "Gitea"
msgstr ""

#: src/views/apps/memcached/IndexView.vue:27
#: src/views/apps/mysql/IndexView.vue:40 src/views/apps/nginx/IndexView.vue:38
#: src/views/apps/php/PhpView.vue:132
#: src/views/apps/postgresql/IndexView.vue:40
#: src/views/apps/redis/IndexView.vue:34
msgid "Property"
msgstr ""

#: src/views/apps/memcached/IndexView.vue:34
#: src/views/apps/mysql/IndexView.vue:47 src/views/apps/nginx/IndexView.vue:45
#: src/views/apps/php/PhpView.vue:139
#: src/views/apps/postgresql/IndexView.vue:47
#: src/views/apps/redis/IndexView.vue:41
msgid "Current Value"
msgstr ""

#: src/views/apps/memcached/IndexView.vue:139
msgid ""
"Stopping Memcached will cause websites using Memcached to become "
"inaccessible. Are you sure you want to stop?"
msgstr ""

#: src/views/apps/memcached/IndexView.vue:150
#, fuzzy
msgid "Service Configuration"
msgstr "Certificate"

#: src/views/apps/memcached/IndexView.vue:166
#: src/views/apps/mysql/IndexView.vue:217
#: src/views/apps/nginx/IndexView.vue:193 src/views/apps/php/PhpView.vue:372
#: src/views/apps/postgresql/IndexView.vue:223
#: src/views/apps/redis/IndexView.vue:166 src/views/dashboard/IndexView.vue:459
msgid "Load Status"
msgstr ""

#: src/views/apps/memcached/route.ts:17
msgid "Memcached"
msgstr ""

#: src/views/apps/minio/IndexView.vue:110
msgid "Are you sure you want to stop Minio?"
msgstr ""

#: src/views/apps/minio/IndexView.vue:120
#: src/views/container/ComposeView.vue:316
#: src/views/container/ComposeView.vue:346
#: src/views/container/ContainerCreate.vue:337
msgid "Environment Variables"
msgstr ""

#: src/views/apps/minio/IndexView.vue:123
msgid ""
"This is modifying the Minio environment variable file /etc/default/minio. If "
"you do not understand the meaning of each parameter, please do not modify it "
"arbitrarily!"
msgstr ""

#: src/views/apps/minio/route.ts:17
msgid "Minio"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:70 src/views/apps/mysql/IndexView.vue:76
#: src/views/apps/nginx/IndexView.vue:68 src/views/apps/php/PhpView.vue:174
#: src/views/apps/php/PhpView.vue:180
#: src/views/apps/postgresql/IndexView.vue:74
#: src/views/apps/supervisor/IndexView.vue:247
#: src/views/website/EditView.vue:160
#, fuzzy
msgid "Cleared successfully"
msgstr "Saved successfully"

#: src/views/apps/mysql/IndexView.vue:111
#: src/views/apps/pureftpd/IndexView.vue:185
#: src/views/database/DatabaseList.vue:130
#: src/views/database/ServerList.vue:249
#: src/views/database/UpdateServerModal.vue:21
#: src/views/database/UpdateUserModal.vue:18
#: src/views/database/UserList.vue:223 src/views/file/PermissionModal.vue:29
#: src/views/task/CronView.vue:182 src/views/task/CronView.vue:209
#: src/views/website/IndexView.vue:253 src/views/website/IndexView.vue:279
#, fuzzy
msgid "Modified successfully"
msgstr "Saved successfully"

#: src/views/apps/mysql/IndexView.vue:139
#: src/views/apps/nginx/IndexView.vue:132
#: src/views/apps/postgresql/IndexView.vue:141
#: src/views/apps/supervisor/IndexView.vue:375
msgid "Clear Log"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:148 src/views/apps/php/PhpView.vue:277
msgid "Clear Slow Log"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:177
msgid ""
"Stopping MySQL will cause websites using MySQL to become inaccessible. Are "
"you sure you want to stop?"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:186
#: src/views/apps/toolbox/IndexView.vue:185
#: src/views/apps/toolbox/IndexView.vue:187
msgid "Root Password"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:193
#, fuzzy
msgid "Save Changes"
msgstr "Save"

#: src/views/apps/mysql/IndexView.vue:201
msgid ""
"This modifies the MySQL main configuration file. If you do not understand "
"the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:230
msgid "Slow Query Log"
msgstr ""

#: src/views/apps/mysql/route.ts:17
msgid "Percona (MySQL)"
msgstr ""

#: src/views/apps/nginx/IndexView.vue:160
msgid ""
"Stopping OpenResty will cause all websites to become inaccessible. Are you "
"sure you want to stop?"
msgstr ""

#: src/views/apps/nginx/IndexView.vue:177
msgid ""
"This modifies the OpenResty main configuration file. If you do not "
"understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/nginx/IndexView.vue:206 src/views/apps/php/PhpView.vue:385
msgid "Error Logs"
msgstr ""

#: src/views/apps/nginx/route.ts:17
msgid "OpenResty (Nginx)"
msgstr ""

#: src/views/apps/php/PhpView.vue:52
msgid "Extension Name"
msgstr ""

#: src/views/apps/php/PhpView.vue:81
msgid "Are you sure you want to install %{ name }?"
msgstr ""

#: src/views/apps/php/PhpView.vue:107
msgid "Are you sure you want to uninstall %{ name }?"
msgstr ""

#: src/views/apps/php/PhpView.vue:156
#, fuzzy
msgid "Set successfully"
msgstr "Saved successfully"

#: src/views/apps/php/PhpView.vue:221 src/views/apps/php/PhpView.vue:227
msgid "Task submitted, please check progress in background tasks"
msgstr ""

#: src/views/apps/php/PhpView.vue:241
msgid "Set as CLI Default Version"
msgstr ""

#: src/views/apps/php/PhpView.vue:268
msgid "Clear Error Log"
msgstr ""

#: src/views/apps/php/PhpView.vue:306
msgid ""
"Stopping PHP %{ version } will cause websites using PHP %{ version } to "
"become inaccessible. Are you sure you want to stop?"
msgstr ""

#: src/views/apps/php/PhpView.vue:321
msgid "Extension Management"
msgstr ""

#: src/views/apps/php/PhpView.vue:322
msgid "Extension List"
msgstr ""

#: src/views/apps/php/PhpView.vue:334
#: src/views/apps/postgresql/IndexView.vue:185
#: src/views/apps/redis/IndexView.vue:147
#: src/views/apps/supervisor/IndexView.vue:439
msgid "Main Configuration"
msgstr ""

#: src/views/apps/php/PhpView.vue:337
msgid ""
"This modifies the PHP %{ version } main configuration file. If you do not "
"understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/php/PhpView.vue:353
msgid "FPM Configuration"
msgstr ""

#: src/views/apps/php/PhpView.vue:356
msgid ""
"This modifies the PHP %{ version } FPM configuration file. If you do not "
"understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/php/PhpView.vue:388
#: src/views/apps/postgresql/IndexView.vue:236
msgid "Slow Logs"
msgstr ""

#: src/views/apps/phpmyadmin/IndexView.vue:73
#, fuzzy
msgid "Access Information"
msgstr "Certificate"

#: src/views/apps/phpmyadmin/IndexView.vue:75
msgid "Access URL:"
msgstr ""

#: src/views/apps/phpmyadmin/IndexView.vue:78
#, fuzzy
msgid "Modify Port"
msgstr "Certificate"

#: src/views/apps/phpmyadmin/IndexView.vue:80
msgid "Modify phpMyAdmin access port"
msgstr ""

#: src/views/apps/phpmyadmin/IndexView.vue:87
msgid ""
"This modifies the OpenResty configuration file for phpMyAdmin. If you do not "
"understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/phpmyadmin/route.ts:17
msgid "phpMyAdmin"
msgstr ""

#: src/views/apps/podman/IndexView.vue:111
msgid ""
"Podman is a daemonless container management tool. Being in a stopped state "
"is normal and does not affect usage!"
msgstr ""

#: src/views/apps/podman/IndexView.vue:136
msgid "Are you sure you want to stop Podman?"
msgstr ""

#: src/views/apps/podman/IndexView.vue:147
msgid "Registry Configuration"
msgstr ""

#: src/views/apps/podman/IndexView.vue:150
msgid ""
"This modifies the Podman registry configuration file (/etc/containers/"
"registries.conf)"
msgstr ""

#: src/views/apps/podman/IndexView.vue:166
msgid "Storage Configuration"
msgstr ""

#: src/views/apps/podman/IndexView.vue:169
msgid ""
"This modifies the Podman storage configuration file (/etc/containers/"
"storage.conf)"
msgstr ""

#: src/views/apps/podman/route.ts:17
msgid "Podman"
msgstr ""

#: src/views/apps/postgresql/IndexView.vue:170
msgid ""
"Stopping PostgreSQL will cause websites using PostgreSQL to become "
"inaccessible. Are you sure you want to stop?"
msgstr ""

#: src/views/apps/postgresql/IndexView.vue:188
msgid ""
"This modifies the PostgreSQL main configuration file. If you do not "
"understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/postgresql/IndexView.vue:204
msgid "User Configuration"
msgstr ""

#: src/views/apps/postgresql/IndexView.vue:207
msgid ""
"This modifies the PostgreSQL user configuration file. If you do not "
"understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/postgresql/route.ts:17
msgid "PostgreSQL"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:41
#: src/views/apps/pureftpd/IndexView.vue:294
#: src/views/container/ImageView.vue:197
#: src/views/database/CreateDatabaseModal.vue:89
#: src/views/database/CreateServerModal.vue:94
#: src/views/database/CreateUserModal.vue:70
#: src/views/database/ServerList.vue:47
#: src/views/database/UpdateServerModal.vue:86
#: src/views/database/UserList.vue:40 src/views/login/IndexView.vue:113
#: src/views/setting/SettingBase.vue:65 src/views/ssh/CreateModal.vue:83
#: src/views/ssh/UpdateModal.vue:89
msgid "Username"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:75
#: src/views/apps/pureftpd/IndexView.vue:327
msgid "Change Password"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:86
msgid "Are you sure you want to delete user %{ username }?"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:146
#, fuzzy
msgid "Auto-start enabled successfully"
msgstr "Saved successfully"

#: src/views/apps/pureftpd/IndexView.vue:149
#, fuzzy
msgid "Auto-start disabled successfully"
msgstr "Saved successfully"

#: src/views/apps/pureftpd/IndexView.vue:218
#, fuzzy
msgid "Add User"
msgstr "Certificate"

#: src/views/apps/pureftpd/IndexView.vue:227
msgid "Auto-start On"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:228
msgid "Auto-start Off"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:247
msgid ""
"Stopping Pure-Ftpd will cause FTP service to be unavailable. Are you sure "
"you want to stop it?"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:256
msgid "Port Settings"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:258
msgid "Modify Pure-Ftpd listening port"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:262
msgid "User Management"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:263
msgid "User List"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:286
msgid "Run Log"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:291
#: src/views/apps/pureftpd/IndexView.vue:292
#: src/views/database/CreateDatabaseModal.vue:78
#: src/views/database/CreateUserModal.vue:54
#: src/views/database/IndexView.vue:36
msgid "Create User"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:299
#: src/views/container/ImageView.vue:202
#: src/views/database/CreateDatabaseModal.vue:94
#: src/views/database/CreateUserModal.vue:75
msgid "Enter username"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:302
#: src/views/apps/pureftpd/IndexView.vue:331
#: src/views/container/ImageView.vue:205
#: src/views/database/CreateDatabaseModal.vue:97
#: src/views/database/CreateServerModal.vue:102
#: src/views/database/CreateUserModal.vue:78
#: src/views/database/ServerList.vue:56
#: src/views/database/UpdateServerModal.vue:94
#: src/views/database/UpdateUserModal.vue:49 src/views/database/UserList.vue:50
#: src/views/login/IndexView.vue:121 src/views/setting/SettingBase.vue:68
#: src/views/ssh/CreateModal.vue:77 src/views/ssh/CreateModal.vue:86
#: src/views/ssh/UpdateModal.vue:83 src/views/ssh/UpdateModal.vue:92
msgid "Password"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:308
#: src/views/apps/pureftpd/IndexView.vue:336
msgid "It is recommended to use the generator to generate a random password"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:311
#: src/views/container/ComposeView.vue:38 src/views/website/IndexView.vue:44
#: src/views/website/IndexView.vue:504
msgid "Directory"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:316
msgid "Enter the directory authorized to the user"
msgstr ""

#: src/views/apps/pureftpd/route.ts:17
msgid "Pure-FTPd"
msgstr ""

#: src/views/apps/redis/IndexView.vue:136
msgid ""
"Stopping Redis will cause websites using Redis to become inaccessible. Are "
"you sure you want to stop?"
msgstr ""

#: src/views/apps/redis/IndexView.vue:150
msgid ""
"This modifies the Redis main configuration file. If you do not understand "
"the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/redis/route.ts:17
msgid "Redis"
msgstr ""

#: src/views/apps/rsync/route.ts:17
msgid "Rsync"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:25
msgid "Mount Path"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:47
msgid "Are you sure you want to delete mount %{ path }?"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:57
msgid "Unmount"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:104 src/views/apps/s3fs/IndexView.vue:130
#: src/views/apps/s3fs/IndexView.vue:131
msgid "Add Mount"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:107
msgid "Mount List"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:138
msgid "Enter Bucket name (COS format: xxxx-ID)"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:146
msgid "Enter AK key"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:154
msgid "Enter SK key"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:157
msgid "Region Endpoint"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:162
msgid ""
"Enter complete URL of region endpoint (e.g., https://oss-cn-"
"beijing.aliyuncs.com)"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:165
msgid "Mount Directory"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:170
msgid "Enter mount directory (e.g., /oss)"
msgstr ""

#: src/views/apps/s3fs/route.ts:17
msgid "S3FS"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:80
msgid "Uptime"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:103
#: src/views/container/ContainerView.vue:103
#: src/views/container/ContainerView.vue:439 src/views/task/CronView.vue:122
#: src/views/task/TaskView.vue:75
msgid "Logs"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:116
msgid "Configure"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:144
msgid "Are you sure you want to stop process %{ name }?"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:171
msgid "Are you sure you want to restart process %{ name }?"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:197
msgid "Are you sure you want to delete process %{ name }?"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:371
#: src/views/apps/supervisor/IndexView.vue:469
msgid "Add Process"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:404
msgid ""
"Stopping Supervisor will cause all processes managed by Supervisor to be "
"killed. Are you sure you want to stop?"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:415
msgid "Process Management"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:416
msgid "Process List"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:442
msgid ""
"This modifies the Supervisor main configuration file. If you do not "
"understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:461
msgid "Daemon Logs"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:482
msgid "Name cannot contain Chinese characters"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:485
msgid "Start Command"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:490
msgid "Please enter absolute path for files in start command"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:493
msgid "Working Directory"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:498
msgid "Please enter absolute path for working directory"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:501
msgid "Run As User"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:506
msgid "Usually www is sufficient"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:509
msgid "Number of Processes"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:519
msgid "Process Configuration"
msgstr ""

#: src/views/apps/supervisor/route.ts:17
msgid "Supervisor"
msgstr ""

#: src/views/apps/toolbox/IndexView.vue:87
#: src/views/database/ServerList.vue:148
#, fuzzy
msgid "Synchronized successfully"
msgstr "Saved successfully"

#: src/views/apps/toolbox/IndexView.vue:118 src/views/cert/AccountView.vue:102
#: src/views/cert/CertView.vue:290 src/views/cert/DnsView.vue:86
#: src/views/database/ServerList.vue:185 src/views/database/UserList.vue:170
#: src/views/file/PermissionModal.vue:123
msgid "Modify"
msgstr ""

#: src/views/apps/toolbox/IndexView.vue:124
msgid "DNS modifications will revert to default after system restart."
msgstr ""

#: src/views/apps/toolbox/IndexView.vue:138
msgid "Total %{ total }, used %{ used }, free %{ free }"
msgstr ""

#: src/views/apps/toolbox/IndexView.vue:141
#: src/views/dashboard/IndexView.vue:578
msgid "SWAP Size"
msgstr ""

#: src/views/apps/toolbox/IndexView.vue:148
#: src/views/database/CreateDatabaseModal.vue:106
#: src/views/database/CreateServerModal.vue:73
#: src/views/database/ServerList.vue:87
#: src/views/database/UpdateServerModal.vue:65
#: src/views/database/UserList.vue:81 src/views/ssh/CreateModal.vue:62
#: src/views/ssh/UpdateModal.vue:68
msgid "Host"
msgstr ""

#: src/views/apps/toolbox/IndexView.vue:151
msgid "Hostname"
msgstr ""

#: src/views/apps/toolbox/IndexView.vue:169
msgid "Time"
msgstr ""

#: src/views/apps/toolbox/IndexView.vue:171
msgid ""
"After manually changing the time, it may still be overwritten by system "
"automatic time synchronization."
msgstr ""

#: src/views/apps/toolbox/IndexView.vue:173
#, fuzzy
msgid "Select Timezone"
msgstr "Certificate"

#: src/views/apps/toolbox/IndexView.vue:174
msgid "Please select a timezone"
msgstr ""

#: src/views/apps/toolbox/IndexView.vue:176
#, fuzzy
msgid "Modify Time"
msgstr "Certificate"

#: src/views/apps/toolbox/IndexView.vue:179
msgid "NTP Time Synchronization"
msgstr ""

#: src/views/apps/toolbox/IndexView.vue:180
#, fuzzy
msgid "Synchronize Time"
msgstr "Saved successfully"

#: src/views/apps/toolbox/route.ts:17
msgid "Toolbox"
msgstr ""

#: src/views/backup/ListView.vue:36
msgid "Filename"
msgstr ""

#: src/views/backup/ListView.vue:43 src/views/container/ImageView.vue:53
#: src/views/file/ListTable.vue:165 src/views/file/SearchModal.vue:28
msgid "Size"
msgstr ""

#: src/views/backup/ListView.vue:49 src/views/database/ServerList.vue:126
#: src/views/database/UserList.vue:143
msgid "Update Date"
msgstr ""

#: src/views/backup/ListView.vue:77
msgid "Restore"
msgstr ""

#: src/views/backup/ListView.vue:88
msgid "Are you sure you want to delete this backup?"
msgstr ""

#: src/views/backup/ListView.vue:126 src/views/cert/CreateAccountModal.vue:49
#: src/views/cert/CreateCertModal.vue:50 src/views/cert/CreateDnsModal.vue:35
#: src/views/cert/UploadCertModal.vue:21
#: src/views/container/ComposeView.vue:234
#: src/views/container/ContainerCreate.vue:106
#: src/views/container/NetworkView.vue:175
#: src/views/container/VolumeView.vue:129
#: src/views/database/CreateDatabaseModal.vue:28
#: src/views/database/CreateUserModal.vue:28 src/views/file/ToolBar.vue:45
#: src/views/firewall/CreateForwardModal.vue:41
#: src/views/firewall/CreateModal.vue:84 src/views/ssh/CreateModal.vue:38
#: src/views/task/CreateModal.vue:51 src/views/website/IndexView.vue:309
#, fuzzy
msgid "Created successfully"
msgstr "Saved successfully"

#: src/views/backup/ListView.vue:132
msgid "Restoring..."
msgstr ""

#: src/views/backup/ListView.vue:139
#, fuzzy
msgid "Restored successfully"
msgstr "Saved successfully"

#: src/views/backup/ListView.vue:182 src/views/backup/ListView.vue:209
msgid "Create Backup"
msgstr ""

#: src/views/backup/ListView.vue:183 src/views/backup/UploadModal.vue:39
msgid "Upload Backup"
msgstr ""

#: src/views/backup/ListView.vue:218 src/views/backup/ListView.vue:251
msgid "Select website"
msgstr ""

#: src/views/backup/ListView.vue:220
#: src/views/database/CreateDatabaseModal.vue:70
#: src/views/database/DatabaseList.vue:35 src/views/task/CreateModal.vue:146
#: src/views/task/CreateModal.vue:148 src/views/website/IndexView.vue:468
#: src/views/website/IndexView.vue:473
msgid "Database Name"
msgstr ""

#: src/views/backup/ListView.vue:225
#: src/views/database/CreateDatabaseModal.vue:75
#: src/views/database/CreateUserModal.vue:104
#: src/views/database/UpdateUserModal.vue:59
msgid "Enter database name"
msgstr ""

#: src/views/backup/ListView.vue:228 src/views/task/CreateModal.vue:150
#: src/views/task/CreateModal.vue:151
msgid "Save Directory"
msgstr ""

#: src/views/backup/ListView.vue:233
msgid "Leave empty to use default path"
msgstr ""

#: src/views/backup/ListView.vue:242
msgid "Restore Backup"
msgstr ""

#: src/views/backup/ListView.vue:253 src/views/dashboard/IndexView.vue:422
#: src/views/database/IndexView.vue:45 src/views/database/route.ts:19
#: src/views/website/IndexView.vue:447
msgid "Database"
msgstr ""

#: src/views/backup/route.ts:19
msgid "Backup"
msgstr ""

#: src/views/backup/UploadModal.vue:20
msgid "Upload %{ filename } successfully"
msgstr ""

#: src/views/backup/UploadModal.vue:51 src/views/file/UploadModal.vue:50
msgid "Click or drag files to this area to upload"
msgstr ""

#: src/views/backup/UploadModal.vue:53
msgid ""
"For large files, it is recommended to use SFTP or other methods to upload"
msgstr ""

#: src/views/cert/AccountView.vue:44 src/views/cert/AccountView.vue:233
#: src/views/cert/CreateAccountModal.vue:89
msgid "Email"
msgstr ""

#: src/views/cert/AccountView.vue:72 src/views/cert/AccountView.vue:225
#: src/views/cert/CertView.vue:456 src/views/cert/CreateAccountModal.vue:81
#: src/views/cert/CreateCertModal.vue:78
msgid "Key Type"
msgstr ""

#: src/views/cert/AccountView.vue:110 src/views/cert/CertView.vue:299
#: src/views/cert/DnsView.vue:95
#, fuzzy
msgid "Deletion successful"
msgstr "Saved successfully"

#: src/views/cert/AccountView.vue:117
msgid "Are you sure you want to delete the account?"
msgstr ""

#: src/views/cert/AccountView.vue:150 src/views/cert/CreateAccountModal.vue:38
msgid "Registering account with CA, please wait patiently"
msgstr ""

#: src/views/cert/AccountView.vue:160 src/views/cert/CertView.vue:350
#: src/views/cert/CertView.vue:367 src/views/cert/DnsView.vue:140
#: src/views/container/ComposeView.vue:252
#, fuzzy
msgid "Update successful"
msgstr "Saved successfully"

#: src/views/cert/AccountView.vue:205
msgid "Modify Account"
msgstr ""

#: src/views/cert/AccountView.vue:212 src/views/cert/CreateAccountModal.vue:68
msgid ""
"Google and SSL.com require obtaining KID and HMAC from their official "
"websites first"
msgstr ""

#: src/views/cert/AccountView.vue:214
msgid ""
"Google is not accessible in mainland China, other CAs depend on network "
"conditions, recommend using GoogleCN or Let's Encrypt"
msgstr ""

#: src/views/cert/AccountView.vue:217 src/views/cert/CreateAccountModal.vue:73
msgid "CA"
msgstr ""

#: src/views/cert/AccountView.vue:220 src/views/cert/CreateAccountModal.vue:76
msgid "Select CA"
msgstr ""

#: src/views/cert/AccountView.vue:228 src/views/cert/CertView.vue:459
#: src/views/cert/CreateAccountModal.vue:84
#: src/views/cert/CreateCertModal.vue:81
msgid "Select key type"
msgstr ""

#: src/views/cert/AccountView.vue:238 src/views/cert/CreateAccountModal.vue:94
msgid "Enter email address"
msgstr ""

#: src/views/cert/AccountView.vue:246 src/views/cert/CreateAccountModal.vue:102
msgid "Enter KID"
msgstr ""

#: src/views/cert/AccountView.vue:254 src/views/cert/CreateAccountModal.vue:110
msgid "Enter HMAC"
msgstr ""

#: src/views/cert/CertView.vue:64 src/views/cert/CertView.vue:448
#: src/views/cert/CreateCertModal.vue:70 src/views/cert/ObtainModal.vue:53
#: src/views/website/EditView.vue:228 src/views/website/IndexView.vue:412
msgid "Domain"
msgstr ""

#: src/views/cert/CertView.vue:70 src/views/cert/CertView.vue:124
#: src/views/cert/CertView.vue:135 src/views/cert/CertView.vue:154
#: src/views/container/ContainerCreate.vue:55
#: src/views/database/ServerList.vue:52 src/views/database/ServerList.vue:67
#: src/views/database/UserList.vue:46 src/views/database/UserList.vue:86
#: src/views/firewall/ForwardView.vue:26 src/views/firewall/IpRuleView.vue:26
#: src/views/firewall/IpRuleView.vue:43 src/views/firewall/RuleView.vue:26
#: src/views/firewall/RuleView.vue:43
msgid "None"
msgstr ""

#: src/views/cert/CertView.vue:109 src/views/file/ToolBar.vue:221
#: src/views/file/UploadModal.vue:38
msgid "Upload"
msgstr ""

#: src/views/cert/CertView.vue:117
msgid "Associated Account"
msgstr ""

#: src/views/cert/CertView.vue:130 src/views/website/EditView.vue:297
msgid "Issuer"
msgstr ""

#: src/views/cert/CertView.vue:139
msgid "Expiration Time"
msgstr ""

#: src/views/cert/CertView.vue:167
msgid "Auto Renew"
msgstr ""

#: src/views/cert/CertView.vue:202 src/views/cert/ObtainModal.vue:74
msgid "Issue"
msgstr ""

#: src/views/cert/CertView.vue:221
msgid "Deploy"
msgstr ""

#: src/views/cert/CertView.vue:233 src/views/cert/ObtainModal.vue:24
#: src/views/cert/ObtainModal.vue:77 src/views/website/EditView.vue:133
msgid "Please wait..."
msgstr ""

#: src/views/cert/CertView.vue:239
#, fuzzy
msgid "Renewal successful"
msgstr "Saved successfully"

#: src/views/cert/CertView.vue:247
msgid "Renew"
msgstr ""

#: src/views/cert/CertView.vue:305
msgid "Are you sure you want to delete the certificate?"
msgstr ""

#: src/views/cert/CertView.vue:391
#, fuzzy
msgid "Deployment successful"
msgstr "Saved successfully"

#: src/views/cert/CertView.vue:437
#, fuzzy
msgid "Modify Certificate"
msgstr "Certificate"

#: src/views/cert/CertView.vue:445
msgid ""
"You can automatically issue and deploy certificates by selecting any website/"
"DNS, or manually enter domain names and set DNS resolution to issue "
"certificates, or fill in deployment scripts to automatically deploy "
"certificates."
msgstr ""

#: src/views/cert/CertView.vue:467 src/views/cert/CreateCertModal.vue:89
msgid "Select website for certificate deployment"
msgstr ""

#: src/views/cert/CertView.vue:472 src/views/cert/CreateCertModal.vue:94
msgid "Account"
msgstr ""

#: src/views/cert/CertView.vue:475 src/views/cert/CreateCertModal.vue:97
msgid "Select account for certificate issuance"
msgstr ""

#: src/views/cert/CertView.vue:480 src/views/cert/CreateCertModal.vue:102
#: src/views/cert/CreateDnsModal.vue:55 src/views/cert/DnsView.vue:193
msgid "DNS"
msgstr ""

#: src/views/cert/CertView.vue:483 src/views/cert/CreateCertModal.vue:105
msgid "Select DNS for certificate issuance"
msgstr ""

#: src/views/cert/CertView.vue:488 src/views/cert/CertView.vue:551
#: src/views/cert/route.ts:19 src/views/cert/UploadCertModal.vue:38
#: src/views/setting/SettingHttps.vue:39 src/views/website/EditView.vue:342
msgid "Certificate"
msgstr "Certificate"

#: src/views/cert/CertView.vue:492 src/views/cert/UploadCertModal.vue:42
#: src/views/website/EditView.vue:346
msgid "Enter the content of the PEM certificate file"
msgstr ""

#: src/views/cert/CertView.vue:496 src/views/cert/CertView.vue:563
#: src/views/cert/UploadCertModal.vue:46 src/views/setting/SettingHttps.vue:46
#: src/views/ssh/CreateModal.vue:78 src/views/ssh/CreateModal.vue:89
#: src/views/ssh/UpdateModal.vue:84 src/views/ssh/UpdateModal.vue:95
#: src/views/website/EditView.vue:350
msgid "Private Key"
msgstr "Private Key"

#: src/views/cert/CertView.vue:500 src/views/cert/UploadCertModal.vue:50
#: src/views/website/EditView.vue:354
msgid "Enter the content of the KEY private key file"
msgstr ""

#: src/views/cert/CertView.vue:504
msgid "Deployment Script"
msgstr ""

#: src/views/cert/CertView.vue:508
msgid ""
"The {cert} and {key} in the script will be replaced with the certificate and "
"private key content"
msgstr ""

#: src/views/cert/CertView.vue:519
#, fuzzy
msgid "Deploy Certificate"
msgstr "Certificate"

#: src/views/cert/CertView.vue:530
msgid "Select websites to deploy the certificate"
msgstr ""

#: src/views/cert/CertView.vue:543
#, fuzzy
msgid "View Certificate"
msgstr "Certificate"

#: src/views/cert/CreateAccountModal.vue:61 src/views/cert/IndexView.vue:106
msgid "Create Account"
msgstr ""

#: src/views/cert/CreateAccountModal.vue:70
msgid ""
"Google is not accessible in mainland China, and other CAs depend on network "
"conditions. GoogleCN or Let's Encrypt are recommended"
msgstr ""

#: src/views/cert/CreateCertModal.vue:59 src/views/cert/IndexView.vue:102
#, fuzzy
msgid "Create Certificate"
msgstr "Certificate"

#: src/views/cert/CreateCertModal.vue:67
msgid ""
"You can automatically issue and deploy certificates by selecting either "
"Website or DNS, or you can manually enter domain names and set up DNS "
"resolution to issue certificates"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:44 src/views/cert/IndexView.vue:110
msgid "Create DNS"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:52
msgid "Comment Name"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:53
msgid "Enter comment name"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:58 src/views/cert/DnsView.vue:196
msgid "Select DNS"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:64 src/views/cert/DnsView.vue:205
msgid "Enter Aliyun Access Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:67 src/views/cert/DnsView.vue:212
msgid "Enter Aliyun Secret Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:70 src/views/cert/DnsView.vue:219
msgid "Enter Tencent Cloud SecretId"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:73 src/views/cert/DnsView.vue:226
msgid "Enter Tencent Cloud SecretKey"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:76 src/views/cert/DnsView.vue:233
msgid "Enter Huawei Cloud AccessKeyId"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:82 src/views/cert/DnsView.vue:240
msgid "Enter Huawei Cloud SecretAccessKey"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:86
msgid "Enter Western Digital Username"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:92
msgid "Enter Western Digital API Password"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:99 src/views/cert/DnsView.vue:261
msgid "Enter Cloudflare API Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:103 src/views/cert/DnsView.vue:268
msgid "Enter GoDaddy Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:106 src/views/cert/DnsView.vue:275
msgid "Enter G-Core API Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:109 src/views/cert/DnsView.vue:282
msgid "Enter Porkbun API Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:115 src/views/cert/DnsView.vue:289
msgid "Enter Porkbun Secret Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:122 src/views/cert/DnsView.vue:296
msgid "Enter Namecheap API Username"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:126 src/views/cert/DnsView.vue:303
msgid "Enter Namecheap API Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:132 src/views/cert/DnsView.vue:310
msgid "Enter NameSilo API Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:136 src/views/cert/DnsView.vue:317
msgid "Enter Name.com Username"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:139 src/views/cert/DnsView.vue:324
msgid "Enter Name.com Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:146
msgid "Enter ClouDNS Auth ID (use Sub Auth ID by adding sub-prefix)"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:153 src/views/cert/DnsView.vue:338
msgid "Enter ClouDNS Auth Password"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:157 src/views/cert/DnsView.vue:345
msgid "Enter Duck DNS Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:163 src/views/cert/DnsView.vue:352
msgid "Enter Hetzner Auth API Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:167 src/views/cert/DnsView.vue:359
msgid "Enter Linode Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:170 src/views/cert/DnsView.vue:366
msgid "Enter Vercel Token"
msgstr ""

#: src/views/cert/DnsView.vue:31 src/views/cert/DnsView.vue:190
msgid "Note Name"
msgstr ""

#: src/views/cert/DnsView.vue:56 src/views/firewall/IpRuleView.vue:77
#: src/views/firewall/IpRuleView.vue:102 src/views/firewall/RuleView.vue:111
#: src/views/firewall/RuleView.vue:136
msgid "Unknown"
msgstr ""

#: src/views/cert/DnsView.vue:101
msgid "Are you sure you want to delete the DNS?"
msgstr ""

#: src/views/cert/DnsView.vue:182
msgid "Modify DNS"
msgstr ""

#: src/views/cert/DnsView.vue:191
msgid "Enter note name"
msgstr ""

#: src/views/cert/DnsView.vue:247
msgid "Enter West.cn Username"
msgstr ""

#: src/views/cert/DnsView.vue:254
msgid "Enter West.cn API Password"
msgstr ""

#: src/views/cert/DnsView.vue:331
msgid "Enter ClouDNS Auth ID (Add sub- prefix for Sub Auth ID)"
msgstr ""

#: src/views/cert/IndexView.vue:98 src/views/cert/UploadCertModal.vue:30
#, fuzzy
msgid "Upload Certificate"
msgstr "Certificate"

#: src/views/cert/IndexView.vue:115
#, fuzzy
msgid "Certificate List"
msgstr "Certificate"

#: src/views/cert/IndexView.vue:118
msgid "Account List"
msgstr ""

#: src/views/cert/IndexView.vue:121
msgid "DNS List"
msgstr ""

#: src/views/cert/ObtainModal.vue:18
msgid "Automatic"
msgstr ""

#: src/views/cert/ObtainModal.vue:19
msgid "Manual"
msgstr ""

#: src/views/cert/ObtainModal.vue:20
msgid "Self-signed"
msgstr ""

#: src/views/cert/ObtainModal.vue:33 src/views/cert/ObtainModal.vue:85
#: src/views/cert/ObtainModal.vue:103
#, fuzzy
msgid "Issuance successful"
msgstr "Saved successfully"

#: src/views/cert/ObtainModal.vue:41
msgid ""
"Please set up DNS resolution for the domain first, then continue with the "
"issuance"
msgstr ""

#: src/views/cert/ObtainModal.vue:44
msgid "DNS Records to Set"
msgstr ""

#: src/views/cert/ObtainModal.vue:55
msgid "Host Record"
msgstr ""

#: src/views/cert/ObtainModal.vue:56
msgid "Record Value"
msgstr ""

#: src/views/cert/ObtainModal.vue:116
#, fuzzy
msgid "Issue Certificate"
msgstr "Certificate"

#: src/views/cert/ObtainModal.vue:123
msgid "Issuance Mode"
msgstr ""

#: src/views/container/ComposeView.vue:65 src/views/container/ImageView.vue:60
#: src/views/container/NetworkView.vue:100
#: src/views/container/VolumeView.vue:54 src/views/task/CronView.vue:84
#: src/views/task/TaskView.vue:37
msgid "Creation Time"
msgstr ""

#: src/views/container/ComposeView.vue:97 src/views/file/ListTable.vue:64
#: src/views/file/ListTable.vue:216 src/views/ssh/IndexView.vue:71
#: src/views/task/CronView.vue:135 src/views/website/IndexView.vue:111
msgid "Edit"
msgstr ""

#: src/views/container/ComposeView.vue:105
msgid "Starting..."
msgstr ""

#: src/views/container/ComposeView.vue:112
#: src/views/container/ContainerView.vue:238
#: src/views/container/ContainerView.vue:302
#, fuzzy
msgid "Start successful"
msgstr "Saved successfully"

#: src/views/container/ComposeView.vue:128
msgid "Are you sure you want to start compose %{ name }?"
msgstr ""

#: src/views/container/ComposeView.vue:135
msgid "Force pull images"
msgstr ""

#: src/views/container/ComposeView.vue:162
#: src/views/container/ContainerView.vue:245
#: src/views/container/ContainerView.vue:316
#, fuzzy
msgid "Stop successful"
msgstr "Saved successfully"

#: src/views/container/ComposeView.vue:168
msgid "Are you sure you want to stop compose %{ name }?"
msgstr ""

#: src/views/container/ComposeView.vue:191
#: src/views/container/ContainerView.vue:280
#: src/views/container/ContainerView.vue:358
#: src/views/container/ImageView.vue:119
#: src/views/container/NetworkView.vue:159
#: src/views/container/VolumeView.vue:113
#, fuzzy
msgid "Delete successful"
msgstr "Saved successfully"

#: src/views/container/ComposeView.vue:197
msgid "Are you sure you want to delete compose %{ name }?"
msgstr ""

#: src/views/container/ComposeView.vue:273
#: src/views/container/ComposeView.vue:299
msgid "Create Compose"
msgstr ""

#: src/views/container/ComposeView.vue:306
msgid "Compose Name"
msgstr ""

#: src/views/container/ComposeView.vue:309
#: src/views/container/ComposeView.vue:339 src/views/container/IndexView.vue:24
msgid "Compose"
msgstr ""

#: src/views/container/ComposeView.vue:320
#: src/views/container/ComposeView.vue:350
#: src/views/container/ContainerCreate.vue:341
msgid "Variable Name"
msgstr ""

#: src/views/container/ComposeView.vue:321
#: src/views/container/ComposeView.vue:351
#: src/views/container/ContainerCreate.vue:342
msgid "Variable Value"
msgstr ""

#: src/views/container/ComposeView.vue:332
msgid "Edit Compose"
msgstr ""

#: src/views/container/ContainerCreate.vue:56
msgid "Always"
msgstr ""

#: src/views/container/ContainerCreate.vue:57
msgid "On failure (default 5 retries)"
msgstr ""

#: src/views/container/ContainerCreate.vue:58
msgid "Unless stopped"
msgstr ""

#: src/views/container/ContainerCreate.vue:127
#: src/views/container/ContainerView.vue:402
#, fuzzy
msgid "Create Container"
msgstr "Certificate"

#: src/views/container/ContainerCreate.vue:137
#: src/views/container/ContainerView.vue:25
msgid "Container Name"
msgstr ""

#: src/views/container/ContainerCreate.vue:140
#: src/views/container/ContainerView.vue:52
#: src/views/container/ImageView.vue:36
msgid "Image"
msgstr ""

#: src/views/container/ContainerCreate.vue:143
msgid "Ports"
msgstr ""

#: src/views/container/ContainerCreate.vue:149
msgid "Map Ports"
msgstr ""

#: src/views/container/ContainerCreate.vue:156
msgid "Expose All"
msgstr ""

#: src/views/container/ContainerCreate.vue:159
msgid "Port Mapping"
msgstr ""

#: src/views/container/ContainerCreate.vue:165
msgid "Host (Start)"
msgstr ""

#: src/views/container/ContainerCreate.vue:166
msgid "Host (End)"
msgstr ""

#: src/views/container/ContainerCreate.vue:167
msgid "Container (Start)"
msgstr ""

#: src/views/container/ContainerCreate.vue:168
msgid "Container (End)"
msgstr ""

#: src/views/container/ContainerCreate.vue:169
msgid "Protocol"
msgstr ""

#: src/views/container/ContainerCreate.vue:180
msgid "Optional"
msgstr ""

#: src/views/container/ContainerCreate.vue:233
#: src/views/container/ContainerCreate.vue:280
msgid "Add"
msgstr ""

#: src/views/container/ContainerCreate.vue:236
#: src/views/dashboard/IndexView.vue:794 src/views/monitor/IndexView.vue:286
msgid "Network"
msgstr ""

#: src/views/container/ContainerCreate.vue:239
msgid "Mount"
msgstr ""

#: src/views/container/ContainerCreate.vue:244
msgid "Host Directory"
msgstr ""

#: src/views/container/ContainerCreate.vue:245
msgid "Container Directory"
msgstr ""

#: src/views/container/ContainerCreate.vue:246 src/views/file/ListTable.vue:69
#: src/views/file/ListTable.vue:141 src/views/file/ListTable.vue:296
#: src/views/file/PermissionModal.vue:113 src/views/file/ToolBar.vue:235
msgid "Permission"
msgstr ""

#: src/views/container/ContainerCreate.vue:265
msgid "Read-Write"
msgstr ""

#: src/views/container/ContainerCreate.vue:273
msgid "Read-Only"
msgstr ""

#: src/views/container/ContainerCreate.vue:283
#: src/views/container/ContainerCreate.vue:284
msgid "Command"
msgstr ""

#: src/views/container/ContainerCreate.vue:286
#: src/views/container/ContainerCreate.vue:287
msgid "Entrypoint"
msgstr ""

#: src/views/container/ContainerCreate.vue:301
msgid "CPU Shares"
msgstr ""

#: src/views/container/ContainerCreate.vue:308
msgid "TTY (-t)"
msgstr ""

#: src/views/container/ContainerCreate.vue:313
msgid "STDIN (-i)"
msgstr ""

#: src/views/container/ContainerCreate.vue:318
msgid "Auto Remove"
msgstr ""

#: src/views/container/ContainerCreate.vue:323
msgid "Privileged Mode"
msgstr ""

#: src/views/container/ContainerCreate.vue:328
msgid "Restart Policy"
msgstr ""

#: src/views/container/ContainerCreate.vue:331
#: src/views/container/ContainerCreate.vue:334
msgid "Select restart policy"
msgstr ""

#: src/views/container/ContainerCreate.vue:345
#: src/views/container/NetworkView.vue:292
#: src/views/container/VolumeView.vue:192
msgid "Labels"
msgstr ""

#: src/views/container/ContainerCreate.vue:349
#: src/views/container/NetworkView.vue:296
#: src/views/container/VolumeView.vue:196
msgid "Label Name"
msgstr ""

#: src/views/container/ContainerCreate.vue:350
#: src/views/container/NetworkView.vue:297
#: src/views/container/VolumeView.vue:197
msgid "Label Value"
msgstr ""

#: src/views/container/ContainerView.vue:63
msgid "Ports (Host->Container)"
msgstr ""

#: src/views/container/ContainerView.vue:119
#: src/views/container/ContainerView.vue:462 src/views/file/ListTable.vue:80
#: src/views/file/ListTable.vue:260
msgid "Rename"
msgstr ""

#: src/views/container/ContainerView.vue:142
#: src/views/container/ContainerView.vue:408
msgid "Force Stop"
msgstr ""

#: src/views/container/ContainerView.vue:147
#: src/views/container/ContainerView.vue:409
msgid "Pause"
msgstr ""

#: src/views/container/ContainerView.vue:152
#: src/views/container/ContainerView.vue:410
msgid "Resume"
msgstr ""

#: src/views/container/ContainerView.vue:197 src/views/file/ListTable.vue:348
msgid "More"
msgstr ""

#: src/views/container/ContainerView.vue:230
#, fuzzy
msgid "Rename successful"
msgstr "Saved successfully"

#: src/views/container/ContainerView.vue:252
#: src/views/container/ContainerView.vue:330
#, fuzzy
msgid "Restart successful"
msgstr "Saved successfully"

#: src/views/container/ContainerView.vue:259
#: src/views/container/ContainerView.vue:344
#, fuzzy
msgid "Force stop successful"
msgstr "Saved successfully"

#: src/views/container/ContainerView.vue:266
#: src/views/container/ContainerView.vue:372
#, fuzzy
msgid "Pause successful"
msgstr "Saved successfully"

#: src/views/container/ContainerView.vue:273
#: src/views/container/ContainerView.vue:386
#, fuzzy
msgid "Resume successful"
msgstr "Saved successfully"

#: src/views/container/ContainerView.vue:287
#: src/views/container/ImageView.vue:126
#: src/views/container/NetworkView.vue:166
#: src/views/container/VolumeView.vue:120
#, fuzzy
msgid "Cleanup successful"
msgstr "Saved successfully"

#: src/views/container/ContainerView.vue:293
msgid "Please select containers to start"
msgstr ""

#: src/views/container/ContainerView.vue:307
msgid "Please select containers to stop"
msgstr ""

#: src/views/container/ContainerView.vue:321
msgid "Please select containers to restart"
msgstr ""

#: src/views/container/ContainerView.vue:335
msgid "Please select containers to force stop"
msgstr ""

#: src/views/container/ContainerView.vue:349
msgid "Please select containers to delete"
msgstr ""

#: src/views/container/ContainerView.vue:363
msgid "Please select containers to pause"
msgstr ""

#: src/views/container/ContainerView.vue:377
msgid "Please select containers to resume"
msgstr ""

#: src/views/container/ContainerView.vue:403
msgid "Cleanup Containers"
msgstr ""

#: src/views/container/ContainerView.vue:469 src/views/file/ListTable.vue:699
msgid "New Name"
msgstr ""

#: src/views/container/ContainerView.vue:474
msgid "Enter new name"
msgstr ""

#: src/views/container/ImageView.vue:29
msgid "Container Count"
msgstr ""

#: src/views/container/ImageView.vue:85 src/views/container/NetworkView.vue:125
#: src/views/container/VolumeView.vue:79 src/views/firewall/ForwardView.vue:94
#: src/views/firewall/IpRuleView.vue:136 src/views/firewall/RuleView.vue:173
#: src/views/task/TaskView.vue:88
msgid "Are you sure you want to delete?"
msgstr ""

#: src/views/container/ImageView.vue:135
#, fuzzy
msgid "Pull successful"
msgstr "Saved successfully"

#: src/views/container/ImageView.vue:151 src/views/container/ImageView.vue:179
msgid "Pull Image"
msgstr ""

#: src/views/container/ImageView.vue:152
msgid "Cleanup Images"
msgstr ""

#: src/views/container/ImageView.vue:186
msgid "Image Name"
msgstr ""

#: src/views/container/ImageView.vue:191
msgid "docker.io/php:8.3-fpm"
msgstr ""

#: src/views/container/ImageView.vue:194
msgid "Authentication"
msgstr ""

#: src/views/container/ImageView.vue:211
#: src/views/database/CreateDatabaseModal.vue:103
#: src/views/database/CreateUserModal.vue:84
#: src/views/database/UpdateUserModal.vue:55
msgid "Enter password"
msgstr ""

#: src/views/container/IndexView.vue:21
msgid "Containers"
msgstr ""

#: src/views/container/IndexView.vue:27
msgid "Images"
msgstr ""

#: src/views/container/IndexView.vue:30
msgid "Networks"
msgstr ""

#: src/views/container/IndexView.vue:33
msgid "Volumes"
msgstr ""

#: src/views/container/NetworkView.vue:52
#: src/views/container/NetworkView.vue:229
#: src/views/container/VolumeView.vue:33 src/views/container/VolumeView.vue:183
msgid "Driver"
msgstr ""

#: src/views/container/NetworkView.vue:59 src/views/container/VolumeView.vue:40
msgid "Scope"
msgstr ""

#: src/views/container/NetworkView.vue:66
#: src/views/container/NetworkView.vue:241
#: src/views/container/NetworkView.vue:268
msgid "Subnet"
msgstr ""

#: src/views/container/NetworkView.vue:83
#: src/views/container/NetworkView.vue:249
#: src/views/container/NetworkView.vue:276
msgid "Gateway"
msgstr ""

#: src/views/container/NetworkView.vue:191
#: src/views/container/NetworkView.vue:219
msgid "Create Network"
msgstr ""

#: src/views/container/NetworkView.vue:192
msgid "Cleanup Networks"
msgstr ""

#: src/views/container/NetworkView.vue:226
msgid "Network Name"
msgstr ""

#: src/views/container/NetworkView.vue:246
#: src/views/container/NetworkView.vue:262
msgid "***********/24"
msgstr ""

#: src/views/container/NetworkView.vue:254
msgid "*************"
msgstr ""

#: src/views/container/NetworkView.vue:257
#: src/views/container/NetworkView.vue:284
msgid "IP Range"
msgstr ""

#: src/views/container/NetworkView.vue:273
msgid "2408:400e::/48"
msgstr ""

#: src/views/container/NetworkView.vue:281
msgid "2408:400e::1"
msgstr ""

#: src/views/container/NetworkView.vue:289
msgid "2408:400e::/64"
msgstr ""

#: src/views/container/NetworkView.vue:300
#: src/views/container/VolumeView.vue:200
msgid "Options"
msgstr ""

#: src/views/container/NetworkView.vue:304
#: src/views/container/VolumeView.vue:204
msgid "Option Name"
msgstr ""

#: src/views/container/NetworkView.vue:305
#: src/views/container/VolumeView.vue:205
msgid "Option Value"
msgstr ""

#: src/views/container/route.ts:19
msgid "Container"
msgstr ""

#: src/views/container/VolumeView.vue:47 src/views/dashboard/IndexView.vue:641
msgid "Mount Point"
msgstr ""

#: src/views/container/VolumeView.vue:145
#: src/views/container/VolumeView.vue:173
msgid "Create Volume"
msgstr ""

#: src/views/container/VolumeView.vue:146
msgid "Cleanup Volumes"
msgstr ""

#: src/views/container/VolumeView.vue:180
msgid "Volume Name"
msgstr ""

#: src/views/dashboard/IndexView.vue:129
msgid "Running blocked"
msgstr ""

#: src/views/dashboard/IndexView.vue:131
msgid "Running slowly"
msgstr ""

#: src/views/dashboard/IndexView.vue:135
msgid "Running smoothly"
msgstr ""

#: src/views/dashboard/IndexView.vue:321
msgid "Panel restarting..."
msgstr ""

#: src/views/dashboard/IndexView.vue:323
#, fuzzy
msgid "Panel restarted successfully"
msgstr "Saved successfully"

#: src/views/dashboard/IndexView.vue:335
msgid "Current version is the latest"
msgstr ""

#: src/views/dashboard/IndexView.vue:431 src/views/task/IndexView.vue:29
msgid "Scheduled Tasks"
msgstr ""

#: src/views/dashboard/IndexView.vue:436
msgid "Rat Panel"
msgstr ""

#: src/views/dashboard/IndexView.vue:440
msgid "Sponsor Support"
msgstr ""

#: src/views/dashboard/IndexView.vue:446
msgid "Are you sure you want to restart the panel?"
msgstr ""

#: src/views/dashboard/IndexView.vue:454
msgid "Resource Overview"
msgstr ""

#: src/views/dashboard/IndexView.vue:471
msgid "Last 1 minute"
msgstr ""

#: src/views/dashboard/IndexView.vue:478
msgid "Last 5 minutes"
msgstr ""

#: src/views/dashboard/IndexView.vue:485
msgid "Last 15 minutes"
msgstr ""

#: src/views/dashboard/IndexView.vue:503 src/views/dashboard/IndexView.vue:514
msgid "cores"
msgstr ""

#: src/views/dashboard/IndexView.vue:508
msgid "Model"
msgstr ""

#: src/views/dashboard/IndexView.vue:512
msgid "Parameters"
msgstr ""

#: src/views/dashboard/IndexView.vue:515
msgid "cache"
msgstr ""

#: src/views/dashboard/IndexView.vue:521 src/views/monitor/IndexView.vue:179
msgid "Usage"
msgstr ""

#: src/views/dashboard/IndexView.vue:522
msgid "Frequency"
msgstr ""

#: src/views/dashboard/IndexView.vue:542
msgid "Active"
msgstr ""

#: src/views/dashboard/IndexView.vue:548
msgid "Inactive"
msgstr ""

#: src/views/dashboard/IndexView.vue:554
msgid "Free"
msgstr ""

#: src/views/dashboard/IndexView.vue:560
msgid "Shared"
msgstr ""

#: src/views/dashboard/IndexView.vue:566
msgid "Committed"
msgstr ""

#: src/views/dashboard/IndexView.vue:572
msgid "Commit Limit"
msgstr ""

#: src/views/dashboard/IndexView.vue:584
msgid "SWAP Used"
msgstr ""

#: src/views/dashboard/IndexView.vue:590
msgid "SWAP Available"
msgstr ""

#: src/views/dashboard/IndexView.vue:596
msgid "Physical Memory Size"
msgstr ""

#: src/views/dashboard/IndexView.vue:602
msgid "Physical Memory Used"
msgstr ""

#: src/views/dashboard/IndexView.vue:608
msgid "Physical Memory Available"
msgstr ""

#: src/views/dashboard/IndexView.vue:645
msgid "File System"
msgstr ""

#: src/views/dashboard/IndexView.vue:649
msgid "Inodes Usage"
msgstr ""

#: src/views/dashboard/IndexView.vue:653
msgid "Inodes Total"
msgstr ""

#: src/views/dashboard/IndexView.vue:657
msgid "Inodes Used"
msgstr ""

#: src/views/dashboard/IndexView.vue:661
msgid "Inodes Available"
msgstr ""

#: src/views/dashboard/IndexView.vue:678
msgid "Quick Apps"
msgstr ""

#: src/views/dashboard/IndexView.vue:721
msgid "You have not set any apps to display here!"
msgstr ""

#: src/views/dashboard/IndexView.vue:725
msgid "Environment Information"
msgstr ""

#: src/views/dashboard/IndexView.vue:728
msgid "System Hostname"
msgstr ""

#: src/views/dashboard/IndexView.vue:730 src/views/dashboard/IndexView.vue:738
#: src/views/dashboard/IndexView.vue:745 src/views/dashboard/IndexView.vue:751
#: src/views/dashboard/IndexView.vue:762 src/views/dashboard/IndexView.vue:774
msgid "Loading..."
msgstr ""

#: src/views/dashboard/IndexView.vue:734
msgid "System Version"
msgstr ""

#: src/views/dashboard/IndexView.vue:743
msgid "System Kernel Version"
msgstr ""

#: src/views/dashboard/IndexView.vue:749
msgid "System Uptime"
msgstr ""

#: src/views/dashboard/IndexView.vue:755
msgid "Panel Internal Version"
msgstr ""

#: src/views/dashboard/IndexView.vue:767
msgid "Panel Compile Information"
msgstr ""

#: src/views/dashboard/IndexView.vue:784
msgid "Real-time Monitoring"
msgstr ""

#: src/views/dashboard/IndexView.vue:798
msgid "Unit"
msgstr ""

#: src/views/dashboard/IndexView.vue:806
msgid "Network Card"
msgstr ""

#: src/views/dashboard/IndexView.vue:826
msgid "Total Sent"
msgstr ""

#: src/views/dashboard/IndexView.vue:828
msgid "Total Received"
msgstr ""

#: src/views/dashboard/IndexView.vue:831
msgid "Real-time Sent"
msgstr ""

#: src/views/dashboard/IndexView.vue:835
msgid "Real-time Received"
msgstr ""

#: src/views/dashboard/IndexView.vue:839 src/views/file/PermissionModal.vue:88
#: src/views/file/PermissionModal.vue:97 src/views/file/PermissionModal.vue:106
msgid "Read"
msgstr ""

#: src/views/dashboard/IndexView.vue:840 src/views/file/PermissionModal.vue:89
#: src/views/file/PermissionModal.vue:98 src/views/file/PermissionModal.vue:107
msgid "Write"
msgstr ""

#: src/views/dashboard/IndexView.vue:842
msgid "Real-time Read/Write"
msgstr ""

#: src/views/dashboard/IndexView.vue:845
msgid "Read/Write Latency"
msgstr ""

#: src/views/dashboard/route.ts:20
msgid "Dashboard"
msgstr ""

#: src/views/dashboard/UpdateView.vue:24
msgid "Update Panel"
msgstr ""

#: src/views/dashboard/UpdateView.vue:25
msgid "Are you sure you want to update the panel?"
msgstr ""

#: src/views/dashboard/UpdateView.vue:26 src/views/monitor/IndexView.vue:469
msgid "Confirm"
msgstr ""

#: src/views/dashboard/UpdateView.vue:27 src/views/file/ListTable.vue:399
#: src/views/file/ListTable.vue:494 src/views/file/ToolBar.vue:139
#: src/views/file/ToolBar.vue:226
msgid "Cancel"
msgstr ""

#: src/views/dashboard/UpdateView.vue:29
msgid "Panel updating..."
msgstr ""

#: src/views/dashboard/UpdateView.vue:40
#, fuzzy
msgid "Panel updated successfully"
msgstr "Saved successfully"

#: src/views/dashboard/UpdateView.vue:47
msgid "Update canceled"
msgstr ""

#: src/views/dashboard/UpdateView.vue:59
msgid "Update Now"
msgstr ""

#: src/views/dashboard/UpdateView.vue:82
msgid "Loading update information, please wait a moment"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:20
#: src/views/database/CreateUserModal.vue:20
msgid "Local (localhost)"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:21
#: src/views/database/CreateUserModal.vue:21
msgid "All (%)"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:22
#: src/views/database/CreateUserModal.vue:22
msgid "Specific"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:54
#: src/views/database/IndexView.vue:32
msgid "Create Database"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:62
#: src/views/database/CreateUserModal.vue:62
#: src/views/database/DatabaseList.vue:42 src/views/database/IndexView.vue:51
#: src/views/database/UserList.vue:91
msgid "Server"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:66
#: src/views/database/CreateUserModal.vue:66
msgid "Select server"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:81
msgid "Authorized User"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:86
msgid "Enter authorized username (leave empty for no authorization)"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:110
#: src/views/database/CreateUserModal.vue:91
msgid "Select host"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:117
#: src/views/database/CreateUserModal.vue:95
msgid "Specific Host"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:123
#: src/views/database/CreateUserModal.vue:100
msgid "Enter supported host address"
msgstr ""

#: src/views/database/CreateServerModal.vue:47
#: src/views/database/IndexView.vue:40
msgid "Add Server"
msgstr ""

#: src/views/database/CreateServerModal.vue:60
#: src/views/database/UpdateServerModal.vue:60
msgid "Enter database server name"
msgstr ""

#: src/views/database/CreateServerModal.vue:67
msgid "Select database type"
msgstr ""

#: src/views/database/CreateServerModal.vue:78
#: src/views/database/UpdateServerModal.vue:70
msgid "Enter database server host"
msgstr ""

#: src/views/database/CreateServerModal.vue:84
#: src/views/database/UpdateServerModal.vue:76
#: src/views/firewall/ForwardView.vue:32 src/views/firewall/RuleView.vue:49
#: src/views/setting/SettingBase.vue:74 src/views/ssh/CreateModal.vue:68
#: src/views/ssh/UpdateModal.vue:74 src/views/website/IndexView.vue:423
msgid "Port"
msgstr ""

#: src/views/database/CreateServerModal.vue:89
#: src/views/database/UpdateServerModal.vue:81
msgid "Enter database server port"
msgstr ""

#: src/views/database/CreateServerModal.vue:99
#: src/views/database/UpdateServerModal.vue:91
msgid "Enter database server username"
msgstr ""

#: src/views/database/CreateServerModal.vue:108
#: src/views/database/UpdateServerModal.vue:100
msgid "Enter database server password"
msgstr ""

#: src/views/database/CreateServerModal.vue:111
#: src/views/database/CreateUserModal.vue:106
#: src/views/database/DatabaseList.vue:57 src/views/database/ServerList.vue:97
#: src/views/database/UpdateServerModal.vue:103
#: src/views/database/UpdateUserModal.vue:61
#: src/views/database/UserList.vue:114
msgid "Comment"
msgstr ""

#: src/views/database/CreateServerModal.vue:116
#: src/views/database/UpdateServerModal.vue:108
msgid "Enter database server comment"
msgstr ""

#: src/views/database/CreateUserModal.vue:87
msgid "Host (MySQL only)"
msgstr ""

#: src/views/database/CreateUserModal.vue:103
#: src/views/database/UpdateUserModal.vue:58 src/views/database/UserList.vue:99
msgid "Privileges"
msgstr ""

#: src/views/database/CreateUserModal.vue:111
#: src/views/database/UpdateUserModal.vue:66
msgid "Enter database user comment"
msgstr ""

#: src/views/database/DatabaseList.vue:47
msgid "Encoding"
msgstr ""

#: src/views/database/DatabaseList.vue:88
msgid "Are you sure you want to delete this database?"
msgstr ""

#: src/views/database/IndexView.vue:48 src/views/task/SystemView.vue:37
msgid "User"
msgstr ""

#: src/views/database/ServerList.vue:76 src/views/database/UserList.vue:70
#: src/views/file/ListTable.vue:500 src/views/file/ListTable.vue:520
#: src/views/file/SearchModal.vue:65 src/views/file/ToolBar.vue:145
#: src/views/file/ToolBar.vue:165
#, fuzzy
msgid "Copied successfully"
msgstr "Saved successfully"

#: src/views/database/ServerList.vue:80 src/views/database/UserList.vue:74
#: src/views/file/ListTable.vue:67 src/views/file/ListTable.vue:294
#: src/views/file/ToolBar.vue:232
msgid "Copy"
msgstr ""

#: src/views/database/ServerList.vue:121 src/views/database/UserList.vue:138
msgid "Valid"
msgstr ""

#: src/views/database/ServerList.vue:121 src/views/database/UserList.vue:138
msgid "Invalid"
msgstr ""

#: src/views/database/ServerList.vue:154
msgid ""
"Are you sure you want to synchronize database users (excluding password) to "
"the panel?"
msgstr ""

#: src/views/database/ServerList.vue:166
msgid "Sync"
msgstr ""

#: src/views/database/ServerList.vue:196
msgid ""
"Built-in servers cannot be deleted. If you need to delete them, please "
"uninstall the corresponding app"
msgstr ""

#: src/views/database/ServerList.vue:207
msgid "Are you sure you want to delete the server?"
msgstr ""

#: src/views/database/UpdateServerModal.vue:47
#, fuzzy
msgid "Modify Server"
msgstr "Certificate"

#: src/views/database/UpdateUserModal.vue:41
#, fuzzy
msgid "Modify User"
msgstr "Certificate"

#: src/views/database/UserList.vue:61
msgid "Not saved"
msgstr ""

#: src/views/database/UserList.vue:181
msgid "Are you sure you want to delete the user?"
msgstr ""

#: src/views/error-page/NotFound.vue:10
msgid "Sorry, the page you visited does not exist."
msgstr ""

#: src/views/error-page/NotFound.vue:15
msgid "Back to Home"
msgstr ""

#: src/views/file/CompressModal.vue:32
msgid "Compressing..."
msgstr ""

#: src/views/file/CompressModal.vue:40
#, fuzzy
msgid "Compressed successfully"
msgstr "Saved successfully"

#: src/views/file/CompressModal.vue:64 src/views/file/CompressModal.vue:96
#: src/views/file/ListTable.vue:71 src/views/file/ListTable.vue:241
#: src/views/file/ListTable.vue:297 src/views/file/ToolBar.vue:234
msgid "Compress"
msgstr ""

#: src/views/file/CompressModal.vue:72
msgid "Files to compress"
msgstr ""

#: src/views/file/CompressModal.vue:75
msgid "Compress to"
msgstr ""

#: src/views/file/CompressModal.vue:78
msgid "Format"
msgstr ""

#: src/views/file/EditModal.vue:22
msgid "Edit - %{ file }"
msgstr ""

#: src/views/file/EditModal.vue:30
msgid "Refresh"
msgstr ""

#: src/views/file/ListTable.vue:64 src/views/file/ListTable.vue:218
msgid "Open"
msgstr ""

#: src/views/file/ListTable.vue:64 src/views/file/ListTable.vue:216
msgid "Preview"
msgstr ""

#: src/views/file/ListTable.vue:68 src/views/file/ListTable.vue:295
#: src/views/file/ToolBar.vue:233
msgid "Move"
msgstr ""

#: src/views/file/ListTable.vue:71 src/views/file/ListTable.vue:243
msgid "Download"
msgstr ""

#: src/views/file/ListTable.vue:75 src/views/file/ListTable.vue:298
#: src/views/file/ListTable.vue:721
msgid "Uncompress"
msgstr ""

#: src/views/file/ListTable.vue:85 src/views/file/ToolBar.vue:229
msgid "Paste"
msgstr ""

#: src/views/file/ListTable.vue:153
msgid "Owner / Group"
msgstr ""

#: src/views/file/ListTable.vue:173 src/views/file/SearchModal.vue:36
msgid "Modification Time"
msgstr ""

#: src/views/file/ListTable.vue:275 src/views/file/SearchModal.vue:88
msgid "Are you sure you want to delete %{ name }?"
msgstr ""

#: src/views/file/ListTable.vue:311 src/views/file/ListTable.vue:322
#: src/views/file/ListTable.vue:558 src/views/file/ListTable.vue:569
#: src/views/file/ToolBar.vue:77 src/views/file/ToolBar.vue:94
msgid "Marked successfully, please navigate to the destination path to paste"
msgstr ""

#: src/views/file/ListTable.vue:389 src/views/file/ToolBar.vue:37
#: src/views/file/ToolBar.vue:51
msgid "Invalid name"
msgstr ""

#: src/views/file/ListTable.vue:396 src/views/file/ListTable.vue:483
#: src/views/file/ToolBar.vue:128
msgid "Warning"
msgstr ""

#: src/views/file/ListTable.vue:397
msgid "There are items with the same name. Do you want to overwrite?"
msgstr ""

#: src/views/file/ListTable.vue:398 src/views/file/ListTable.vue:493
#: src/views/file/ToolBar.vue:138
msgid "Overwrite"
msgstr ""

#: src/views/file/ListTable.vue:405 src/views/file/ListTable.vue:421
msgid "Renamed %{ source } to %{ target } successfully"
msgstr ""

#: src/views/file/ListTable.vue:440 src/views/file/PathInput.vue:33
msgid "Invalid path"
msgstr ""

#: src/views/file/ListTable.vue:443
msgid "Uncompressing..."
msgstr ""

#: src/views/file/ListTable.vue:450
#, fuzzy
msgid "Uncompressed successfully"
msgstr "Saved successfully"

#: src/views/file/ListTable.vue:459 src/views/file/ToolBar.vue:104
msgid "Please mark the files/folders to copy or move first"
msgstr ""

#: src/views/file/ListTable.vue:484 src/views/file/ToolBar.vue:129
msgid ""
"There are items with the same name. %{ items } Do you want to overwrite?"
msgstr ""

#: src/views/file/ListTable.vue:506 src/views/file/ListTable.vue:526
#: src/views/file/ToolBar.vue:151 src/views/file/ToolBar.vue:171
#, fuzzy
msgid "Moved successfully"
msgstr "Saved successfully"

#: src/views/file/ListTable.vue:512 src/views/file/ToolBar.vue:157
msgid "Canceled"
msgstr ""

#: src/views/file/ListTable.vue:691
msgid "Rename - %{ source }"
msgstr ""

#: src/views/file/ListTable.vue:709
msgid "Uncompress - %{ file }"
msgstr ""

#: src/views/file/ListTable.vue:717
msgid "Uncompress to"
msgstr ""

#: src/views/file/PathInput.vue:132
msgid "Root Directory"
msgstr ""

#: src/views/file/PathInput.vue:152
msgid "Enter search content"
msgstr ""

#: src/views/file/PathInput.vue:154
msgid "Include subdirectories"
msgstr ""

#: src/views/file/PermissionModal.vue:65
msgid "Batch modify permissions"
msgstr ""

#: src/views/file/PermissionModal.vue:66
msgid "Modify permissions - %{ path }"
msgstr ""

#: src/views/file/PermissionModal.vue:86 src/views/file/PermissionModal.vue:116
msgid "Owner"
msgstr ""

#: src/views/file/PermissionModal.vue:90 src/views/file/PermissionModal.vue:99
#: src/views/file/PermissionModal.vue:108
msgid "Execute"
msgstr ""

#: src/views/file/PermissionModal.vue:95 src/views/file/PermissionModal.vue:119
msgid "Group"
msgstr ""

#: src/views/file/PermissionModal.vue:104
msgid "Others"
msgstr ""

#: src/views/file/PreviewModal.vue:31
msgid "Preview - "
msgstr ""

#: src/views/file/route.ts:19
msgid "Files"
msgstr ""

#: src/views/file/SearchModal.vue:71
msgid "Copy Path"
msgstr ""

#: src/views/file/SearchModal.vue:154
msgid "%{ keyword } - Search Results"
msgstr ""

#: src/views/file/ToolBar.vue:60
#, fuzzy
msgid "Download task created successfully"
msgstr "Saved successfully"

#: src/views/file/ToolBar.vue:66
msgid "Please select files/folders to copy"
msgstr ""

#: src/views/file/ToolBar.vue:83
msgid "Please select files/folders to move"
msgstr ""

#: src/views/file/ToolBar.vue:180
msgid "Please select files/folders to delete"
msgstr ""

#: src/views/file/ToolBar.vue:214
msgid "File"
msgstr ""

#: src/views/file/ToolBar.vue:215
msgid "Folder"
msgstr ""

#: src/views/file/ToolBar.vue:219 src/views/file/ToolBar.vue:249
msgid "New"
msgstr ""

#: src/views/file/ToolBar.vue:222 src/views/file/ToolBar.vue:267
msgid "Remote Download"
msgstr ""

#: src/views/file/ToolBar.vue:240
msgid "Are you sure you want to delete in bulk?"
msgstr ""

#: src/views/file/ToolBar.vue:275
msgid "Download URL"
msgstr ""

#: src/views/file/ToolBar.vue:278
#, fuzzy
msgid "Save as"
msgstr "Save"

#: src/views/file/UploadModal.vue:20
#, fuzzy
msgid "Upload %{ fileName } successful"
msgstr "Saved successfully"

#: src/views/file/UploadModal.vue:51
msgid ""
"For large files, it is recommended to use SFTP and other methods to upload"
msgstr ""

#: src/views/firewall/CreateForwardModal.vue:50
#: src/views/firewall/ForwardView.vue:167
#, fuzzy
msgid "Create Forwarding"
msgstr "Certificate"

#: src/views/firewall/CreateForwardModal.vue:58
#: src/views/firewall/CreateIpModal.vue:97
#: src/views/firewall/CreateModal.vue:101 src/views/firewall/ForwardView.vue:15
#: src/views/firewall/IpRuleView.vue:15 src/views/firewall/RuleView.vue:15
msgid "Transport Protocol"
msgstr ""

#: src/views/firewall/CreateForwardModal.vue:61
#: src/views/firewall/ForwardView.vue:44
msgid "Target IP"
msgstr ""

#: src/views/firewall/CreateForwardModal.vue:66
msgid "Source Port"
msgstr ""

#: src/views/firewall/CreateForwardModal.vue:76
#: src/views/firewall/ForwardView.vue:62
msgid "Target Port"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:38
#: src/views/firewall/CreateModal.vue:38 src/views/firewall/IpRuleView.vue:69
#: src/views/firewall/RuleView.vue:103
msgid "Accept"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:42
#: src/views/firewall/CreateModal.vue:42 src/views/firewall/IpRuleView.vue:71
#: src/views/firewall/RuleView.vue:105
msgid "Drop"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:46
#: src/views/firewall/CreateModal.vue:46 src/views/firewall/IpRuleView.vue:73
#: src/views/firewall/RuleView.vue:107
msgid "Reject"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:53
#: src/views/firewall/CreateModal.vue:53 src/views/firewall/IpRuleView.vue:98
#: src/views/firewall/RuleView.vue:132
msgid "Inbound"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:57
#: src/views/firewall/CreateModal.vue:57 src/views/firewall/IpRuleView.vue:100
#: src/views/firewall/RuleView.vue:134
msgid "Outbound"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:78
#, fuzzy
msgid "%{ address } created successfully"
msgstr "Saved successfully"

#: src/views/firewall/CreateIpModal.vue:89
#: src/views/firewall/CreateModal.vue:93 src/views/firewall/IpRuleView.vue:209
#: src/views/firewall/RuleView.vue:246
#, fuzzy
msgid "Create Rule"
msgstr "Certificate"

#: src/views/firewall/CreateIpModal.vue:100
#: src/views/firewall/CreateModal.vue:104 src/views/firewall/IpRuleView.vue:32
#: src/views/firewall/RuleView.vue:32
msgid "Network Protocol"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:103
msgid "IP Address"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:108
#: src/views/firewall/CreateModal.vue:132
msgid ""
"Optional IP or IP range: 127.0.0.1 or **********/24 (multiple separated by "
"commas)"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:114
#: src/views/firewall/CreateModal.vue:135 src/views/firewall/IpRuleView.vue:49
#: src/views/firewall/RuleView.vue:83
msgid "Strategy"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:117
#: src/views/firewall/CreateModal.vue:138 src/views/firewall/IpRuleView.vue:85
#: src/views/firewall/RuleView.vue:119
msgid "Direction"
msgstr ""

#: src/views/firewall/CreateModal.vue:109
msgid "Start Port"
msgstr ""

#: src/views/firewall/CreateModal.vue:119
msgid "End Port"
msgstr ""

#: src/views/firewall/CreateModal.vue:129 src/views/firewall/IpRuleView.vue:110
#: src/views/firewall/RuleView.vue:144
msgid "Target"
msgstr ""

#: src/views/firewall/ForwardView.vue:138 src/views/firewall/IpRuleView.vue:180
#: src/views/firewall/RuleView.vue:217
msgid "Please select rules to delete"
msgstr ""

#: src/views/firewall/ForwardView.vue:173 src/views/firewall/IpRuleView.vue:215
#: src/views/firewall/RuleView.vue:252 src/views/website/IndexView.vue:352
msgid "Batch Delete"
msgstr ""

#: src/views/firewall/ForwardView.vue:176 src/views/firewall/IpRuleView.vue:218
#: src/views/firewall/RuleView.vue:255
msgid "Are you sure you want to batch delete?"
msgstr ""

#: src/views/firewall/IndexView.vue:19
msgid "Port Rules"
msgstr ""

#: src/views/firewall/IndexView.vue:22
msgid "IP Rules"
msgstr ""

#: src/views/firewall/IndexView.vue:25
msgid "Port Forwarding"
msgstr ""

#: src/views/firewall/IndexView.vue:28
msgid "Settings"
msgstr ""

#: src/views/firewall/IpRuleView.vue:75 src/views/firewall/RuleView.vue:109
msgid "Mark"
msgstr ""

#: src/views/firewall/route.ts:19
msgid "Firewall"
msgstr ""

#: src/views/firewall/RuleView.vue:74
msgid "In Use"
msgstr ""

#: src/views/firewall/RuleView.vue:76
msgid "Not Used"
msgstr ""

#: src/views/firewall/RuleView.vue:151
msgid "All"
msgstr ""

#: src/views/firewall/SettingView.vue:27 src/views/firewall/SettingView.vue:33
#: src/views/firewall/SettingView.vue:39
#, fuzzy
msgid "Settings saved successfully"
msgstr "Saved successfully"

#: src/views/firewall/SettingView.vue:46
msgid "System Firewall"
msgstr ""

#: src/views/firewall/SettingView.vue:49
msgid "SSH Switch"
msgstr ""

#: src/views/firewall/SettingView.vue:52
msgid "Allow Ping"
msgstr ""

#: src/views/firewall/SettingView.vue:55
msgid "SSH Port"
msgstr ""

#: src/views/login/IndexView.vue:46
msgid "Please enter username and password"
msgstr ""

#: src/views/login/IndexView.vue:50
msgid ""
"Failed to get encryption public key, please refresh the page and try again"
msgstr ""

#: src/views/login/IndexView.vue:61
#, fuzzy
msgid "Login successful!"
msgstr "Saved successfully"

#: src/views/login/IndexView.vue:130
msgid "Safe Login"
msgstr ""

#: src/views/login/IndexView.vue:131
msgid "Remember Me"
msgstr ""

#: src/views/login/IndexView.vue:145
msgid "Login"
msgstr ""

#: src/views/monitor/IndexView.vue:60
msgid "Load"
msgstr ""

#: src/views/monitor/IndexView.vue:71 src/views/monitor/IndexView.vue:87
msgid "1 minute"
msgstr ""

#: src/views/monitor/IndexView.vue:71 src/views/monitor/IndexView.vue:102
msgid "5 minutes"
msgstr ""

#: src/views/monitor/IndexView.vue:71 src/views/monitor/IndexView.vue:124
msgid "15 minutes"
msgstr ""

#: src/views/monitor/IndexView.vue:93 src/views/monitor/IndexView.vue:115
#: src/views/monitor/IndexView.vue:137 src/views/monitor/IndexView.vue:192
#: src/views/monitor/IndexView.vue:251 src/views/monitor/IndexView.vue:273
#: src/views/monitor/IndexView.vue:330 src/views/monitor/IndexView.vue:352
#: src/views/monitor/IndexView.vue:374 src/views/monitor/IndexView.vue:396
msgid "Maximum"
msgstr ""

#: src/views/monitor/IndexView.vue:94 src/views/monitor/IndexView.vue:116
#: src/views/monitor/IndexView.vue:138 src/views/monitor/IndexView.vue:193
#: src/views/monitor/IndexView.vue:252 src/views/monitor/IndexView.vue:274
#: src/views/monitor/IndexView.vue:331 src/views/monitor/IndexView.vue:353
#: src/views/monitor/IndexView.vue:375 src/views/monitor/IndexView.vue:397
msgid "Minimum"
msgstr ""

#: src/views/monitor/IndexView.vue:98 src/views/monitor/IndexView.vue:120
#: src/views/monitor/IndexView.vue:142 src/views/monitor/IndexView.vue:197
#: src/views/monitor/IndexView.vue:256 src/views/monitor/IndexView.vue:278
#: src/views/monitor/IndexView.vue:335 src/views/monitor/IndexView.vue:357
#: src/views/monitor/IndexView.vue:379 src/views/monitor/IndexView.vue:401
msgid "Average"
msgstr ""

#: src/views/monitor/IndexView.vue:162
msgid "Unit %"
msgstr ""

#: src/views/monitor/IndexView.vue:221 src/views/monitor/IndexView.vue:302
msgid "Unit MB"
msgstr ""

#: src/views/monitor/IndexView.vue:297 src/views/monitor/IndexView.vue:317
msgid "Total Out"
msgstr ""

#: src/views/monitor/IndexView.vue:297 src/views/monitor/IndexView.vue:339
msgid "Total In"
msgstr ""

#: src/views/monitor/IndexView.vue:297 src/views/monitor/IndexView.vue:361
msgid "Per Second Out"
msgstr ""

#: src/views/monitor/IndexView.vue:297 src/views/monitor/IndexView.vue:383
msgid "Per Second In"
msgstr ""

#: src/views/monitor/IndexView.vue:409 src/views/monitor/IndexView.vue:415
#, fuzzy
msgid "Operation successful"
msgstr "Saved successfully"

#: src/views/monitor/IndexView.vue:446
msgid "Clear Monitoring Records"
msgstr ""

#: src/views/monitor/IndexView.vue:449 src/views/website/EditView.vue:220
msgid "Are you sure you want to clear?"
msgstr ""

#: src/views/monitor/IndexView.vue:460
msgid "Enable Monitoring"
msgstr ""

#: src/views/monitor/IndexView.vue:463
#, fuzzy
msgid "Save Days"
msgstr "Save"

#: src/views/monitor/IndexView.vue:465
msgid "days"
msgstr ""

#: src/views/monitor/IndexView.vue:471
msgid "Time Selection"
msgstr ""

#: src/views/monitor/route.ts:19
msgid "Monitoring"
msgstr ""

#: src/views/setting/IndexView.vue:17
msgid "Basic"
msgstr ""

#: src/views/setting/route.ts:19
msgid "Panel Settings"
msgstr ""

#: src/views/setting/SettingBase.vue:53
msgid ""
"Modifying panel port/entrance requires corresponding changes in the browser "
"address bar to access the panel!"
msgstr ""

#: src/views/setting/SettingBase.vue:59 src/views/setting/SettingBase.vue:60
msgid "Panel Name"
msgstr ""

#: src/views/setting/SettingBase.vue:62
msgid "Language"
msgstr ""

#: src/views/setting/SettingBase.vue:66 src/views/setting/SettingBase.vue:69
#: src/views/setting/SettingBase.vue:78
msgid "admin"
msgstr ""

#: src/views/setting/SettingBase.vue:71
#, fuzzy
msgid "Certificate Default Email"
msgstr "Certificate"

#: src/views/setting/SettingBase.vue:72
msgid "<EMAIL>"
msgstr ""

#: src/views/setting/SettingBase.vue:75
msgid "8888"
msgstr ""

#: src/views/setting/SettingBase.vue:77
msgid "Security Entrance"
msgstr ""

#: src/views/setting/SettingBase.vue:80
msgid "Offline Mode"
msgstr ""

#: src/views/setting/SettingBase.vue:83
msgid "Auto Update"
msgstr ""

#: src/views/setting/SettingBase.vue:86
msgid "Default Website Directory"
msgstr ""

#: src/views/setting/SettingBase.vue:87
msgid "/www/wwwroot"
msgstr ""

#: src/views/setting/SettingBase.vue:89
msgid "Default Backup Directory"
msgstr ""

#: src/views/setting/SettingBase.vue:90
msgid "/www/backup"
msgstr ""

#: src/views/setting/SettingHttps.vue:34
msgid ""
"Incorrect certificates may cause the panel to be inaccessible. Please "
"proceed with caution!"
msgstr ""

#: src/views/setting/SettingHttps.vue:36
msgid "Panel HTTPS"
msgstr "Panel HTTPS"

#: src/views/ssh/CreateModal.vue:50 src/views/ssh/IndexView.vue:231
#, fuzzy
msgid "Create Host"
msgstr "Certificate"

#: src/views/ssh/CreateModal.vue:73 src/views/ssh/UpdateModal.vue:79
msgid "Authentication Method"
msgstr ""

#: src/views/ssh/CreateModal.vue:92 src/views/ssh/UpdateModal.vue:98
msgid "Remarks"
msgstr ""

#: src/views/ssh/IndexView.vue:43
msgid "Please create a host first"
msgstr ""

#: src/views/ssh/IndexView.vue:82
msgid "Are you sure you want to delete this host?"
msgstr ""

#: src/views/ssh/IndexView.vue:162
msgid "Connection closed. Please refresh."
msgstr ""

#: src/views/ssh/IndexView.vue:167
msgid "Connection error. Please refresh."
msgstr ""

#: src/views/ssh/route.ts:19
msgid "Terminal"
msgstr ""

#: src/views/ssh/UpdateModal.vue:30
#, fuzzy
msgid "Updated successfully"
msgstr "Saved successfully"

#: src/views/ssh/UpdateModal.vue:56
msgid "Update Host"
msgstr ""

#: src/views/task/CreateModal.vue:22
msgid "# Enter your script content here"
msgstr ""

#: src/views/task/CreateModal.vue:89
#, fuzzy
msgid "Create Scheduled Task"
msgstr "Certificate"

#: src/views/task/CreateModal.vue:96 src/views/task/CronView.vue:36
msgid "Task Type"
msgstr ""

#: src/views/task/CreateModal.vue:100 src/views/task/CronView.vue:49
msgid "Run Script"
msgstr ""

#: src/views/task/CreateModal.vue:101 src/views/task/CronView.vue:51
msgid "Backup Data"
msgstr ""

#: src/views/task/CreateModal.vue:102 src/views/task/CronView.vue:52
msgid "Log Rotation"
msgstr ""

#: src/views/task/CreateModal.vue:107 src/views/task/CreateModal.vue:108
#: src/views/task/CronView.vue:29 src/views/task/CronView.vue:259
#: src/views/task/CronView.vue:260 src/views/task/TaskView.vue:15
msgid "Task Name"
msgstr ""

#: src/views/task/CreateModal.vue:110 src/views/task/CronView.vue:74
#: src/views/task/CronView.vue:262
msgid "Task Schedule"
msgstr ""

#: src/views/task/CreateModal.vue:114
msgid "Script Content"
msgstr ""

#: src/views/task/CreateModal.vue:128
msgid "Backup Type"
msgstr ""

#: src/views/task/CreateModal.vue:131
msgid "MySQL Database"
msgstr ""

#: src/views/task/CreateModal.vue:132
msgid "PostgreSQL Database"
msgstr ""

#: src/views/task/CreateModal.vue:153
msgid "Retention Count"
msgstr ""

#: src/views/task/CronView.vue:59
msgid "Enabled"
msgstr ""

#: src/views/task/CronView.vue:94
msgid "Last Update Time"
msgstr ""

#: src/views/task/CronView.vue:146
msgid "Are you sure you want to delete this task?"
msgstr ""

#: src/views/task/CronView.vue:251
msgid "Edit Task"
msgstr ""

#: src/views/task/IndexView.vue:25
#, fuzzy
msgid "Create Task"
msgstr "Certificate"

#: src/views/task/IndexView.vue:32
msgid "System Processes"
msgstr ""

#: src/views/task/IndexView.vue:35
#, fuzzy
msgid "Panel Tasks"
msgstr "Panel HTTPS"

#: src/views/task/route.ts:19
msgid "Background Tasks"
msgstr ""

#: src/views/task/SystemView.vue:25
msgid "Parent PID"
msgstr ""

#: src/views/task/SystemView.vue:31
msgid "Threads"
msgstr ""

#: src/views/task/SystemView.vue:50 src/views/task/TaskView.vue:33
#: src/views/website/IndexView.vue:30
msgid "Running"
msgstr ""

#: src/views/task/SystemView.vue:52
msgid "Sleeping"
msgstr ""

#: src/views/task/SystemView.vue:56
msgid "Idle"
msgstr ""

#: src/views/task/SystemView.vue:58
msgid "Zombie"
msgstr ""

#: src/views/task/SystemView.vue:60 src/views/task/TaskView.vue:30
msgid "Waiting"
msgstr ""

#: src/views/task/SystemView.vue:62
msgid "Locked"
msgstr ""

#: src/views/task/SystemView.vue:87
msgid "Start Time"
msgstr ""

#: src/views/task/SystemView.vue:108
msgid "Process %{ pid } has been terminated"
msgstr ""

#: src/views/task/SystemView.vue:114
msgid "Are you sure you want to terminate process %{ pid }?"
msgstr ""

#: src/views/task/SystemView.vue:124
msgid "Terminate"
msgstr ""

#: src/views/task/TaskView.vue:28
msgid "Completed"
msgstr ""

#: src/views/task/TaskView.vue:32
msgid "Failed"
msgstr ""

#: src/views/task/TaskView.vue:46
msgid "Completion Time"
msgstr ""

#: src/views/task/TaskView.vue:136
msgid "If logs cannot be loaded, please disable ad blockers!"
msgstr ""

#: src/views/website/EditView.vue:54 src/views/website/IndexView.vue:210
msgid "Not used"
msgstr ""

#: src/views/website/EditView.vue:83
#, fuzzy
msgid "Edit Website - %{ name }"
msgstr "Certificate"

#: src/views/website/EditView.vue:85 src/views/website/route.ts:31
#, fuzzy
msgid "Edit Website"
msgstr "Certificate"

#: src/views/website/EditView.vue:122
#, fuzzy
msgid "Reset successfully"
msgstr "Saved successfully"

#: src/views/website/EditView.vue:139
#, fuzzy
msgid "Issued successfully"
msgstr "Saved successfully"

#: src/views/website/EditView.vue:153
msgid "The selected certificate is invalid"
msgstr ""

#: src/views/website/EditView.vue:178
msgid ""
"If you modify the original text, other modifications will not take effect "
"after clicking save!"
msgstr ""

#: src/views/website/EditView.vue:184
msgid "Reset Configuration"
msgstr ""

#: src/views/website/EditView.vue:187
msgid "Are you sure you want to reset the configuration?"
msgstr ""

#: src/views/website/EditView.vue:196
#: src/views/website/ProxyBuilderModal.vue:121
msgid "Generate Reverse Proxy Configuration"
msgstr ""

#: src/views/website/EditView.vue:207
msgid "One-click Certificate Issuance"
msgstr ""

#: src/views/website/EditView.vue:217
msgid "Clear Logs"
msgstr ""

#: src/views/website/EditView.vue:226
msgid "Domain & Listening"
msgstr ""

#: src/views/website/EditView.vue:236
msgid "Listening Address"
msgstr ""

#: src/views/website/EditView.vue:254
msgid "Basic Settings"
msgstr ""

#: src/views/website/EditView.vue:256
msgid "Website Directory"
msgstr ""

#: src/views/website/EditView.vue:257
msgid "Enter website directory (absolute path)"
msgstr ""

#: src/views/website/EditView.vue:259
msgid "Running Directory"
msgstr ""

#: src/views/website/EditView.vue:262
msgid "Enter running directory (needed for Laravel etc.) (absolute path)"
msgstr ""

#: src/views/website/EditView.vue:265
msgid "Default Document"
msgstr ""

#: src/views/website/EditView.vue:268 src/views/website/IndexView.vue:435
msgid "PHP Version"
msgstr ""

#: src/views/website/EditView.vue:273 src/views/website/IndexView.vue:439
msgid "Select PHP Version"
msgstr ""

#: src/views/website/EditView.vue:278
msgid "Anti-XSS Attack (PHP)"
msgstr ""

#: src/views/website/EditView.vue:287
#, fuzzy
msgid "Certificate Information"
msgstr "Certificate"

#: src/views/website/EditView.vue:289
#, fuzzy
msgid "Certificate Validity"
msgstr "Certificate"

#: src/views/website/EditView.vue:303
msgid "Domains"
msgstr ""

#: src/views/website/EditView.vue:318
msgid "Main Switch"
msgstr ""

#: src/views/website/EditView.vue:321
#, fuzzy
msgid "Use Existing Certificate"
msgstr "Certificate"

#: src/views/website/EditView.vue:334
msgid "HTTP Redirect"
msgstr ""

#: src/views/website/EditView.vue:337
msgid "OCSP Stapling"
msgstr ""

#: src/views/website/EditView.vue:362
msgid "Rewrite"
msgstr ""

#: src/views/website/EditView.vue:365
msgid "Presets"
msgstr ""

#: src/views/website/EditView.vue:391
msgid ""
"If you do not understand the configuration rules, please do not modify them "
"arbitrarily, otherwise it may cause the website to be inaccessible or panel "
"function abnormalities! If you have already encountered a problem, try "
"resetting the configuration!"
msgstr ""

#: src/views/website/EditView.vue:407
msgid "Access Log"
msgstr ""

#: src/views/website/EditView.vue:411 src/views/website/EditView.vue:423
msgid "All logs can be viewed by downloading the file"
msgstr ""

#: src/views/website/EditView.vue:413 src/views/website/EditView.vue:425
msgid "view"
msgstr ""

#: src/views/website/EditView.vue:419
msgid "Error Log"
msgstr ""

#: src/views/website/IndexView.vue:23 src/views/website/IndexView.vue:398
msgid "Website Name"
msgstr ""

#: src/views/website/IndexView.vue:78 src/views/website/IndexView.vue:516
#: src/views/website/IndexView.vue:521
msgid "Remark"
msgstr ""

#: src/views/website/IndexView.vue:135
msgid "Are you sure you want to delete website %{ name }?"
msgstr ""

#: src/views/website/IndexView.vue:146
msgid "Delete website directory"
msgstr ""

#: src/views/website/IndexView.vue:154
msgid "Delete local database with the same name"
msgstr ""

#: src/views/website/IndexView.vue:240
msgid "Already %{ status }"
msgstr ""

#: src/views/website/IndexView.vue:241
msgid "started"
msgstr ""

#: src/views/website/IndexView.vue:241
msgid "stopped"
msgstr ""

#: src/views/website/IndexView.vue:315
msgid "Please select the websites to delete"
msgstr ""

#: src/views/website/IndexView.vue:348 src/views/website/IndexView.vue:389
#, fuzzy
msgid "Create Website"
msgstr "Certificate"

#: src/views/website/IndexView.vue:355
msgid ""
"This will delete the website directory but not the database with the same "
"name. Are you sure you want to delete the selected websites?"
msgstr ""

#: src/views/website/IndexView.vue:361 src/views/website/IndexView.vue:532
msgid "Modify Default Page"
msgstr ""

#: src/views/website/IndexView.vue:404
msgid ""
"Recommended to use English for the website name, it cannot be modified after "
"setting"
msgstr ""

#: src/views/website/IndexView.vue:451
msgid "Select Database"
msgstr ""

#: src/views/website/IndexView.vue:479 src/views/website/IndexView.vue:484
msgid "Database User"
msgstr ""

#: src/views/website/IndexView.vue:493 src/views/website/IndexView.vue:499
msgid "Database Password"
msgstr ""

#: src/views/website/IndexView.vue:510
msgid ""
"Website root directory (if left empty, defaults to website directory/website "
"name)"
msgstr ""

#: src/views/website/IndexView.vue:526
msgid "Create"
msgstr ""

#: src/views/website/IndexView.vue:540
msgid "Default Page"
msgstr ""

#: src/views/website/IndexView.vue:554
msgid "Stop Page"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:23
msgid "Disabled buffer and enabled cache cannot be used simultaneously"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:27
msgid "Matching expression cannot be empty"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:31
msgid "Proxy address cannot be empty"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:35
msgid "Exact match expression must start with /"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:42
msgid "Prefix match expression must start with /"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:48
msgid "Proxy address format error"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:100
#, fuzzy
msgid "Configuration generated successfully"
msgstr "Saved successfully"

#: src/views/website/ProxyBuilderModal.vue:128
msgid ""
"After generating the reverse proxy configuration, the original rewrite rules "
"will be overwritten."
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:130
msgid ""
"If you need to proxy static resources like JS/CSS, please remove the static "
"log recording part from the original configuration."
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:133
msgid "Auto Refresh Resolution"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:136
msgid "Enable SNI"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:139
msgid "Enable Cache"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:142
msgid "Disable Buffer"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:147
msgid "Match Type"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:151
msgid "Exact Match (=)"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:152
msgid "Priority Prefix Match (^~)"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:153
msgid "Normal Prefix Match ( )"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:154
msgid "Case Sensitive Regex Match (~)"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:155
msgid "Case Insensitive Regex Match (~*)"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:159
msgid "Match Expression"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:162
msgid "Proxy Address"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:165
msgid "Send Domain"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:168
msgid "Cache Time"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:174
msgid "Cache time (minutes)"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:176
msgid "minutes"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:179
msgid "Content Replacement"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:184
msgid "Target content"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:185
msgid "Replacement content"
msgstr ""

#: src/views/website/route.ts:19
#, fuzzy
msgid "Websites"
msgstr "Certificate"
