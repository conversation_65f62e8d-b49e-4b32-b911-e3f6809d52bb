msgid ""
msgstr ""
"Content-Type: text/plain; charset=UTF-8\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: ratpanel\n"
"X-Crowdin-Project-ID: 778640\n"
"X-Crowdin-Language: zh-CN\n"
"X-Crowdin-File: /main/web/src/locales/frontend.pot\n"
"X-Crowdin-File-ID: 934\n"
"Project-Id-Version: ratpanel\n"
"Language-Team: Chinese Simplified\n"
"Language: zh_CN\n"
"PO-Revision-Date: 2025-05-18 18:03\n"

#: src/components/common/AppFooter.vue:13
#: src/views/dashboard/IndexView.vue:439
msgid "Rat Panel"
msgstr "耗子面板"

#: src/components/common/AppFooter.vue:15
msgid "All Rights Reserved."
msgstr "版权所有"

#: src/components/common/AppFooter.vue:23
msgid "QQ Group"
msgstr "QQ 群"

#: src/components/common/AppFooter.vue:31
msgid "Documentation"
msgstr "文档"

#: src/components/common/AppFooter.vue:39
msgid "Community"
msgstr "社区"

#: src/components/common/AppFooter.vue:47
msgid "Sponsor"
msgstr "赞助"

#: src/components/common/CodeEditor.vue:27
msgid "Retrieved successfully"
msgstr "获取成功"

#: src/components/common/CodeEditor.vue:36
msgid "Cannot save in current state"
msgstr "当前状态无法保存"

#: src/components/common/CodeEditor.vue:40
#: src/views/apps/codeserver/IndexView.vue:24
#: src/views/apps/docker/IndexView.vue:24
#: src/views/apps/fail2ban/IndexView.vue:164
#: src/views/apps/frp/IndexView.vue:28
#: src/views/apps/gitea/IndexView.vue:22
#: src/views/apps/memcached/IndexView.vue:44
#: src/views/apps/minio/IndexView.vue:22
#: src/views/apps/mysql/IndexView.vue:47
#: src/views/apps/nginx/IndexView.vue:44
#: src/views/apps/php/PhpView.vue:146
#: src/views/apps/php/PhpView.vue:152
#: src/views/apps/phpmyadmin/IndexView.vue:37
#: src/views/apps/phpmyadmin/IndexView.vue:44
#: src/views/apps/podman/IndexView.vue:26
#: src/views/apps/podman/IndexView.vue:32
#: src/views/apps/postgresql/IndexView.vue:47
#: src/views/apps/postgresql/IndexView.vue:52
#: src/views/apps/pureftpd/IndexView.vue:117
#: src/views/apps/redis/IndexView.vue:41
#: src/views/apps/rsync/IndexView.vue:136
#: src/views/apps/rsync/IndexView.vue:180
#: src/views/apps/supervisor/IndexView.vue:227
#: src/views/apps/supervisor/IndexView.vue:292
#: src/views/setting/IndexView.vue:44
#: src/views/toolbox/SystemView.vue:51
#: src/views/toolbox/SystemView.vue:57
#: src/views/toolbox/SystemView.vue:66
#: src/views/toolbox/SystemView.vue:72
#: src/views/toolbox/SystemView.vue:81
#: src/views/website/EditView.vue:115
msgid "Saved successfully"
msgstr "保存成功"

#: src/components/common/PathSelector.vue:20
msgid "Select Directory"
msgstr "选择目录"

#: src/components/common/PathSelector.vue:20
msgid "Select File"
msgstr "选择文件"

#: src/components/common/PathSelector.vue:42
#: src/components/common/PathSelector.vue:331
#: src/views/apps/fail2ban/IndexView.vue:38
#: src/views/apps/rsync/IndexView.vue:40
#: src/views/apps/rsync/IndexView.vue:280
#: src/views/apps/supervisor/IndexView.vue:48
#: src/views/apps/supervisor/IndexView.vue:399
#: src/views/container/ComposeView.vue:31
#: src/views/container/NetworkView.vue:45
#: src/views/container/VolumeView.vue:26
#: src/views/database/CreateServerModal.vue:55
#: src/views/database/ServerList.vue:40
#: src/views/database/UpdateServerModal.vue:55
#: src/views/file/ListTable.vue:103
#: src/views/file/SearchModal.vue:20
#: src/views/file/ToolBar.vue:257
#: src/views/ssh/CreateModal.vue:57
#: src/views/ssh/UpdateModal.vue:63
#: src/views/task/SystemView.vue:18
msgid "Name"
msgstr "名称"

#: src/components/common/PathSelector.vue:83
msgid "Permissions"
msgstr "权限"

#: src/components/common/PathSelector.vue:95
#: src/views/file/ListTable.vue:157
msgid "Owner / Group"
msgstr "所有者 / 组"

#: src/components/common/PathSelector.vue:107
#: src/views/backup/ListView.vue:43
#: src/views/container/ImageView.vue:53
#: src/views/file/ListTable.vue:169
#: src/views/file/SearchModal.vue:28
msgid "Size"
msgstr "大小"

#: src/components/common/PathSelector.vue:115
#: src/views/file/ListTable.vue:177
#: src/views/file/SearchModal.vue:36
msgid "Modification Time"
msgstr "修改时间"

#: src/components/common/PathSelector.vue:148
#: src/views/file/ListTable.vue:456
#: src/views/file/PathInput.vue:33
msgid "Invalid path"
msgstr "无效路径"

#: src/components/common/PathSelector.vue:208
#: src/views/file/ListTable.vue:405
#: src/views/file/ToolBar.vue:37
#: src/views/file/ToolBar.vue:51
msgid "Invalid name"
msgstr "无效的名称"

#: src/components/common/PathSelector.vue:216
#: src/views/backup/ListView.vue:125
#: src/views/cert/CreateAccountModal.vue:52
#: src/views/cert/CreateCertModal.vue:50
#: src/views/cert/CreateDnsModal.vue:35
#: src/views/cert/UploadCertModal.vue:21
#: src/views/container/ComposeView.vue:246
#: src/views/container/ContainerCreate.vue:106
#: src/views/container/NetworkView.vue:174
#: src/views/container/VolumeView.vue:128
#: src/views/database/CreateDatabaseModal.vue:28
#: src/views/database/CreateUserModal.vue:28
#: src/views/file/ToolBar.vue:45
#: src/views/firewall/CreateForwardModal.vue:41
#: src/views/firewall/CreateModal.vue:84
#: src/views/setting/CreateModal.vue:19
#: src/views/setting/TokenModal.vue:127
#: src/views/ssh/CreateModal.vue:38
#: src/views/task/CreateModal.vue:51
msgid "Created successfully"
msgstr "创建成功"

#: src/components/common/PathSelector.vue:255
#: src/views/file/ToolBar.vue:214
msgid "File"
msgstr "文件"

#: src/components/common/PathSelector.vue:256
#: src/views/file/ToolBar.vue:215
msgid "Folder"
msgstr "文件夹"

#: src/components/common/PathSelector.vue:260
#: src/components/common/PathSelector.vue:323
#: src/views/setting/TokenModal.vue:268
#: src/views/website/BulkCreate.vue:127
#: src/views/website/IndexView.vue:539
msgid "Create"
msgstr "创建"

#: src/components/common/PathSelector.vue:269
#: src/views/file/PathInput.vue:133
msgid "Root Directory"
msgstr "根目录"

#: src/components/common/PathSelector.vue:335
#: src/views/app/VersionModal.vue:96
#: src/views/apps/fail2ban/IndexView.vue:375
#: src/views/apps/pureftpd/IndexView.vue:249
#: src/views/apps/pureftpd/IndexView.vue:271
#: src/views/apps/rsync/IndexView.vue:329
#: src/views/apps/s3fs/IndexView.vue:184
#: src/views/apps/supervisor/IndexView.vue:435
#: src/views/backup/ListView.vue:244
#: src/views/backup/ListView.vue:268
#: src/views/cert/AccountView.vue:268
#: src/views/cert/CertView.vue:535
#: src/views/cert/CertView.vue:559
#: src/views/cert/CreateAccountModal.vue:125
#: src/views/cert/CreateCertModal.vue:115
#: src/views/cert/CreateDnsModal.vue:237
#: src/views/cert/DnsView.vue:373
#: src/views/cert/ObtainModal.vue:130
#: src/views/cert/UploadCertModal.vue:55
#: src/views/container/ComposeView.vue:340
#: src/views/container/ComposeView.vue:370
#: src/views/container/ContainerCreate.vue:370
#: src/views/container/ContainerView.vue:481
#: src/views/container/ImageView.vue:217
#: src/views/container/NetworkView.vue:313
#: src/views/container/VolumeView.vue:213
#: src/views/database/CreateDatabaseModal.vue:131
#: src/views/database/CreateServerModal.vue:120
#: src/views/database/CreateUserModal.vue:118
#: src/views/database/UpdateServerModal.vue:112
#: src/views/database/UpdateUserModal.vue:73
#: src/views/file/ToolBar.vue:261
#: src/views/file/ToolBar.vue:282
#: src/views/firewall/CreateForwardModal.vue:88
#: src/views/firewall/CreateIpModal.vue:122
#: src/views/firewall/CreateModal.vue:147
#: src/views/setting/CreateModal.vue:64
#: src/views/setting/PasswordModal.vue:44
#: src/views/setting/TwoFaModal.vue:87
#: src/views/ssh/CreateModal.vue:99
#: src/views/ssh/UpdateModal.vue:105
#: src/views/task/CreateModal.vue:171
#: src/views/website/ProxyBuilderModal.vue:201
msgid "Submit"
msgstr "提交"

#: src/components/common/RealtimeLog.vue:29
msgid "Path or service cannot be empty"
msgstr "路径或服务不能为空"

#: src/components/common/RealtimeLog.vue:44
#: src/components/common/RealtimeLogModal.vue:33
msgid "Failed to get log stream"
msgstr "获取日志流失败"

#: src/components/common/RealtimeLogModal.vue:67
#: src/views/apps/supervisor/IndexView.vue:91
#: src/views/container/ContainerView.vue:102
#: src/views/container/ContainerView.vue:442
#: src/views/task/CronView.vue:120
#: src/views/task/TaskView.vue:74
msgid "Logs"
msgstr "日志"

#: src/components/common/ServiceStatus.vue:25
#: src/views/dashboard/IndexView.vue:733
#: src/views/dashboard/IndexView.vue:741
#: src/views/dashboard/IndexView.vue:748
#: src/views/dashboard/IndexView.vue:754
#: src/views/dashboard/IndexView.vue:765
#: src/views/dashboard/IndexView.vue:777
msgid "Loading..."
msgstr "加载中……"

#: src/components/common/ServiceStatus.vue:26
#: src/views/task/SystemView.vue:50
#: src/views/task/TaskView.vue:33
#: src/views/website/IndexView.vue:31
msgid "Running"
msgstr "运行中"

#: src/components/common/ServiceStatus.vue:26
#: src/views/task/SystemView.vue:54
msgid "Stopped"
msgstr "已停止"

#: src/components/common/ServiceStatus.vue:42
#: src/views/container/ComposeView.vue:104
msgid "Starting..."
msgstr "正在启动……"

#: src/components/common/ServiceStatus.vue:48
#: src/views/apps/supervisor/IndexView.vue:248
#: src/views/website/IndexView.vue:239
msgid "Started successfully"
msgstr "启动成功"

#: src/components/common/ServiceStatus.vue:57
msgid "Stopping..."
msgstr "停止中..."

#: src/components/common/ServiceStatus.vue:63
#: src/views/apps/supervisor/IndexView.vue:255
#: src/views/website/IndexView.vue:241
msgid "Stopped successfully"
msgstr "停止成功"

#: src/components/common/ServiceStatus.vue:72
msgid "Restarting..."
msgstr "重启中..."

#: src/components/common/ServiceStatus.vue:78
#: src/views/apps/supervisor/IndexView.vue:262
msgid "Restarted successfully"
msgstr "重启成功"

#: src/components/common/ServiceStatus.vue:87
msgid "Reloading..."
msgstr "重载中..."

#: src/components/common/ServiceStatus.vue:93
msgid "Reloaded successfully"
msgstr "重载成功"

#: src/components/common/ServiceStatus.vue:102
msgid "Setting autostart..."
msgstr "设置自启动中..."

#: src/components/common/ServiceStatus.vue:109
msgid "Autostart enabled successfully"
msgstr "自启动已成功启用"

#: src/components/common/ServiceStatus.vue:118
msgid "Autostart disabled successfully"
msgstr "自启动已成功禁用"

#: src/components/common/ServiceStatus.vue:134
#: src/views/apps/codeserver/IndexView.vue:43
#: src/views/apps/docker/IndexView.vue:43
#: src/views/apps/fail2ban/IndexView.vue:252
#: src/views/apps/gitea/IndexView.vue:41
#: src/views/apps/memcached/IndexView.vue:63
#: src/views/apps/minio/IndexView.vue:36
#: src/views/apps/mysql/IndexView.vue:101
#: src/views/apps/nginx/IndexView.vue:78
#: src/views/apps/php/PhpView.vue:225
#: src/views/apps/podman/IndexView.vue:60
#: src/views/apps/postgresql/IndexView.vue:88
#: src/views/apps/pureftpd/IndexView.vue:175
#: src/views/apps/redis/IndexView.vue:60
#: src/views/apps/rsync/IndexView.vue:214
#: src/views/apps/supervisor/IndexView.vue:330
#: src/views/container/ContainerView.vue:80
msgid "Running Status"
msgstr "运行状态"

#: src/components/common/ServiceStatus.vue:141
msgid "Autostart On"
msgstr "自启动开启"

#: src/components/common/ServiceStatus.vue:142
msgid "Autostart Off"
msgstr "自启动关闭"

#: src/components/common/ServiceStatus.vue:152
#: src/views/apps/supervisor/IndexView.vue:119
#: src/views/container/ComposeView.vue:158
#: src/views/container/ContainerView.vue:126
#: src/views/container/ContainerView.vue:408
msgid "Start"
msgstr "启动"

#: src/components/common/ServiceStatus.vue:158
#: src/views/apps/supervisor/IndexView.vue:145
#: src/views/container/ComposeView.vue:189
#: src/views/container/ContainerView.vue:131
#: src/views/container/ContainerView.vue:409
msgid "Stop"
msgstr "停止"

#: src/components/common/ServiceStatus.vue:161
msgid "Are you sure you want to stop %{ service }?"
msgstr "您确定要停止 %{ service } 吗？"

#: src/components/common/ServiceStatus.vue:165
#: src/views/apps/supervisor/IndexView.vue:174
#: src/views/container/ContainerView.vue:136
#: src/views/container/ContainerView.vue:410
#: src/views/dashboard/IndexView.vue:447
msgid "Restart"
msgstr "重启"

#: src/components/common/ServiceStatus.vue:174
#: src/layout/tab/components/ContextMenu.vue:34
msgid "Reload"
msgstr "重载"

#: src/layout/header/components/FullScreen.vue:16
msgid "Fullscreen Display"
msgstr "全屏显示"

#: src/layout/header/components/MenuCollapse.vue:17
msgid "Menu Zoom"
msgstr "菜单缩放"

#: src/layout/header/components/ReloadPage.vue:20
msgid "Refresh Tab"
msgstr "刷新标签页"

#: src/layout/header/components/ThemeMode.vue:17
msgid "Switch Theme"
msgstr "切换主题"

#: src/layout/header/components/ThemeSetting.vue:19
msgid "Set Theme Color"
msgstr "设置主题颜色"

#: src/layout/header/components/UserAvatar.vue:13
#: src/views/apps/pureftpd/IndexView.vue:65
#: src/views/apps/pureftpd/IndexView.vue:256
#: src/views/setting/PasswordModal.vue:26
#: src/views/setting/SettingUser.vue:109
msgid "Change Password"
msgstr "更改密码"

#: src/layout/header/components/UserAvatar.vue:18
msgid "Logout"
msgstr "登出"

#: src/layout/header/components/UserAvatar.vue:27
msgid "Confirm logout?"
msgstr "确认退出登录？"

#: src/layout/header/components/UserAvatar.vue:28
msgid "Prompt"
msgstr "提示"

#: src/layout/header/components/UserAvatar.vue:29
#: src/views/dashboard/UpdateView.vue:26
#: src/views/monitor/IndexView.vue:474
msgid "Confirm"
msgstr "确认"

#: src/layout/header/components/UserAvatar.vue:30
#: src/views/dashboard/UpdateView.vue:27
#: src/views/file/ListTable.vue:415
#: src/views/file/ListTable.vue:510
#: src/views/file/ToolBar.vue:139
#: src/views/file/ToolBar.vue:226
msgid "Cancel"
msgstr "取消"

#: src/layout/header/components/UserAvatar.vue:35
msgid "Logged out successfully!"
msgstr "退出登录成功！"

#: src/layout/header/components/UserAvatar.vue:48
#: src/views/cert/DnsView.vue:56
#: src/views/firewall/IpRuleView.vue:77
#: src/views/firewall/IpRuleView.vue:102
#: src/views/firewall/RuleView.vue:111
#: src/views/firewall/RuleView.vue:136
msgid "Unknown"
msgstr "未知"

#: src/layout/sidebar/components/SideSetting.vue:66
#: src/layout/sidebar/components/SideSetting.vue:71
msgid "Menu Settings"
msgstr "菜单设置"

#: src/layout/sidebar/components/SideSetting.vue:83
msgid "Settings are saved in the browser and will be reset after clearing the browser cache"
msgstr "设置保存在浏览器中，清除浏览器缓存后将重置"

#: src/layout/sidebar/components/SideSetting.vue:88
msgid "Custom Logo"
msgstr "自定义 Logo"

#: src/layout/sidebar/components/SideSetting.vue:91
msgid "Please enter the complete URL"
msgstr "请输入完整的 URL"

#: src/layout/sidebar/components/SideSetting.vue:94
msgid "Hide Menu"
msgstr "隐藏菜单"

#: src/layout/tab/components/ContextMenu.vue:28
msgid "Close"
msgstr "关闭"

#: src/layout/tab/components/ContextMenu.vue:40
msgid "Pin"
msgstr "固定"

#: src/layout/tab/components/ContextMenu.vue:46
msgid "Unpin"
msgstr "取消固定"

#: src/layout/tab/components/ContextMenu.vue:52
msgid "Close Others"
msgstr "关闭其他"

#: src/layout/tab/components/ContextMenu.vue:58
msgid "Close Left"
msgstr "关闭左侧"

#: src/layout/tab/components/ContextMenu.vue:64
msgid "Close Right"
msgstr "关闭右侧"

#: src/locales/menu.ts:7
msgid "Apps"
msgstr "应用"

#: src/locales/menu.ts:8
msgid "Backup"
msgstr "备份"

#: src/locales/menu.ts:9
#: src/views/cert/CertView.vue:497
#: src/views/cert/CertView.vue:573
#: src/views/cert/UploadCertModal.vue:38
#: src/views/setting/SettingSafe.vue:54
#: src/views/website/EditView.vue:355
msgid "Certificate"
msgstr "证书"

#: src/locales/menu.ts:10
msgid "Container"
msgstr "容器"

#: src/locales/menu.ts:11
msgid "Dashboard"
msgstr "仪表板"

#: src/locales/menu.ts:12
#: src/views/app/IndexView.vue:96
#: src/views/dashboard/IndexView.vue:451
#: src/views/setting/TokenModal.vue:301
msgid "Update"
msgstr "更新"

#: src/locales/menu.ts:13
#: src/views/backup/ListView.vue:264
#: src/views/dashboard/IndexView.vue:425
#: src/views/database/IndexView.vue:45
#: src/views/website/IndexView.vue:460
msgid "Database"
msgstr "数据库"

#: src/locales/menu.ts:14
msgid "Files"
msgstr "文件"

#: src/locales/menu.ts:15
msgid "Firewall"
msgstr "防火墙"

#: src/locales/menu.ts:16
msgid "Monitoring"
msgstr "监控"

#: src/locales/menu.ts:17
#: src/views/firewall/IndexView.vue:28
msgid "Settings"
msgstr "设置"

#: src/locales/menu.ts:18
msgid "Terminal"
msgstr "终端"

#: src/locales/menu.ts:19
msgid "Tasks"
msgstr "任务"

#: src/locales/menu.ts:20
msgid "Toolbox"
msgstr "工具箱"

#: src/locales/menu.ts:21
msgid "System"
msgstr "系统"

#: src/locales/menu.ts:22
msgid "Benchmark"
msgstr "跑分"

#: src/locales/menu.ts:23
#: src/views/apps/fail2ban/IndexView.vue:322
#: src/views/backup/IndexView.vue:37
#: src/views/backup/ListView.vue:220
#: src/views/backup/ListView.vue:257
#: src/views/cert/CertView.vue:466
#: src/views/cert/CertView.vue:549
#: src/views/cert/CreateCertModal.vue:90
#: src/views/dashboard/IndexView.vue:421
#: src/views/task/CreateModal.vue:130
msgid "Website"
msgstr "网站"

#: src/locales/menu.ts:24
msgid "Website Edit"
msgstr "网站编辑"

#: src/locales/menu.ts:26
msgid "Fail2ban Manager"
msgstr "Fail2ban 管理器"

#: src/locales/menu.ts:27
msgid "S3fs Manager"
msgstr "S3fs 管理器"

#: src/locales/menu.ts:28
msgid "Supervisor Manager"
msgstr "Supervisor 管理器"

#: src/locales/menu.ts:29
msgid "Rsync Manager"
msgstr "Rsync 管理器"

#: src/locales/menu.ts:30
msgid "Frp Manager"
msgstr "Frp 管理器"

#: src/router/routes/index.ts:18
msgid "Login Page"
msgstr "登录页面"

#: src/views/app/IndexView.vue:19
#: src/views/app/IndexView.vue:154
#: src/views/app/IndexView.vue:159
#: src/views/apps/php/PhpView.vue:81
msgid "Install"
msgstr "安装"

#: src/views/app/IndexView.vue:37
msgid "App Name"
msgstr "应用名称"

#: src/views/app/IndexView.vue:43
#: src/views/apps/php/PhpView.vue:50
msgid "Description"
msgstr "描述"

#: src/views/app/IndexView.vue:49
msgid "Installed Version"
msgstr "已安装版本"

#: src/views/app/IndexView.vue:55
msgid "Show in Home"
msgstr "在主页显示"

#: src/views/app/IndexView.vue:68
#: src/views/apps/fail2ban/IndexView.vue:60
#: src/views/apps/fail2ban/IndexView.vue:121
#: src/views/apps/php/PhpView.vue:57
#: src/views/apps/pureftpd/IndexView.vue:46
#: src/views/apps/rsync/IndexView.vue:69
#: src/views/apps/s3fs/IndexView.vue:33
#: src/views/apps/supervisor/IndexView.vue:76
#: src/views/backup/ListView.vue:58
#: src/views/cert/AccountView.vue:79
#: src/views/cert/CertView.vue:181
#: src/views/cert/DnsView.vue:64
#: src/views/container/ComposeView.vue:74
#: src/views/container/ContainerCreate.vue:174
#: src/views/container/ContainerCreate.vue:255
#: src/views/container/ContainerView.vue:87
#: src/views/container/ImageView.vue:69
#: src/views/container/NetworkView.vue:109
#: src/views/container/VolumeView.vue:63
#: src/views/database/DatabaseList.vue:74
#: src/views/database/ServerList.vue:135
#: src/views/database/UserList.vue:152
#: src/views/file/ListTable.vue:189
#: src/views/file/SearchModal.vue:48
#: src/views/firewall/ForwardView.vue:80
#: src/views/firewall/IpRuleView.vue:122
#: src/views/firewall/RuleView.vue:159
#: src/views/setting/SettingUser.vue:76
#: src/views/setting/TokenModal.vue:52
#: src/views/task/CronView.vue:102
#: src/views/task/SystemView.vue:96
#: src/views/task/TaskView.vue:55
#: src/views/website/IndexView.vue:94
msgid "Actions"
msgstr "操作"

#: src/views/app/IndexView.vue:83
msgid "Updating app %{ app } may reset related configurations to default state, are you sure to continue?"
msgstr "更新应用 %{ app } 可能会将相关配置重置为默认状态，您确定要继续吗？"

#: src/views/app/IndexView.vue:115
msgid "Manage"
msgstr "管理"

#: src/views/app/IndexView.vue:128
msgid "Are you sure to uninstall app %{ app }?"
msgstr "您确定要卸载应用 %{ app } 吗？"

#: src/views/app/IndexView.vue:138
msgid "Uninstall"
msgstr "卸载"

#: src/views/app/IndexView.vue:183
msgid "Setup successfully"
msgstr "设置成功"

#: src/views/app/IndexView.vue:190
#: src/views/app/IndexView.vue:198
#: src/views/app/VersionModal.vue:32
msgid "Task submitted, please check the progress in background tasks"
msgstr "任务已提交，请在后台任务中查看进度"

#: src/views/app/IndexView.vue:210
msgid "Cache updated successfully"
msgstr "缓存更新成功"

#: src/views/app/IndexView.vue:224
msgid "Update Cache"
msgstr "更新缓存"

#: src/views/app/IndexView.vue:229
msgid "Before updating apps, it is strongly recommended to backup/snapshot first, so you can roll back immediately if there are any issues!"
msgstr "在更新应用前，强烈建议先进行备份/快照，这样如果出现任何问题，可以第一时间回滚！"

#: src/views/app/VersionModal.vue:73
msgid "Channel"
msgstr "渠道"

#: src/views/app/VersionModal.vue:80
msgid "Version"
msgstr "版本"

#: src/views/app/VersionModal.vue:83
msgid "Please select a channel"
msgstr "请选择一个渠道"

#: src/views/apps/codeserver/IndexView.vue:39
#: src/views/apps/docker/IndexView.vue:39
#: src/views/apps/frp/IndexView.vue:48
#: src/views/apps/frp/IndexView.vue:73
#: src/views/apps/gitea/IndexView.vue:37
#: src/views/apps/memcached/IndexView.vue:59
#: src/views/apps/minio/IndexView.vue:32
#: src/views/apps/mysql/IndexView.vue:79
#: src/views/apps/nginx/IndexView.vue:65
#: src/views/apps/php/PhpView.vue:194
#: src/views/apps/php/PhpView.vue:203
#: src/views/apps/phpmyadmin/IndexView.vue:58
#: src/views/apps/phpmyadmin/IndexView.vue:67
#: src/views/apps/podman/IndexView.vue:47
#: src/views/apps/podman/IndexView.vue:56
#: src/views/apps/postgresql/IndexView.vue:71
#: src/views/apps/postgresql/IndexView.vue:80
#: src/views/apps/pureftpd/IndexView.vue:162
#: src/views/apps/redis/IndexView.vue:56
#: src/views/apps/rsync/IndexView.vue:201
#: src/views/apps/supervisor/IndexView.vue:313
#: src/views/file/EditModal.vue:31
#: src/views/file/ListTable.vue:723
#: src/views/setting/IndexView.vue:65
#: src/views/toolbox/SystemView.vue:97
#: src/views/toolbox/SystemView.vue:101
#: src/views/toolbox/SystemView.vue:105
#: src/views/toolbox/SystemView.vue:109
#: src/views/website/EditView.vue:215
msgid "Save"
msgstr "保存"

#: src/views/apps/codeserver/IndexView.vue:46
#: src/views/apps/frp/IndexView.vue:44
#: src/views/apps/frp/IndexView.vue:69
#: src/views/apps/gitea/IndexView.vue:44
#: src/views/apps/mysql/IndexView.vue:118
#: src/views/apps/nginx/IndexView.vue:81
#: src/views/apps/phpmyadmin/IndexView.vue:84
msgid "Modify Configuration"
msgstr "修改配置"

#: src/views/apps/codeserver/IndexView.vue:50
msgid "This modifies the Code Server configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 Code Server 配置文件。如果您不理解每个参数的含义，请不要随意修改！"

#: src/views/apps/codeserver/IndexView.vue:69
#: src/views/apps/docker/IndexView.vue:65
#: src/views/apps/fail2ban/IndexView.vue:289
#: src/views/apps/gitea/IndexView.vue:67
#: src/views/apps/memcached/IndexView.vue:92
#: src/views/apps/minio/IndexView.vue:62
#: src/views/apps/mysql/IndexView.vue:151
#: src/views/apps/nginx/IndexView.vue:114
#: src/views/apps/php/PhpView.vue:299
#: src/views/apps/podman/IndexView.vue:118
#: src/views/apps/postgresql/IndexView.vue:147
#: src/views/apps/redis/IndexView.vue:96
#: src/views/apps/rsync/IndexView.vue:264
#: src/views/apps/supervisor/IndexView.vue:380
msgid "Runtime Logs"
msgstr "运行日志"

#: src/views/apps/docker/IndexView.vue:46
#: src/views/website/EditView.vue:401
msgid "Configuration"
msgstr "配置"

#: src/views/apps/docker/IndexView.vue:49
msgid "This modifies the Docker configuration file (/etc/docker/daemon.json)"
msgstr "这将修改 Docker 配置文件 (/etc/docker/daemon.json)"

#: src/views/apps/fail2ban/IndexView.vue:44
#: src/views/apps/phpmyadmin/IndexView.vue:71
#: src/views/apps/supervisor/IndexView.vue:55
#: src/views/container/ComposeView.vue:58
#: src/views/container/ContainerView.vue:32
#: src/views/database/ServerList.vue:114
#: src/views/database/UserList.vue:131
#: src/views/firewall/RuleView.vue:62
#: src/views/task/SystemView.vue:43
#: src/views/task/TaskView.vue:22
msgid "Status"
msgstr "状态"

#: src/views/apps/fail2ban/IndexView.vue:56
#: src/views/apps/fail2ban/IndexView.vue:365
msgid "Max Retries"
msgstr "最大重试次数"

#: src/views/apps/fail2ban/IndexView.vue:57
#: src/views/apps/fail2ban/IndexView.vue:371
msgid "Ban Time"
msgstr "封禁时间"

#: src/views/apps/fail2ban/IndexView.vue:58
#: src/views/apps/fail2ban/IndexView.vue:368
msgid "Find Time"
msgstr "查找时间"

#: src/views/apps/fail2ban/IndexView.vue:78
#: src/views/cert/CertView.vue:263
msgid "View"
msgstr "查看"

#: src/views/apps/fail2ban/IndexView.vue:89
msgid "Are you sure you want to delete rule %{ name }?"
msgstr "您确定要删除规则 %{ name } 吗？"

#: src/views/apps/fail2ban/IndexView.vue:100
#: src/views/apps/php/PhpView.vue:109
#: src/views/apps/pureftpd/IndexView.vue:89
#: src/views/apps/rsync/IndexView.vue:107
#: src/views/apps/supervisor/IndexView.vue:202
#: src/views/backup/ListView.vue:98
#: src/views/cert/AccountView.vue:127
#: src/views/cert/CertView.vue:314
#: src/views/cert/DnsView.vue:111
#: src/views/container/ComposeView.vue:220
#: src/views/container/ContainerCreate.vue:235
#: src/views/container/ContainerCreate.vue:286
#: src/views/container/ContainerView.vue:156
#: src/views/container/ContainerView.vue:414
#: src/views/container/ImageView.vue:94
#: src/views/container/NetworkView.vue:134
#: src/views/container/VolumeView.vue:88
#: src/views/database/DatabaseList.vue:98
#: src/views/database/ServerList.vue:217
#: src/views/database/UserList.vue:191
#: src/views/file/ListTable.vue:85
#: src/views/file/ListTable.vue:289
#: src/views/file/SearchModal.vue:98
#: src/views/file/ToolBar.vue:238
#: src/views/firewall/ForwardView.vue:104
#: src/views/firewall/IpRuleView.vue:146
#: src/views/firewall/RuleView.vue:183
#: src/views/setting/SettingUser.vue:132
#: src/views/setting/TokenModal.vue:92
#: src/views/ssh/IndexView.vue:93
#: src/views/task/CronView.vue:155
#: src/views/task/TaskView.vue:98
#: src/views/website/IndexView.vue:167
msgid "Delete"
msgstr "删除"

#: src/views/apps/fail2ban/IndexView.vue:134
msgid "Are you sure you want to unban %{ ip }?"
msgstr "您确定要解封 %{ ip } 吗？"

#: src/views/apps/fail2ban/IndexView.vue:144
msgid "Unban"
msgstr "解封"

#: src/views/apps/fail2ban/IndexView.vue:192
#: src/views/apps/pureftpd/IndexView.vue:130
#: src/views/apps/rsync/IndexView.vue:153
#: src/views/apps/s3fs/IndexView.vue:84
#: src/views/apps/supervisor/IndexView.vue:241
#: src/views/database/CreateServerModal.vue:37
msgid "Added successfully"
msgstr "添加成功"

#: src/views/apps/fail2ban/IndexView.vue:200
#: src/views/apps/pureftpd/IndexView.vue:147
#: src/views/apps/rsync/IndexView.vue:161
#: src/views/apps/s3fs/IndexView.vue:91
#: src/views/apps/supervisor/IndexView.vue:269
#: src/views/backup/ListView.vue:148
#: src/views/database/DatabaseList.vue:123
#: src/views/database/ServerList.vue:242
#: src/views/database/UserList.vue:216
#: src/views/file/ListTable.vue:272
#: src/views/file/ListTable.vue:615
#: src/views/file/SearchModal.vue:81
#: src/views/file/ToolBar.vue:189
#: src/views/firewall/ForwardView.vue:131
#: src/views/firewall/ForwardView.vue:149
#: src/views/firewall/IpRuleView.vue:173
#: src/views/firewall/IpRuleView.vue:191
#: src/views/firewall/RuleView.vue:210
#: src/views/firewall/RuleView.vue:228
#: src/views/setting/SettingUser.vue:162
#: src/views/setting/TokenModal.vue:116
#: src/views/task/CronView.vue:198
#: src/views/task/TaskView.vue:124
#: src/views/website/IndexView.vue:269
#: src/views/website/IndexView.vue:325
msgid "Deleted successfully"
msgstr "删除成功"

#: src/views/apps/fail2ban/IndexView.vue:213
msgid "Unbanned successfully"
msgstr "解封成功"

#: src/views/apps/fail2ban/IndexView.vue:239
msgid "Save Whitelist"
msgstr "保存白名单"

#: src/views/apps/fail2ban/IndexView.vue:248
#: src/views/apps/fail2ban/IndexView.vue:294
#: src/views/apps/fail2ban/IndexView.vue:298
msgid "Add Rule"
msgstr "添加规则"

#: src/views/apps/fail2ban/IndexView.vue:255
msgid "IP Whitelist"
msgstr "IP 白名单"

#: src/views/apps/fail2ban/IndexView.vue:260
msgid "IP whitelist, separated by commas"
msgstr "IP 白名单，用逗号分隔"

#: src/views/apps/fail2ban/IndexView.vue:265
msgid "Rule Management"
msgstr "规则管理"

#: src/views/apps/fail2ban/IndexView.vue:266
msgid "Rule List"
msgstr "规则列表"

#: src/views/apps/fail2ban/IndexView.vue:304
msgid "If an IP exceeds the maximum retries within the find time (seconds), it will be banned for the ban time (seconds)"
msgstr "如果一个 IP 在查找时间（秒）内超过最大重试次数，它将被禁止访问指定的禁止时间（秒）"

#: src/views/apps/fail2ban/IndexView.vue:311
msgid "Protected ports are automatically obtained. If you modify the port corresponding to a rule, please delete and re-add the rule, otherwise protection may not be effective"
msgstr "受保护的端口会自动获取。如果您修改了某个规则对应的端口，请删除并重新添加该规则，否则保护可能无效"

#: src/views/apps/fail2ban/IndexView.vue:318
#: src/views/cert/CertView.vue:87
#: src/views/cert/DnsView.vue:38
#: src/views/cert/ObtainModal.vue:58
#: src/views/database/CreateServerModal.vue:63
#: src/views/database/DatabaseList.vue:12
#: src/views/database/ServerList.vue:17
#: src/views/database/UserList.vue:17
msgid "Type"
msgstr "类型"

#: src/views/apps/fail2ban/IndexView.vue:323
#: src/views/apps/fail2ban/IndexView.vue:354
msgid "Service"
msgstr "服务"

#: src/views/apps/fail2ban/IndexView.vue:328
#: src/views/apps/fail2ban/IndexView.vue:332
#: src/views/task/CreateModal.vue:144
#: src/views/task/CreateModal.vue:149
msgid "Select Website"
msgstr "选择网站"

#: src/views/apps/fail2ban/IndexView.vue:335
msgid "Protection Mode"
msgstr "保护模式"

#: src/views/apps/fail2ban/IndexView.vue:340
#: src/views/apps/pureftpd/IndexView.vue:39
msgid "Path"
msgstr "路径"

#: src/views/apps/fail2ban/IndexView.vue:347
#: src/views/apps/fail2ban/IndexView.vue:351
msgid "Protection Path"
msgstr "保护路径"

#: src/views/apps/fail2ban/IndexView.vue:379
#: src/views/apps/fail2ban/IndexView.vue:383
msgid "View Rule"
msgstr "查看规则"

#: src/views/apps/fail2ban/IndexView.vue:387
msgid "Rule Information"
msgstr "规则信息"

#: src/views/apps/fail2ban/IndexView.vue:390
msgid "Currently Banned"
msgstr "当前已封禁"

#: src/views/apps/fail2ban/IndexView.vue:394
msgid "Total Bans"
msgstr "总封禁次数"

#: src/views/apps/fail2ban/IndexView.vue:399
msgid "Ban List"
msgstr "封禁列表"

#: src/views/apps/gitea/IndexView.vue:48
msgid "This modifies the Gitea configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 Gitea 配置文件。如果您不理解每个参数的含义，请不要随意修改！"

#: src/views/apps/memcached/IndexView.vue:18
#: src/views/apps/mysql/IndexView.vue:31
#: src/views/apps/nginx/IndexView.vue:28
#: src/views/apps/php/PhpView.vue:124
#: src/views/apps/postgresql/IndexView.vue:31
#: src/views/apps/redis/IndexView.vue:25
msgid "Property"
msgstr "属性"

#: src/views/apps/memcached/IndexView.vue:25
#: src/views/apps/mysql/IndexView.vue:38
#: src/views/apps/nginx/IndexView.vue:35
#: src/views/apps/php/PhpView.vue:131
#: src/views/apps/postgresql/IndexView.vue:38
#: src/views/apps/redis/IndexView.vue:32
msgid "Current Value"
msgstr "当前值"

#: src/views/apps/memcached/IndexView.vue:66
msgid "Service Configuration"
msgstr "服务配置"

#: src/views/apps/memcached/IndexView.vue:82
#: src/views/apps/mysql/IndexView.vue:141
#: src/views/apps/nginx/IndexView.vue:104
#: src/views/apps/php/PhpView.vue:289
#: src/views/apps/postgresql/IndexView.vue:137
#: src/views/apps/redis/IndexView.vue:86
#: src/views/dashboard/IndexView.vue:462
msgid "Load Status"
msgstr "负载状态"

#: src/views/apps/minio/IndexView.vue:39
#: src/views/container/ComposeView.vue:330
#: src/views/container/ComposeView.vue:360
#: src/views/container/ContainerCreate.vue:352
msgid "Environment Variables"
msgstr "环境变量"

#: src/views/apps/minio/IndexView.vue:43
msgid "This is modifying the Minio environment variable file /etc/default/minio. If you do not understand the meaning of each parameter, please do not modify it arbitrarily!"
msgstr "这将修改 Minio 环境变量文件 /etc/default/minio。如果您不了解每个参数的含义，请不要随意修改！"

#: src/views/apps/mysql/IndexView.vue:53
#: src/views/apps/mysql/IndexView.vue:59
#: src/views/apps/nginx/IndexView.vue:50
#: src/views/apps/php/PhpView.vue:158
#: src/views/apps/php/PhpView.vue:164
#: src/views/apps/postgresql/IndexView.vue:57
#: src/views/apps/supervisor/IndexView.vue:233
#: src/views/website/EditView.vue:160
msgid "Cleared successfully"
msgstr "清除成功"

#: src/views/apps/mysql/IndexView.vue:65
#: src/views/apps/pureftpd/IndexView.vue:140
#: src/views/database/DatabaseList.vue:129
#: src/views/database/ServerList.vue:248
#: src/views/database/UpdateServerModal.vue:21
#: src/views/database/UpdateUserModal.vue:18
#: src/views/database/UserList.vue:222
#: src/views/file/PermissionModal.vue:29
#: src/views/setting/SettingUser.vue:156
#: src/views/task/CronView.vue:180
#: src/views/task/CronView.vue:207
#: src/views/website/IndexView.vue:252
#: src/views/website/IndexView.vue:278
msgid "Modified successfully"
msgstr "修改成功"

#: src/views/apps/mysql/IndexView.vue:88
#: src/views/apps/nginx/IndexView.vue:74
#: src/views/apps/postgresql/IndexView.vue:84
#: src/views/apps/supervisor/IndexView.vue:326
msgid "Clear Log"
msgstr "清除日志"

#: src/views/apps/mysql/IndexView.vue:97
#: src/views/apps/php/PhpView.vue:221
msgid "Clear Slow Log"
msgstr "清除慢查询日志"

#: src/views/apps/mysql/IndexView.vue:104
#: src/views/toolbox/SystemView.vue:205
#: src/views/toolbox/SystemView.vue:207
msgid "Root Password"
msgstr "Root 密码"

#: src/views/apps/mysql/IndexView.vue:112
msgid "Save Changes"
msgstr "保存更改"

#: src/views/apps/mysql/IndexView.vue:122
msgid "This modifies the MySQL main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 MySQL 主配置文件。如果您不了解每个参数的含义，请不要随意修改！"

#: src/views/apps/mysql/IndexView.vue:154
msgid "Slow Query Log"
msgstr "慢查询日志"

#: src/views/apps/nginx/IndexView.vue:85
msgid "This modifies the OpenResty main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 OpenResty 主配置文件。如果您不理解每个参数的含义，请不要随意修改！"

#: src/views/apps/nginx/IndexView.vue:117
#: src/views/apps/php/PhpView.vue:302
msgid "Error Logs"
msgstr "错误日志"

#: src/views/apps/php/PhpView.vue:43
msgid "Extension Name"
msgstr "扩展名称"

#: src/views/apps/php/PhpView.vue:71
msgid "Are you sure you want to install %{ name }?"
msgstr "您确定要安装 %{ name } 吗？"

#: src/views/apps/php/PhpView.vue:97
msgid "Are you sure you want to uninstall %{ name }?"
msgstr "您确定要卸载 %{ name } 吗？"

#: src/views/apps/php/PhpView.vue:140
msgid "Set successfully"
msgstr "设置成功"

#: src/views/apps/php/PhpView.vue:170
#: src/views/apps/php/PhpView.vue:176
msgid "Task submitted, please check progress in background tasks"
msgstr "任务已提交，请在后台任务中查看进度"

#: src/views/apps/php/PhpView.vue:185
msgid "Set as CLI Default Version"
msgstr "设置为 CLI 默认版本"

#: src/views/apps/php/PhpView.vue:212
msgid "Clear Error Log"
msgstr "清除错误日志"

#: src/views/apps/php/PhpView.vue:228
msgid "Extension Management"
msgstr "扩展管理"

#: src/views/apps/php/PhpView.vue:241
#: src/views/apps/postgresql/IndexView.vue:91
#: src/views/apps/redis/IndexView.vue:63
#: src/views/apps/rsync/IndexView.vue:241
#: src/views/apps/supervisor/IndexView.vue:357
msgid "Main Configuration"
msgstr "主要配置"

#: src/views/apps/php/PhpView.vue:245
msgid "This modifies the PHP %{ version } main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 PHP %{ version } 的主要配置文件。如果您不了解每个参数的含义，请不要随意修改！"

#: src/views/apps/php/PhpView.vue:265
msgid "FPM Configuration"
msgstr "FPM 配置"

#: src/views/apps/php/PhpView.vue:269
msgid "This modifies the PHP %{ version } FPM configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 PHP %{ version } 的 FPM 配置文件。如果您不了解每个参数的含义，请不要随意修改！"

#: src/views/apps/php/PhpView.vue:305
#: src/views/apps/postgresql/IndexView.vue:150
msgid "Slow Logs"
msgstr "慢日志"

#: src/views/apps/phpmyadmin/IndexView.vue:73
msgid "Access Information"
msgstr "访问信息"

#: src/views/apps/phpmyadmin/IndexView.vue:75
msgid "Access URL:"
msgstr "访问 URL："

#: src/views/apps/phpmyadmin/IndexView.vue:78
msgid "Modify Port"
msgstr "修改端口"

#: src/views/apps/phpmyadmin/IndexView.vue:80
msgid "Modify phpMyAdmin access port"
msgstr "修改 phpMyAdmin 访问端口"

#: src/views/apps/phpmyadmin/IndexView.vue:88
msgid "This modifies the OpenResty configuration file for phpMyAdmin. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 phpMyAdmin 的 OpenResty 配置文件。如果您不了解每个参数的含义，请不要随意修改！"

#: src/views/apps/podman/IndexView.vue:64
msgid "Podman is a daemonless container management tool. Being in a stopped state is normal and does not affect usage!"
msgstr "Podman 是一个无守护进程的容器管理工具。处于停止状态是正常的，不影响使用！"

#: src/views/apps/podman/IndexView.vue:72
msgid "Registry Configuration"
msgstr "注册表配置"

#: src/views/apps/podman/IndexView.vue:76
msgid "This modifies the Podman registry configuration file (/etc/containers/registries.conf)"
msgstr "这会修改 Podman 注册表配置文件 (/etc/containers/registries.conf)"

#: src/views/apps/podman/IndexView.vue:95
msgid "Storage Configuration"
msgstr "存储配置"

#: src/views/apps/podman/IndexView.vue:99
msgid "This modifies the Podman storage configuration file (/etc/containers/storage.conf)"
msgstr "这会修改 Podman 存储配置文件 (/etc/containers/storage.conf)"

#: src/views/apps/postgresql/IndexView.vue:95
msgid "This modifies the PostgreSQL main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 PostgreSQL 主配置文件。如果您不了解每个参数的含义，请不要随意修改！"

#: src/views/apps/postgresql/IndexView.vue:114
msgid "User Configuration"
msgstr "用户配置"

#: src/views/apps/postgresql/IndexView.vue:118
msgid "This modifies the PostgreSQL user configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 PostgreSQL 用户配置文件。如果您不了解每个参数的含义，请不要随意修改！"

#: src/views/apps/pureftpd/IndexView.vue:32
#: src/views/apps/pureftpd/IndexView.vue:221
#: src/views/container/ImageView.vue:198
#: src/views/database/CreateDatabaseModal.vue:93
#: src/views/database/CreateServerModal.vue:94
#: src/views/database/CreateUserModal.vue:70
#: src/views/database/ServerList.vue:47
#: src/views/database/UpdateServerModal.vue:86
#: src/views/database/UserList.vue:40
#: src/views/login/IndexView.vue:133
#: src/views/setting/CreateModal.vue:40
#: src/views/setting/SettingUser.vue:19
#: src/views/ssh/CreateModal.vue:83
#: src/views/ssh/UpdateModal.vue:89
msgid "Username"
msgstr "用户名"

#: src/views/apps/pureftpd/IndexView.vue:76
msgid "Are you sure you want to delete user %{ username }?"
msgstr "您确定要删除用户 %{ username } 吗？"

#: src/views/apps/pureftpd/IndexView.vue:171
msgid "Add User"
msgstr "添加用户"

#: src/views/apps/pureftpd/IndexView.vue:178
msgid "Port Settings"
msgstr "端口设置"

#: src/views/apps/pureftpd/IndexView.vue:180
msgid "Modify Pure-Ftpd listening port"
msgstr "修改 Pure-Ftpd 监听端口"

#: src/views/apps/pureftpd/IndexView.vue:184
msgid "User Management"
msgstr "用户管理"

#: src/views/apps/pureftpd/IndexView.vue:208
msgid "Run Log"
msgstr "运行日志"

#: src/views/apps/pureftpd/IndexView.vue:213
#: src/views/apps/pureftpd/IndexView.vue:217
#: src/views/database/CreateDatabaseModal.vue:78
#: src/views/database/CreateUserModal.vue:54
#: src/views/database/IndexView.vue:36
#: src/views/setting/CreateModal.vue:32
#: src/views/setting/IndexView.vue:69
msgid "Create User"
msgstr "创建用户"

#: src/views/apps/pureftpd/IndexView.vue:226
#: src/views/container/ImageView.vue:203
#: src/views/database/CreateDatabaseModal.vue:98
#: src/views/database/CreateUserModal.vue:75
msgid "Enter username"
msgstr "输入用户名"

#: src/views/apps/pureftpd/IndexView.vue:229
#: src/views/apps/pureftpd/IndexView.vue:260
#: src/views/apps/rsync/IndexView.vue:304
#: src/views/apps/rsync/IndexView.vue:358
#: src/views/container/ImageView.vue:206
#: src/views/database/CreateDatabaseModal.vue:101
#: src/views/database/CreateServerModal.vue:102
#: src/views/database/CreateUserModal.vue:78
#: src/views/database/ServerList.vue:56
#: src/views/database/UpdateServerModal.vue:94
#: src/views/database/UpdateUserModal.vue:49
#: src/views/database/UserList.vue:50
#: src/views/login/IndexView.vue:142
#: src/views/setting/CreateModal.vue:47
#: src/views/setting/PasswordModal.vue:34
#: src/views/ssh/CreateModal.vue:77
#: src/views/ssh/CreateModal.vue:86
#: src/views/ssh/UpdateModal.vue:83
#: src/views/ssh/UpdateModal.vue:92
msgid "Password"
msgstr "密码"

#: src/views/apps/pureftpd/IndexView.vue:236
#: src/views/apps/pureftpd/IndexView.vue:266
msgid "It is recommended to use the generator to generate a random password"
msgstr "建议使用生成器生成随机密码"

#: src/views/apps/pureftpd/IndexView.vue:240
#: src/views/apps/rsync/IndexView.vue:47
#: src/views/apps/rsync/IndexView.vue:288
#: src/views/apps/rsync/IndexView.vue:342
#: src/views/container/ComposeView.vue:38
#: src/views/website/IndexView.vue:44
#: src/views/website/IndexView.vue:517
msgid "Directory"
msgstr "目录"

#: src/views/apps/pureftpd/IndexView.vue:245
msgid "Enter the directory authorized to the user"
msgstr "输入授权给用户的目录"

#: src/views/apps/redis/IndexView.vue:67
msgid "This modifies the Redis main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 Redis 主配置文件。如果您不了解每个参数的含义，请不要随意修改！"

#: src/views/apps/rsync/IndexView.vue:54
#: src/views/apps/rsync/IndexView.vue:296
#: src/views/apps/rsync/IndexView.vue:350
#: src/views/database/IndexView.vue:48
#: src/views/setting/IndexView.vue:79
#: src/views/task/SystemView.vue:37
msgid "User"
msgstr "用户"

#: src/views/apps/rsync/IndexView.vue:61
#: src/views/apps/rsync/IndexView.vue:312
#: src/views/apps/rsync/IndexView.vue:367
#: src/views/database/CreateDatabaseModal.vue:110
#: src/views/database/CreateServerModal.vue:73
#: src/views/database/ServerList.vue:87
#: src/views/database/UpdateServerModal.vue:65
#: src/views/database/UserList.vue:81
#: src/views/ssh/CreateModal.vue:62
#: src/views/ssh/UpdateModal.vue:68
#: src/views/toolbox/SystemView.vue:156
msgid "Host"
msgstr "主机"

#: src/views/apps/rsync/IndexView.vue:67
#: src/views/apps/rsync/IndexView.vue:320
#: src/views/apps/rsync/IndexView.vue:375
#: src/views/database/CreateServerModal.vue:111
#: src/views/database/CreateUserModal.vue:109
#: src/views/database/DatabaseList.vue:57
#: src/views/database/ServerList.vue:97
#: src/views/database/UpdateServerModal.vue:103
#: src/views/database/UpdateUserModal.vue:64
#: src/views/database/UserList.vue:114
msgid "Comment"
msgstr "注释"

#: src/views/apps/rsync/IndexView.vue:83
#: src/views/apps/supervisor/IndexView.vue:104
msgid "Configure"
msgstr "配置"

#: src/views/apps/rsync/IndexView.vue:94
msgid "Are you sure you want to delete module %{ name }?"
msgstr "确定要删除模块 %{ name } 吗？"

#: src/views/apps/rsync/IndexView.vue:210
#: src/views/apps/rsync/IndexView.vue:272
msgid "Add Module"
msgstr "添加模块"

#: src/views/apps/rsync/IndexView.vue:217
msgid "Module Management"
msgstr "模块管理"

#: src/views/apps/rsync/IndexView.vue:245
msgid "This modifies the Rsync main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 Rsync 主配置文件。如果您不理解每个参数的含义，请不要随意修改！"

#: src/views/apps/rsync/IndexView.vue:285
#: src/views/apps/supervisor/IndexView.vue:404
msgid "Name cannot contain Chinese characters"
msgstr "名称不能包含中文字符"

#: src/views/apps/rsync/IndexView.vue:293
#: src/views/apps/rsync/IndexView.vue:347
msgid "Please enter absolute path"
msgstr "请输入绝对路径"

#: src/views/apps/rsync/IndexView.vue:301
#: src/views/apps/rsync/IndexView.vue:355
msgid "Enter module username"
msgstr "输入模块用户名"

#: src/views/apps/rsync/IndexView.vue:309
#: src/views/apps/rsync/IndexView.vue:364
msgid "Enter module password"
msgstr "输入模块密码"

#: src/views/apps/rsync/IndexView.vue:317
#: src/views/apps/rsync/IndexView.vue:372
msgid "Enter allowed hosts, separate multiple hosts with spaces"
msgstr "输入允许的主机，多个主机用空格分隔"

#: src/views/apps/rsync/IndexView.vue:325
#: src/views/apps/rsync/IndexView.vue:380
msgid "Enter comments"
msgstr "输入注释"

#: src/views/apps/rsync/IndexView.vue:334
msgid "Module Configuration"
msgstr "模块配置"

#: src/views/apps/s3fs/IndexView.vue:25
msgid "Mount Path"
msgstr "挂载路径"

#: src/views/apps/s3fs/IndexView.vue:46
msgid "Are you sure you want to delete mount %{ path }?"
msgstr "您确定要删除挂载 %{ path } 吗？"

#: src/views/apps/s3fs/IndexView.vue:58
msgid "Unmount"
msgstr "卸载"

#: src/views/apps/s3fs/IndexView.vue:105
#: src/views/apps/s3fs/IndexView.vue:131
#: src/views/apps/s3fs/IndexView.vue:135
msgid "Add Mount"
msgstr "添加挂载"

#: src/views/apps/s3fs/IndexView.vue:144
msgid "Enter Bucket name (COS format: xxxx-ID)"
msgstr "输入存储桶名称（COS 格式：xxxx-ID）"

#: src/views/apps/s3fs/IndexView.vue:152
msgid "Enter AK key"
msgstr "输入 AK 密钥"

#: src/views/apps/s3fs/IndexView.vue:160
msgid "Enter SK key"
msgstr "输入 SK 密钥"

#: src/views/apps/s3fs/IndexView.vue:163
msgid "Region Endpoint"
msgstr "区域端点"

#: src/views/apps/s3fs/IndexView.vue:169
msgid "Enter complete URL of region endpoint (e.g., https://oss-cn-beijing.aliyuncs.com)"
msgstr "输入完整的区域端点 URL（例如：https://oss-cn-beijing.aliyuncs.com）"

#: src/views/apps/s3fs/IndexView.vue:175
msgid "Mount Directory"
msgstr "挂载目录"

#: src/views/apps/s3fs/IndexView.vue:180
msgid "Enter mount directory (e.g., /oss)"
msgstr "输入挂载目录（例如，/oss）"

#: src/views/apps/supervisor/IndexView.vue:69
msgid "Uptime"
msgstr "运行时间"

#: src/views/apps/supervisor/IndexView.vue:132
msgid "Are you sure you want to stop process %{ name }?"
msgstr "您确定要停止进程 %{ name } 吗？"

#: src/views/apps/supervisor/IndexView.vue:161
msgid "Are you sure you want to restart process %{ name }?"
msgstr "您确定要重启进程 %{ name } 吗？"

#: src/views/apps/supervisor/IndexView.vue:189
msgid "Are you sure you want to delete process %{ name }?"
msgstr "您确定要删除进程 %{ name } 吗？"

#: src/views/apps/supervisor/IndexView.vue:322
#: src/views/apps/supervisor/IndexView.vue:391
msgid "Add Process"
msgstr "添加进程"

#: src/views/apps/supervisor/IndexView.vue:333
msgid "Process Management"
msgstr "进程管理"

#: src/views/apps/supervisor/IndexView.vue:361
msgid "This modifies the Supervisor main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr "这将修改 Supervisor 主配置文件。如果您不了解每个参数的含义，请不要随意修改！"

#: src/views/apps/supervisor/IndexView.vue:383
msgid "Daemon Logs"
msgstr "守护进程日志"

#: src/views/apps/supervisor/IndexView.vue:407
msgid "Start Command"
msgstr "启动命令"

#: src/views/apps/supervisor/IndexView.vue:412
msgid "Please enter absolute path for files in start command"
msgstr "请在启动命令中输入文件的绝对路径"

#: src/views/apps/supervisor/IndexView.vue:415
msgid "Working Directory"
msgstr "工作目录"

#: src/views/apps/supervisor/IndexView.vue:420
msgid "Please enter absolute path for working directory"
msgstr "请输入工作目录的绝对路径"

#: src/views/apps/supervisor/IndexView.vue:423
msgid "Run As User"
msgstr "运行用户"

#: src/views/apps/supervisor/IndexView.vue:428
msgid "Usually www is sufficient"
msgstr "通常使用 www 即可"

#: src/views/apps/supervisor/IndexView.vue:431
msgid "Number of Processes"
msgstr "进程数量"

#: src/views/apps/supervisor/IndexView.vue:441
msgid "Process Configuration"
msgstr "进程配置"

#: src/views/backup/ListView.vue:36
msgid "Filename"
msgstr "文件名"

#: src/views/backup/ListView.vue:49
#: src/views/database/ServerList.vue:126
#: src/views/database/UserList.vue:143
msgid "Update Date"
msgstr "更新日期"

#: src/views/backup/ListView.vue:76
msgid "Restore"
msgstr "恢复"

#: src/views/backup/ListView.vue:87
msgid "Are you sure you want to delete this backup?"
msgstr "您确定要删除此备份吗？"

#: src/views/backup/ListView.vue:131
msgid "Restoring..."
msgstr "正在恢复……"

#: src/views/backup/ListView.vue:138
msgid "Restored successfully"
msgstr "恢复成功"

#: src/views/backup/ListView.vue:182
#: src/views/backup/ListView.vue:212
msgid "Create Backup"
msgstr "创建备份"

#: src/views/backup/ListView.vue:185
#: src/views/backup/UploadModal.vue:39
msgid "Upload Backup"
msgstr "上传备份"

#: src/views/backup/ListView.vue:224
#: src/views/backup/ListView.vue:261
msgid "Select website"
msgstr "选择网站"

#: src/views/backup/ListView.vue:227
#: src/views/database/CreateDatabaseModal.vue:70
#: src/views/database/DatabaseList.vue:35
#: src/views/task/CreateModal.vue:154
#: src/views/task/CreateModal.vue:156
#: src/views/website/IndexView.vue:481
#: src/views/website/IndexView.vue:486
msgid "Database Name"
msgstr "数据库名称"

#: src/views/backup/ListView.vue:232
#: src/views/database/CreateDatabaseModal.vue:75
#: src/views/database/CreateUserModal.vue:106
#: src/views/database/UpdateUserModal.vue:61
msgid "Enter database name"
msgstr "输入数据库名称"

#: src/views/backup/ListView.vue:235
#: src/views/task/CreateModal.vue:158
#: src/views/task/CreateModal.vue:161
msgid "Save Directory"
msgstr "保存目录"

#: src/views/backup/ListView.vue:240
msgid "Leave empty to use default path"
msgstr "留空以使用默认路径"

#: src/views/backup/ListView.vue:249
msgid "Restore Backup"
msgstr "恢复备份"

#: src/views/backup/UploadModal.vue:20
msgid "Upload %{ filename } successfully"
msgstr "上传 %{ filename } 成功"

#: src/views/backup/UploadModal.vue:51
#: src/views/file/UploadModal.vue:50
msgid "Click or drag files to this area to upload"
msgstr "点击或将文件拖到此区域上传"

#: src/views/backup/UploadModal.vue:53
msgid "For large files, it is recommended to use SFTP or other methods to upload"
msgstr "对于大文件，建议使用 SFTP 或其他方法上传"

#: src/views/cert/AccountView.vue:44
#: src/views/cert/AccountView.vue:243
#: src/views/cert/CreateAccountModal.vue:100
#: src/views/setting/CreateModal.vue:56
#: src/views/setting/SettingUser.vue:26
msgid "Email"
msgstr "邮箱"

#: src/views/cert/AccountView.vue:72
#: src/views/cert/AccountView.vue:235
#: src/views/cert/CertView.vue:458
#: src/views/cert/CreateAccountModal.vue:92
#: src/views/cert/CreateCertModal.vue:82
msgid "Key Type"
msgstr "密钥类型"

#: src/views/cert/AccountView.vue:101
#: src/views/cert/CertView.vue:288
#: src/views/cert/DnsView.vue:85
#: src/views/database/ServerList.vue:184
#: src/views/database/UserList.vue:169
#: src/views/file/PermissionModal.vue:123
#: src/views/setting/TokenModal.vue:69
#: src/views/toolbox/SystemView.vue:118
msgid "Modify"
msgstr "修改"

#: src/views/cert/AccountView.vue:109
#: src/views/cert/CertView.vue:297
#: src/views/cert/DnsView.vue:94
msgid "Deletion successful"
msgstr "删除成功"

#: src/views/cert/AccountView.vue:116
msgid "Are you sure you want to delete the account?"
msgstr "您确定要删除该账户吗？"

#: src/views/cert/AccountView.vue:150
#: src/views/cert/CreateAccountModal.vue:39
msgid "Registering account with CA, please wait patiently"
msgstr "正在向 CA 注册账户，请耐心等待"

#: src/views/cert/AccountView.vue:162
#: src/views/cert/CertView.vue:348
#: src/views/cert/CertView.vue:365
#: src/views/cert/DnsView.vue:139
#: src/views/container/ComposeView.vue:264
msgid "Update successful"
msgstr "更新成功"

#: src/views/cert/AccountView.vue:207
msgid "Modify Account"
msgstr "修改账户"

#: src/views/cert/AccountView.vue:215
#: src/views/cert/CreateAccountModal.vue:72
msgid "Google and SSL.com require obtaining KID and HMAC from their official websites first"
msgstr "Google 和 SSL.com 需要先从其官网获取 KID 和 HMAC"

#: src/views/cert/AccountView.vue:221
#: src/views/cert/CreateAccountModal.vue:78
msgid "Google is not accessible in mainland China, other CAs depend on network conditions, recommend using Let's Encrypt"
msgstr "Google 在中国大陆无法访问，其他 CA 取决于网络条件，建议使用 Let's Encrypt"

#: src/views/cert/AccountView.vue:227
#: src/views/cert/CreateAccountModal.vue:84
msgid "CA"
msgstr "CA"

#: src/views/cert/AccountView.vue:230
#: src/views/cert/CreateAccountModal.vue:87
msgid "Select CA"
msgstr "选择 CA"

#: src/views/cert/AccountView.vue:238
#: src/views/cert/CertView.vue:461
#: src/views/cert/CreateAccountModal.vue:95
#: src/views/cert/CreateCertModal.vue:85
msgid "Select key type"
msgstr "选择密钥类型"

#: src/views/cert/AccountView.vue:248
#: src/views/cert/CreateAccountModal.vue:105
msgid "Enter email address"
msgstr "输入电子邮件地址"

#: src/views/cert/AccountView.vue:256
#: src/views/cert/CreateAccountModal.vue:113
msgid "Enter KID"
msgstr "输入 KID"

#: src/views/cert/AccountView.vue:264
#: src/views/cert/CreateAccountModal.vue:121
msgid "Enter HMAC"
msgstr "输入 HMAC"

#: src/views/cert/CertView.vue:64
#: src/views/cert/CertView.vue:450
#: src/views/cert/CreateCertModal.vue:74
#: src/views/cert/ObtainModal.vue:57
#: src/views/website/EditView.vue:232
#: src/views/website/IndexView.vue:425
msgid "Domain"
msgstr "域名"

#: src/views/cert/CertView.vue:70
#: src/views/cert/CertView.vue:124
#: src/views/cert/CertView.vue:135
#: src/views/cert/CertView.vue:154
#: src/views/container/ContainerCreate.vue:55
#: src/views/database/ServerList.vue:52
#: src/views/database/ServerList.vue:67
#: src/views/database/UserList.vue:46
#: src/views/database/UserList.vue:86
#: src/views/firewall/ForwardView.vue:26
#: src/views/firewall/IpRuleView.vue:26
#: src/views/firewall/IpRuleView.vue:43
#: src/views/firewall/RuleView.vue:26
#: src/views/firewall/RuleView.vue:43
msgid "None"
msgstr "无"

#: src/views/cert/CertView.vue:109
#: src/views/file/ToolBar.vue:221
#: src/views/file/UploadModal.vue:38
msgid "Upload"
msgstr "上传"

#: src/views/cert/CertView.vue:117
msgid "Associated Account"
msgstr "关联账户"

#: src/views/cert/CertView.vue:130
#: src/views/website/EditView.vue:306
msgid "Issuer"
msgstr "颁发者"

#: src/views/cert/CertView.vue:139
#: src/views/setting/TokenModal.vue:43
#: src/views/setting/TokenModal.vue:258
#: src/views/setting/TokenModal.vue:291
msgid "Expiration Time"
msgstr "过期时间"

#: src/views/cert/CertView.vue:167
msgid "Auto Renew"
msgstr "自动续期"

#: src/views/cert/CertView.vue:200
#: src/views/cert/ObtainModal.vue:78
msgid "Issue"
msgstr "签发"

#: src/views/cert/CertView.vue:219
msgid "Deploy"
msgstr "部署"

#: src/views/cert/CertView.vue:231
#: src/views/cert/ObtainModal.vue:24
#: src/views/cert/ObtainModal.vue:81
#: src/views/website/EditView.vue:133
msgid "Please wait..."
msgstr "请稍候……"

#: src/views/cert/CertView.vue:237
msgid "Renewal successful"
msgstr "续期成功"

#: src/views/cert/CertView.vue:245
msgid "Renew"
msgstr "续期"

#: src/views/cert/CertView.vue:303
msgid "Are you sure you want to delete the certificate?"
msgstr "您确定要删除证书吗？"

#: src/views/cert/CertView.vue:389
msgid "Deployment successful"
msgstr "部署成功"

#: src/views/cert/CertView.vue:435
msgid "Modify Certificate"
msgstr "修改证书"

#: src/views/cert/CertView.vue:444
msgid "You can automatically issue and deploy certificates by selecting any website/DNS, or manually enter domain names and set DNS resolution to issue certificates, or fill in deployment scripts to automatically deploy certificates."
msgstr "您可以通过选择任何网站 / DNS 自动颁发和部署证书，或手动输入域名并设置 DNS 解析以颁发证书，或填写部署脚本以自动部署证书。"

#: src/views/cert/CertView.vue:469
#: src/views/cert/CreateCertModal.vue:93
msgid "Select website for certificate deployment"
msgstr "选择要部署证书的网站"

#: src/views/cert/CertView.vue:477
#: src/views/cert/CreateCertModal.vue:98
msgid "Account"
msgstr "账户"

#: src/views/cert/CertView.vue:481
#: src/views/cert/CreateCertModal.vue:101
msgid "Select account for certificate issuance"
msgstr "选择用于证书颁发的账户"

#: src/views/cert/CertView.vue:486
#: src/views/cert/CreateCertModal.vue:106
#: src/views/cert/CreateDnsModal.vue:59
#: src/views/cert/DnsView.vue:196
msgid "DNS"
msgstr "DNS"

#: src/views/cert/CertView.vue:489
#: src/views/cert/CreateCertModal.vue:109
msgid "Select DNS for certificate issuance"
msgstr "选择用于证书颁发的 DNS"

#: src/views/cert/CertView.vue:502
#: src/views/cert/UploadCertModal.vue:42
#: src/views/website/EditView.vue:359
msgid "Enter the content of the PEM certificate file"
msgstr "输入 PEM 证书文件的内容"

#: src/views/cert/CertView.vue:509
#: src/views/cert/CertView.vue:585
#: src/views/cert/UploadCertModal.vue:46
#: src/views/setting/SettingSafe.vue:61
#: src/views/ssh/CreateModal.vue:78
#: src/views/ssh/CreateModal.vue:89
#: src/views/ssh/UpdateModal.vue:84
#: src/views/ssh/UpdateModal.vue:95
#: src/views/website/EditView.vue:363
msgid "Private Key"
msgstr "私钥"

#: src/views/cert/CertView.vue:514
#: src/views/cert/UploadCertModal.vue:50
#: src/views/website/EditView.vue:367
msgid "Enter the content of the KEY private key file"
msgstr "输入 KEY 私钥文件的内容"

#: src/views/cert/CertView.vue:521
msgid "Deployment Script"
msgstr "部署脚本"

#: src/views/cert/CertView.vue:527
msgid "The {cert} and {key} in the script will be replaced with the certificate and private key content"
msgstr "脚本中的 {cert} 和 {key} 将被替换为证书和私钥内容"

#: src/views/cert/CertView.vue:541
msgid "Deploy Certificate"
msgstr "部署证书"

#: src/views/cert/CertView.vue:552
msgid "Select websites to deploy the certificate"
msgstr "选择要部署证书的网站"

#: src/views/cert/CertView.vue:565
msgid "View Certificate"
msgstr "查看证书"

#: src/views/cert/CreateAccountModal.vue:64
#: src/views/cert/IndexView.vue:106
msgid "Create Account"
msgstr "创建账户"

#: src/views/cert/CreateCertModal.vue:59
#: src/views/cert/IndexView.vue:102
msgid "Create Certificate"
msgstr "创建证书"

#: src/views/cert/CreateCertModal.vue:68
msgid "You can automatically issue and deploy certificates by selecting either Website or DNS, or you can manually enter domain names and set up DNS resolution to issue certificates"
msgstr "您可以通过选择网站或 DNS 来自动颁发和部署证书，或者手动输入域名并设置 DNS 解析以颁发证书"

#: src/views/cert/CreateDnsModal.vue:44
#: src/views/cert/IndexView.vue:110
msgid "Create DNS"
msgstr "创建 DNS"

#: src/views/cert/CreateDnsModal.vue:52
msgid "Comment Name"
msgstr "备注名称"

#: src/views/cert/CreateDnsModal.vue:56
msgid "Enter comment name"
msgstr "输入备注名称"

#: src/views/cert/CreateDnsModal.vue:62
#: src/views/cert/DnsView.vue:199
msgid "Select DNS"
msgstr "选择 DNS"

#: src/views/cert/CreateDnsModal.vue:71
#: src/views/cert/DnsView.vue:208
msgid "Enter Aliyun Access Key"
msgstr "输入阿里云 Access Key"

#: src/views/cert/CreateDnsModal.vue:78
#: src/views/cert/DnsView.vue:215
msgid "Enter Aliyun Secret Key"
msgstr "输入阿里云 Secret Key"

#: src/views/cert/CreateDnsModal.vue:85
#: src/views/cert/DnsView.vue:222
msgid "Enter Tencent Cloud SecretId"
msgstr "输入腾讯云 SecretId"

#: src/views/cert/CreateDnsModal.vue:92
#: src/views/cert/DnsView.vue:229
msgid "Enter Tencent Cloud SecretKey"
msgstr "输入腾讯云 SecretKey"

#: src/views/cert/CreateDnsModal.vue:99
#: src/views/cert/DnsView.vue:236
msgid "Enter Huawei Cloud AccessKeyId"
msgstr "输入华为云 AccessKeyId"

#: src/views/cert/CreateDnsModal.vue:106
#: src/views/cert/DnsView.vue:243
msgid "Enter Huawei Cloud SecretAccessKey"
msgstr "输入华为云 SecretAccessKey"

#: src/views/cert/CreateDnsModal.vue:113
#: src/views/cert/DnsView.vue:250
msgid "Enter West.cn Username"
msgstr "输入西部数码用户名"

#: src/views/cert/CreateDnsModal.vue:120
#: src/views/cert/DnsView.vue:257
msgid "Enter West.cn API Password"
msgstr "输入西部数码 API 密码"

#: src/views/cert/CreateDnsModal.vue:127
#: src/views/cert/DnsView.vue:264
msgid "Enter Cloudflare API Key"
msgstr "输入 Cloudflare API 密钥"

#: src/views/cert/CreateDnsModal.vue:134
#: src/views/cert/DnsView.vue:271
msgid "Enter GoDaddy Token"
msgstr "输入 GoDaddy 令牌"

#: src/views/cert/CreateDnsModal.vue:141
#: src/views/cert/DnsView.vue:278
msgid "Enter G-Core API Key"
msgstr "输入 G-Core API 密钥"

#: src/views/cert/CreateDnsModal.vue:148
#: src/views/cert/DnsView.vue:285
msgid "Enter Porkbun API Key"
msgstr "输入 Porkbun API 密钥"

#: src/views/cert/CreateDnsModal.vue:155
#: src/views/cert/DnsView.vue:292
msgid "Enter Porkbun Secret Key"
msgstr "输入 Porkbun 密钥"

#: src/views/cert/CreateDnsModal.vue:162
#: src/views/cert/DnsView.vue:299
msgid "Enter Namecheap API Username"
msgstr "输入 Namecheap API 用户名"

#: src/views/cert/CreateDnsModal.vue:169
#: src/views/cert/DnsView.vue:306
msgid "Enter Namecheap API Key"
msgstr "输入 Namecheap API 密钥"

#: src/views/cert/CreateDnsModal.vue:176
#: src/views/cert/DnsView.vue:313
msgid "Enter NameSilo API Token"
msgstr "输入 NameSilo API 令牌"

#: src/views/cert/CreateDnsModal.vue:183
#: src/views/cert/DnsView.vue:320
msgid "Enter Name.com Username"
msgstr "输入 Name.com 用户名"

#: src/views/cert/CreateDnsModal.vue:190
#: src/views/cert/DnsView.vue:327
msgid "Enter Name.com Token"
msgstr "输入 Name.com 令牌"

#: src/views/cert/CreateDnsModal.vue:198
#: src/views/cert/DnsView.vue:334
msgid "Enter ClouDNS Auth ID (use Sub Auth ID by adding sub-prefix)"
msgstr "输入 ClouDNS 认证 ID（使用子认证 ID 时添加 sub- 前缀）"

#: src/views/cert/CreateDnsModal.vue:205
#: src/views/cert/DnsView.vue:341
msgid "Enter ClouDNS Auth Password"
msgstr "输入 ClouDNS 认证密码"

#: src/views/cert/CreateDnsModal.vue:212
#: src/views/cert/DnsView.vue:348
msgid "Enter Duck DNS Token"
msgstr "输入 Duck DNS 令牌"

#: src/views/cert/CreateDnsModal.vue:219
#: src/views/cert/DnsView.vue:355
msgid "Enter Hetzner Auth API Token"
msgstr "输入 Hetzner Auth API Token"

#: src/views/cert/CreateDnsModal.vue:226
#: src/views/cert/DnsView.vue:362
msgid "Enter Linode Token"
msgstr "输入 Linode Token"

#: src/views/cert/CreateDnsModal.vue:233
#: src/views/cert/DnsView.vue:369
msgid "Enter Vercel Token"
msgstr "输入 Vercel Token"

#: src/views/cert/DnsView.vue:31
#: src/views/cert/DnsView.vue:189
msgid "Note Name"
msgstr "备注名称"

#: src/views/cert/DnsView.vue:100
msgid "Are you sure you want to delete the DNS?"
msgstr "您确定要删除 DNS 吗？"

#: src/views/cert/DnsView.vue:181
msgid "Modify DNS"
msgstr "修改 DNS"

#: src/views/cert/DnsView.vue:193
msgid "Enter note name"
msgstr "输入备注名称"

#: src/views/cert/IndexView.vue:98
#: src/views/cert/UploadCertModal.vue:30
msgid "Upload Certificate"
msgstr "上传证书"

#: src/views/cert/IndexView.vue:115
msgid "Certificate List"
msgstr "证书列表"

#: src/views/cert/IndexView.vue:118
msgid "Account List"
msgstr "账户列表"

#: src/views/cert/IndexView.vue:121
msgid "DNS List"
msgstr "DNS 列表"

#: src/views/cert/ObtainModal.vue:18
msgid "Automatic"
msgstr "自动"

#: src/views/cert/ObtainModal.vue:19
msgid "Manual"
msgstr "手动"

#: src/views/cert/ObtainModal.vue:20
msgid "Self-signed"
msgstr "自签名"

#: src/views/cert/ObtainModal.vue:33
#: src/views/cert/ObtainModal.vue:89
#: src/views/cert/ObtainModal.vue:107
msgid "Issuance successful"
msgstr "签发成功"

#: src/views/cert/ObtainModal.vue:42
msgid "Please set up DNS resolution for the domain first, then continue with the issuance"
msgstr "请先为域名设置 DNS 解析，然后继续签发"

#: src/views/cert/ObtainModal.vue:48
msgid "DNS Records to Set"
msgstr "要设置的 DNS 记录"

#: src/views/cert/ObtainModal.vue:59
msgid "Host Record"
msgstr "主机记录"

#: src/views/cert/ObtainModal.vue:60
msgid "Record Value"
msgstr "记录值"

#: src/views/cert/ObtainModal.vue:120
msgid "Issue Certificate"
msgstr "签发证书"

#: src/views/cert/ObtainModal.vue:127
msgid "Issuance Mode"
msgstr "签发模式"

#: src/views/container/ComposeView.vue:65
#: src/views/container/ImageView.vue:60
#: src/views/container/NetworkView.vue:100
#: src/views/container/VolumeView.vue:54
#: src/views/setting/SettingUser.vue:67
#: src/views/setting/TokenModal.vue:34
#: src/views/task/CronView.vue:83
#: src/views/task/TaskView.vue:37
msgid "Creation Time"
msgstr "创建时间"

#: src/views/container/ComposeView.vue:96
#: src/views/file/ListTable.vue:68
#: src/views/file/ListTable.vue:220
#: src/views/ssh/IndexView.vue:71
#: src/views/task/CronView.vue:133
#: src/views/website/IndexView.vue:109
msgid "Edit"
msgstr "编辑"

#: src/views/container/ComposeView.vue:111
#: src/views/container/ContainerView.vue:237
#: src/views/container/ContainerView.vue:301
msgid "Start successful"
msgstr "启动成功"

#: src/views/container/ComposeView.vue:132
msgid "Are you sure you want to start compose %{ name }?"
msgstr "您确定要启动编排 %{ name } 吗？"

#: src/views/container/ComposeView.vue:143
msgid "Force pull images"
msgstr "强制拉取镜像"

#: src/views/container/ComposeView.vue:170
#: src/views/container/ContainerView.vue:244
#: src/views/container/ContainerView.vue:315
msgid "Stop successful"
msgstr "停止成功"

#: src/views/container/ComposeView.vue:176
msgid "Are you sure you want to stop compose %{ name }?"
msgstr "您确定要停止编排 %{ name } 吗？"

#: src/views/container/ComposeView.vue:201
#: src/views/container/ContainerView.vue:279
#: src/views/container/ContainerView.vue:357
#: src/views/container/ImageView.vue:118
#: src/views/container/NetworkView.vue:158
#: src/views/container/VolumeView.vue:112
msgid "Delete successful"
msgstr "删除成功"

#: src/views/container/ComposeView.vue:207
msgid "Are you sure you want to delete compose %{ name }?"
msgstr "您确定要删除编排 %{ name } 吗？"

#: src/views/container/ComposeView.vue:286
#: src/views/container/ComposeView.vue:313
msgid "Create Compose"
msgstr "创建编排"

#: src/views/container/ComposeView.vue:320
msgid "Compose Name"
msgstr "编排名称"

#: src/views/container/ComposeView.vue:323
#: src/views/container/ComposeView.vue:353
#: src/views/container/IndexView.vue:24
msgid "Compose"
msgstr "编排"

#: src/views/container/ComposeView.vue:334
#: src/views/container/ComposeView.vue:364
#: src/views/container/ContainerCreate.vue:356
msgid "Variable Name"
msgstr "变量名"

#: src/views/container/ComposeView.vue:335
#: src/views/container/ComposeView.vue:365
#: src/views/container/ContainerCreate.vue:357
msgid "Variable Value"
msgstr "变量值"

#: src/views/container/ComposeView.vue:346
msgid "Edit Compose"
msgstr "编辑编排"

#: src/views/container/ContainerCreate.vue:56
msgid "Always"
msgstr "总是"

#: src/views/container/ContainerCreate.vue:57
msgid "On failure (default 5 retries)"
msgstr "失败时（默认重试 5 次）"

#: src/views/container/ContainerCreate.vue:58
msgid "Unless stopped"
msgstr "除非停止"

#: src/views/container/ContainerCreate.vue:127
#: src/views/container/ContainerView.vue:402
msgid "Create Container"
msgstr "创建容器"

#: src/views/container/ContainerCreate.vue:137
#: src/views/container/ContainerView.vue:25
msgid "Container Name"
msgstr "容器名称"

#: src/views/container/ContainerCreate.vue:140
#: src/views/container/ContainerView.vue:52
#: src/views/container/ImageView.vue:36
msgid "Image"
msgstr "镜像"

#: src/views/container/ContainerCreate.vue:143
msgid "Ports"
msgstr "端口"

#: src/views/container/ContainerCreate.vue:149
msgid "Map Ports"
msgstr "映射端口"

#: src/views/container/ContainerCreate.vue:156
msgid "Expose All"
msgstr "全部暴露"

#: src/views/container/ContainerCreate.vue:161
msgid "Port Mapping"
msgstr "端口映射"

#: src/views/container/ContainerCreate.vue:169
msgid "Host (Start)"
msgstr "主机（起始）"

#: src/views/container/ContainerCreate.vue:170
msgid "Host (End)"
msgstr "主机（结束）"

#: src/views/container/ContainerCreate.vue:171
msgid "Container (Start)"
msgstr "容器（起始）"

#: src/views/container/ContainerCreate.vue:172
msgid "Container (End)"
msgstr "容器（结束）"

#: src/views/container/ContainerCreate.vue:173
msgid "Protocol"
msgstr "协议"

#: src/views/container/ContainerCreate.vue:184
msgid "Optional"
msgstr "可选"

#: src/views/container/ContainerCreate.vue:241
#: src/views/container/ContainerCreate.vue:292
msgid "Add"
msgstr "添加"

#: src/views/container/ContainerCreate.vue:244
#: src/views/dashboard/IndexView.vue:141
#: src/views/dashboard/IndexView.vue:797
#: src/views/monitor/IndexView.vue:286
msgid "Network"
msgstr "网络"

#: src/views/container/ContainerCreate.vue:247
msgid "Mount"
msgstr "挂载"

#: src/views/container/ContainerCreate.vue:252
msgid "Host Directory"
msgstr "主机目录"

#: src/views/container/ContainerCreate.vue:253
msgid "Container Directory"
msgstr "容器目录"

#: src/views/container/ContainerCreate.vue:254
#: src/views/file/ListTable.vue:73
#: src/views/file/ListTable.vue:145
#: src/views/file/ListTable.vue:300
#: src/views/file/PermissionModal.vue:113
#: src/views/file/ToolBar.vue:235
msgid "Permission"
msgstr "权限"

#: src/views/container/ContainerCreate.vue:273
msgid "Read-Write"
msgstr "读写"

#: src/views/container/ContainerCreate.vue:281
msgid "Read-Only"
msgstr "只读"

#: src/views/container/ContainerCreate.vue:295
#: src/views/container/ContainerCreate.vue:296
msgid "Command"
msgstr "命令"

#: src/views/container/ContainerCreate.vue:298
#: src/views/container/ContainerCreate.vue:301
msgid "Entrypoint"
msgstr "入口点"

#: src/views/container/ContainerCreate.vue:306
#: src/views/dashboard/IndexView.vue:533
#: src/views/monitor/IndexView.vue:205
#: src/views/monitor/IndexView.vue:216
#: src/views/monitor/IndexView.vue:238
#: src/views/task/SystemView.vue:78
#: src/views/toolbox/BenchmarkView.vue:188
msgid "Memory"
msgstr "内存"

#: src/views/container/ContainerCreate.vue:316
msgid "CPU Shares"
msgstr "CPU 份额"

#: src/views/container/ContainerCreate.vue:323
msgid "TTY (-t)"
msgstr "TTY (-t)"

#: src/views/container/ContainerCreate.vue:328
msgid "STDIN (-i)"
msgstr "STDIN (-i)"

#: src/views/container/ContainerCreate.vue:333
msgid "Auto Remove"
msgstr "自动移除"

#: src/views/container/ContainerCreate.vue:338
msgid "Privileged Mode"
msgstr "特权模式"

#: src/views/container/ContainerCreate.vue:343
msgid "Restart Policy"
msgstr "重启策略"

#: src/views/container/ContainerCreate.vue:346
#: src/views/container/ContainerCreate.vue:349
msgid "Select restart policy"
msgstr "选择重启策略"

#: src/views/container/ContainerCreate.vue:360
#: src/views/container/NetworkView.vue:295
#: src/views/container/VolumeView.vue:195
msgid "Labels"
msgstr "标签"

#: src/views/container/ContainerCreate.vue:364
#: src/views/container/NetworkView.vue:299
#: src/views/container/VolumeView.vue:199
msgid "Label Name"
msgstr "标签名称"

#: src/views/container/ContainerCreate.vue:365
#: src/views/container/NetworkView.vue:300
#: src/views/container/VolumeView.vue:200
msgid "Label Value"
msgstr "标签值"

#: src/views/container/ContainerView.vue:63
msgid "Ports (Host->Container)"
msgstr "端口（主机->容器）"

#: src/views/container/ContainerView.vue:118
#: src/views/container/ContainerView.vue:465
#: src/views/file/ListTable.vue:84
#: src/views/file/ListTable.vue:264
msgid "Rename"
msgstr "重命名"

#: src/views/container/ContainerView.vue:141
#: src/views/container/ContainerView.vue:411
msgid "Force Stop"
msgstr "强制停止"

#: src/views/container/ContainerView.vue:146
#: src/views/container/ContainerView.vue:412
msgid "Pause"
msgstr "暂停"

#: src/views/container/ContainerView.vue:151
#: src/views/container/ContainerView.vue:413
msgid "Resume"
msgstr "恢复"

#: src/views/container/ContainerView.vue:196
#: src/views/file/ListTable.vue:364
msgid "More"
msgstr "更多"

#: src/views/container/ContainerView.vue:229
msgid "Rename successful"
msgstr "重命名成功"

#: src/views/container/ContainerView.vue:251
#: src/views/container/ContainerView.vue:329
msgid "Restart successful"
msgstr "重启成功"

#: src/views/container/ContainerView.vue:258
#: src/views/container/ContainerView.vue:343
msgid "Force stop successful"
msgstr "强制停止成功"

#: src/views/container/ContainerView.vue:265
#: src/views/container/ContainerView.vue:371
msgid "Pause successful"
msgstr "暂停成功"

#: src/views/container/ContainerView.vue:272
#: src/views/container/ContainerView.vue:385
msgid "Resume successful"
msgstr "恢复成功"

#: src/views/container/ContainerView.vue:286
#: src/views/container/ImageView.vue:125
#: src/views/container/NetworkView.vue:165
#: src/views/container/VolumeView.vue:119
msgid "Cleanup successful"
msgstr "清理成功"

#: src/views/container/ContainerView.vue:292
msgid "Please select containers to start"
msgstr "请选择要启动的容器"

#: src/views/container/ContainerView.vue:306
msgid "Please select containers to stop"
msgstr "请选择要停止的容器"

#: src/views/container/ContainerView.vue:320
msgid "Please select containers to restart"
msgstr "请选择要重启的容器"

#: src/views/container/ContainerView.vue:334
msgid "Please select containers to force stop"
msgstr "请选择要强制停止的容器"

#: src/views/container/ContainerView.vue:348
msgid "Please select containers to delete"
msgstr "请选择要删除的容器"

#: src/views/container/ContainerView.vue:362
msgid "Please select containers to pause"
msgstr "请选择要暂停的容器"

#: src/views/container/ContainerView.vue:376
msgid "Please select containers to resume"
msgstr "请选择要恢复的容器"

#: src/views/container/ContainerView.vue:405
msgid "Cleanup Containers"
msgstr "清理容器"

#: src/views/container/ContainerView.vue:472
#: src/views/file/ListTable.vue:719
msgid "New Name"
msgstr "新名称"

#: src/views/container/ContainerView.vue:477
msgid "Enter new name"
msgstr "输入新名称"

#: src/views/container/ImageView.vue:29
msgid "Container Count"
msgstr "容器数量"

#: src/views/container/ImageView.vue:84
#: src/views/container/NetworkView.vue:124
#: src/views/container/VolumeView.vue:78
#: src/views/firewall/ForwardView.vue:93
#: src/views/firewall/IpRuleView.vue:135
#: src/views/firewall/RuleView.vue:172
#: src/views/task/TaskView.vue:87
msgid "Are you sure you want to delete?"
msgstr "您确定要删除吗？"

#: src/views/container/ImageView.vue:134
msgid "Pull successful"
msgstr "拉取成功"

#: src/views/container/ImageView.vue:150
#: src/views/container/ImageView.vue:180
msgid "Pull Image"
msgstr "拉取镜像"

#: src/views/container/ImageView.vue:152
msgid "Cleanup Images"
msgstr "清理镜像"

#: src/views/container/ImageView.vue:187
msgid "Image Name"
msgstr "镜像名称"

#: src/views/container/ImageView.vue:192
msgid "docker.io/php:8.3-fpm"
msgstr "docker.io/php:8.3-fpm"

#: src/views/container/ImageView.vue:195
msgid "Authentication"
msgstr "身份验证"

#: src/views/container/ImageView.vue:212
#: src/views/database/CreateDatabaseModal.vue:107
#: src/views/database/CreateUserModal.vue:84
#: src/views/database/UpdateUserModal.vue:55
msgid "Enter password"
msgstr "输入密码"

#: src/views/container/IndexView.vue:21
msgid "Containers"
msgstr "容器"

#: src/views/container/IndexView.vue:27
msgid "Images"
msgstr "镜像"

#: src/views/container/IndexView.vue:30
msgid "Networks"
msgstr "网络"

#: src/views/container/IndexView.vue:33
msgid "Volumes"
msgstr "卷"

#: src/views/container/NetworkView.vue:52
#: src/views/container/NetworkView.vue:232
#: src/views/container/VolumeView.vue:33
#: src/views/container/VolumeView.vue:186
msgid "Driver"
msgstr "驱动"

#: src/views/container/NetworkView.vue:59
#: src/views/container/VolumeView.vue:40
msgid "Scope"
msgstr "范围"

#: src/views/container/NetworkView.vue:66
#: src/views/container/NetworkView.vue:244
#: src/views/container/NetworkView.vue:271
msgid "Subnet"
msgstr "子网"

#: src/views/container/NetworkView.vue:83
#: src/views/container/NetworkView.vue:252
#: src/views/container/NetworkView.vue:279
msgid "Gateway"
msgstr "网关"

#: src/views/container/NetworkView.vue:191
#: src/views/container/NetworkView.vue:222
msgid "Create Network"
msgstr "创建网络"

#: src/views/container/NetworkView.vue:194
msgid "Cleanup Networks"
msgstr "清理网络"

#: src/views/container/NetworkView.vue:229
msgid "Network Name"
msgstr "网络名称"

#: src/views/container/NetworkView.vue:249
#: src/views/container/NetworkView.vue:265
msgid "***********/24"
msgstr "***********/24"

#: src/views/container/NetworkView.vue:257
msgid "*************"
msgstr "*************"

#: src/views/container/NetworkView.vue:260
#: src/views/container/NetworkView.vue:287
msgid "IP Range"
msgstr "IP 范围"

#: src/views/container/NetworkView.vue:276
msgid "2408:400e::/48"
msgstr "2408:400e::/48"

#: src/views/container/NetworkView.vue:284
msgid "2408:400e::1"
msgstr "2408:400e::1"

#: src/views/container/NetworkView.vue:292
msgid "2408:400e::/64"
msgstr "2408:400e::/64"

#: src/views/container/NetworkView.vue:303
#: src/views/container/VolumeView.vue:203
msgid "Options"
msgstr "选项"

#: src/views/container/NetworkView.vue:307
#: src/views/container/VolumeView.vue:207
msgid "Option Name"
msgstr "选项名称"

#: src/views/container/NetworkView.vue:308
#: src/views/container/VolumeView.vue:208
msgid "Option Value"
msgstr "选项值"

#: src/views/container/VolumeView.vue:47
#: src/views/dashboard/IndexView.vue:644
msgid "Mount Point"
msgstr "挂载点"

#: src/views/container/VolumeView.vue:145
#: src/views/container/VolumeView.vue:176
msgid "Create Volume"
msgstr "创建卷"

#: src/views/container/VolumeView.vue:148
msgid "Cleanup Volumes"
msgstr "清理卷"

#: src/views/container/VolumeView.vue:183
msgid "Volume Name"
msgstr "卷名称"

#: src/views/dashboard/IndexView.vue:129
msgid "Running blocked"
msgstr "运行阻塞"

#: src/views/dashboard/IndexView.vue:131
msgid "Running slowly"
msgstr "运行缓慢"

#: src/views/dashboard/IndexView.vue:133
msgid "Running normally"
msgstr "运行正常"

#: src/views/dashboard/IndexView.vue:135
msgid "Running smoothly"
msgstr "运行流畅"

#: src/views/dashboard/IndexView.vue:141
#: src/views/dashboard/IndexView.vue:798
#: src/views/dashboard/IndexView.vue:818
#: src/views/toolbox/BenchmarkView.vue:219
msgid "Disk"
msgstr "磁盘"

#: src/views/dashboard/IndexView.vue:164
#: src/views/dashboard/IndexView.vue:181
msgid "Send"
msgstr "发送"

#: src/views/dashboard/IndexView.vue:164
#: src/views/dashboard/IndexView.vue:205
msgid "Receive"
msgstr "接收"

#: src/views/dashboard/IndexView.vue:165
#: src/views/dashboard/IndexView.vue:181
#: src/views/dashboard/IndexView.vue:842
#: src/views/file/PermissionModal.vue:88
#: src/views/file/PermissionModal.vue:97
#: src/views/file/PermissionModal.vue:106
msgid "Read"
msgstr "读取"

#: src/views/dashboard/IndexView.vue:165
#: src/views/dashboard/IndexView.vue:205
#: src/views/dashboard/IndexView.vue:843
#: src/views/file/PermissionModal.vue:89
#: src/views/file/PermissionModal.vue:98
#: src/views/file/PermissionModal.vue:107
msgid "Write"
msgstr "写入"

#: src/views/dashboard/IndexView.vue:173
msgid "Unit %{unit}"
msgstr "单位 %{unit}"

#: src/views/dashboard/IndexView.vue:187
#: src/views/dashboard/IndexView.vue:211
#: src/views/monitor/IndexView.vue:93
#: src/views/monitor/IndexView.vue:115
#: src/views/monitor/IndexView.vue:137
#: src/views/monitor/IndexView.vue:192
#: src/views/monitor/IndexView.vue:251
#: src/views/monitor/IndexView.vue:273
#: src/views/monitor/IndexView.vue:335
#: src/views/monitor/IndexView.vue:357
#: src/views/monitor/IndexView.vue:379
#: src/views/monitor/IndexView.vue:401
msgid "Maximum"
msgstr "最大值"

#: src/views/dashboard/IndexView.vue:188
#: src/views/dashboard/IndexView.vue:212
#: src/views/monitor/IndexView.vue:94
#: src/views/monitor/IndexView.vue:116
#: src/views/monitor/IndexView.vue:138
#: src/views/monitor/IndexView.vue:193
#: src/views/monitor/IndexView.vue:252
#: src/views/monitor/IndexView.vue:274
#: src/views/monitor/IndexView.vue:336
#: src/views/monitor/IndexView.vue:358
#: src/views/monitor/IndexView.vue:380
#: src/views/monitor/IndexView.vue:402
msgid "Minimum"
msgstr "最小值"

#: src/views/dashboard/IndexView.vue:192
#: src/views/dashboard/IndexView.vue:216
#: src/views/monitor/IndexView.vue:98
#: src/views/monitor/IndexView.vue:120
#: src/views/monitor/IndexView.vue:142
#: src/views/monitor/IndexView.vue:197
#: src/views/monitor/IndexView.vue:256
#: src/views/monitor/IndexView.vue:278
#: src/views/monitor/IndexView.vue:340
#: src/views/monitor/IndexView.vue:362
#: src/views/monitor/IndexView.vue:384
#: src/views/monitor/IndexView.vue:406
msgid "Average"
msgstr "平均值"

#: src/views/dashboard/IndexView.vue:324
msgid "Panel restarting..."
msgstr "面板重启中……"

#: src/views/dashboard/IndexView.vue:326
msgid "Panel restarted successfully"
msgstr "面板重启成功"

#: src/views/dashboard/IndexView.vue:338
msgid "Current version is the latest"
msgstr "当前版本是最新版本"

#: src/views/dashboard/IndexView.vue:434
#: src/views/task/IndexView.vue:29
msgid "Scheduled Tasks"
msgstr "计划任务"

#: src/views/dashboard/IndexView.vue:443
msgid "Sponsor Support"
msgstr "赞助支持"

#: src/views/dashboard/IndexView.vue:449
msgid "Are you sure you want to restart the panel?"
msgstr "您确定要重启面板吗？"

#: src/views/dashboard/IndexView.vue:457
msgid "Resource Overview"
msgstr "资源概览"

#: src/views/dashboard/IndexView.vue:474
msgid "Last 1 minute"
msgstr "最近 1 分钟"

#: src/views/dashboard/IndexView.vue:481
msgid "Last 5 minutes"
msgstr "最近 5 分钟"

#: src/views/dashboard/IndexView.vue:488
msgid "Last 15 minutes"
msgstr "最近 15 分钟"

#: src/views/dashboard/IndexView.vue:506
#: src/views/dashboard/IndexView.vue:517
msgid "cores"
msgstr "核心"

#: src/views/dashboard/IndexView.vue:511
msgid "Model"
msgstr "型号"

#: src/views/dashboard/IndexView.vue:515
msgid "Parameters"
msgstr "参数"

#: src/views/dashboard/IndexView.vue:518
msgid "cache"
msgstr "缓存"

#: src/views/dashboard/IndexView.vue:524
#: src/views/monitor/IndexView.vue:179
msgid "Usage"
msgstr "使用率"

#: src/views/dashboard/IndexView.vue:525
msgid "Frequency"
msgstr "频率"

#: src/views/dashboard/IndexView.vue:545
msgid "Active"
msgstr "活跃"

#: src/views/dashboard/IndexView.vue:551
msgid "Inactive"
msgstr "非活跃"

#: src/views/dashboard/IndexView.vue:557
msgid "Free"
msgstr "空闲"

#: src/views/dashboard/IndexView.vue:563
msgid "Shared"
msgstr "共享"

#: src/views/dashboard/IndexView.vue:569
msgid "Committed"
msgstr "已提交"

#: src/views/dashboard/IndexView.vue:575
msgid "Commit Limit"
msgstr "提交限制"

#: src/views/dashboard/IndexView.vue:581
#: src/views/toolbox/SystemView.vue:149
msgid "SWAP Size"
msgstr "SWAP 大小"

#: src/views/dashboard/IndexView.vue:587
msgid "SWAP Used"
msgstr "已用 SWAP"

#: src/views/dashboard/IndexView.vue:593
msgid "SWAP Available"
msgstr "可用 SWAP"

#: src/views/dashboard/IndexView.vue:599
msgid "Physical Memory Size"
msgstr "物理内存大小"

#: src/views/dashboard/IndexView.vue:605
msgid "Physical Memory Used"
msgstr "已用物理内存"

#: src/views/dashboard/IndexView.vue:611
msgid "Physical Memory Available"
msgstr "可用物理内存"

#: src/views/dashboard/IndexView.vue:648
msgid "File System"
msgstr "文件系统"

#: src/views/dashboard/IndexView.vue:652
msgid "Inodes Usage"
msgstr "Inodes 使用率"

#: src/views/dashboard/IndexView.vue:656
msgid "Inodes Total"
msgstr "Inodes 总数"

#: src/views/dashboard/IndexView.vue:660
msgid "Inodes Used"
msgstr "已用 Inodes"

#: src/views/dashboard/IndexView.vue:664
msgid "Inodes Available"
msgstr "可用 Inodes"

#: src/views/dashboard/IndexView.vue:681
msgid "Quick Apps"
msgstr "快捷应用"

#: src/views/dashboard/IndexView.vue:724
msgid "You have not set any apps to display here!"
msgstr "您尚未设置任何要在此处显示的应用！"

#: src/views/dashboard/IndexView.vue:728
msgid "Environment Information"
msgstr "环境信息"

#: src/views/dashboard/IndexView.vue:731
msgid "System Hostname"
msgstr "系统主机名"

#: src/views/dashboard/IndexView.vue:737
msgid "System Version"
msgstr "系统版本"

#: src/views/dashboard/IndexView.vue:746
msgid "System Kernel Version"
msgstr "系统内核版本"

#: src/views/dashboard/IndexView.vue:752
msgid "System Uptime"
msgstr "系统运行时间"

#: src/views/dashboard/IndexView.vue:758
msgid "Panel Internal Version"
msgstr "面板内部版本"

#: src/views/dashboard/IndexView.vue:770
msgid "Panel Compile Information"
msgstr "面板编译信息"

#: src/views/dashboard/IndexView.vue:787
msgid "Real-time Monitoring"
msgstr "实时监控"

#: src/views/dashboard/IndexView.vue:801
msgid "Unit"
msgstr "单位"

#: src/views/dashboard/IndexView.vue:809
msgid "Network Card"
msgstr "网卡"

#: src/views/dashboard/IndexView.vue:829
msgid "Total Sent"
msgstr "总发送量"

#: src/views/dashboard/IndexView.vue:831
msgid "Total Received"
msgstr "总接收量"

#: src/views/dashboard/IndexView.vue:834
msgid "Real-time Sent"
msgstr "实时发送"

#: src/views/dashboard/IndexView.vue:838
msgid "Real-time Received"
msgstr "实时接收"

#: src/views/dashboard/IndexView.vue:845
msgid "Real-time Read/Write"
msgstr "实时读/写"

#: src/views/dashboard/IndexView.vue:848
msgid "Read/Write Latency"
msgstr "读/写延迟"

#: src/views/dashboard/UpdateView.vue:24
msgid "Update Panel"
msgstr "更新面板"

#: src/views/dashboard/UpdateView.vue:25
msgid "Are you sure you want to update the panel?"
msgstr "您确定要更新面板吗？"

#: src/views/dashboard/UpdateView.vue:29
msgid "Panel updating..."
msgstr "面板更新中……"

#: src/views/dashboard/UpdateView.vue:40
msgid "Panel updated successfully"
msgstr "面板更新成功"

#: src/views/dashboard/UpdateView.vue:47
msgid "Update canceled"
msgstr "更新已取消"

#: src/views/dashboard/UpdateView.vue:59
msgid "Update Now"
msgstr "立即更新"

#: src/views/dashboard/UpdateView.vue:85
msgid "Loading update information, please wait a moment"
msgstr "正在加载更新信息，请稍候"

#: src/views/database/CreateDatabaseModal.vue:20
#: src/views/database/CreateUserModal.vue:20
msgid "Local (localhost)"
msgstr "本地 (localhost)"

#: src/views/database/CreateDatabaseModal.vue:21
#: src/views/database/CreateUserModal.vue:21
msgid "All (%)"
msgstr "所有 (%)"

#: src/views/database/CreateDatabaseModal.vue:22
#: src/views/database/CreateUserModal.vue:22
msgid "Specific"
msgstr "特定"

#: src/views/database/CreateDatabaseModal.vue:54
#: src/views/database/IndexView.vue:32
msgid "Create Database"
msgstr "创建数据库"

#: src/views/database/CreateDatabaseModal.vue:62
#: src/views/database/CreateUserModal.vue:62
#: src/views/database/DatabaseList.vue:42
#: src/views/database/IndexView.vue:51
#: src/views/database/UserList.vue:91
msgid "Server"
msgstr "服务器"

#: src/views/database/CreateDatabaseModal.vue:66
#: src/views/database/CreateUserModal.vue:66
msgid "Select server"
msgstr "选择服务器"

#: src/views/database/CreateDatabaseModal.vue:84
msgid "Authorized User"
msgstr "授权用户"

#: src/views/database/CreateDatabaseModal.vue:90
msgid "Enter authorized username (leave empty for no authorization)"
msgstr "输入授权用户名（留空表示无授权）"

#: src/views/database/CreateDatabaseModal.vue:114
#: src/views/database/CreateUserModal.vue:91
msgid "Select host"
msgstr "选择主机"

#: src/views/database/CreateDatabaseModal.vue:121
#: src/views/database/CreateUserModal.vue:95
msgid "Specific Host"
msgstr "特定主机"

#: src/views/database/CreateDatabaseModal.vue:127
#: src/views/database/CreateUserModal.vue:100
msgid "Enter supported host address"
msgstr "输入支持的主机地址"

#: src/views/database/CreateServerModal.vue:47
#: src/views/database/IndexView.vue:40
msgid "Add Server"
msgstr "添加服务器"

#: src/views/database/CreateServerModal.vue:60
#: src/views/database/UpdateServerModal.vue:60
msgid "Enter database server name"
msgstr "输入数据库服务器名称"

#: src/views/database/CreateServerModal.vue:67
msgid "Select database type"
msgstr "选择数据库类型"

#: src/views/database/CreateServerModal.vue:78
#: src/views/database/UpdateServerModal.vue:70
msgid "Enter database server host"
msgstr "输入数据库服务器主机"

#: src/views/database/CreateServerModal.vue:84
#: src/views/database/UpdateServerModal.vue:76
#: src/views/firewall/ForwardView.vue:32
#: src/views/firewall/RuleView.vue:49
#: src/views/setting/SettingBase.vue:49
#: src/views/ssh/CreateModal.vue:68
#: src/views/ssh/UpdateModal.vue:74
#: src/views/website/IndexView.vue:436
msgid "Port"
msgstr "端口"

#: src/views/database/CreateServerModal.vue:89
#: src/views/database/UpdateServerModal.vue:81
msgid "Enter database server port"
msgstr "输入数据库服务器端口"

#: src/views/database/CreateServerModal.vue:99
#: src/views/database/UpdateServerModal.vue:91
msgid "Enter database server username"
msgstr "输入数据库服务器用户名"

#: src/views/database/CreateServerModal.vue:108
#: src/views/database/UpdateServerModal.vue:100
msgid "Enter database server password"
msgstr "输入数据库服务器密码"

#: src/views/database/CreateServerModal.vue:116
#: src/views/database/UpdateServerModal.vue:108
msgid "Enter database server comment"
msgstr "输入数据库服务器备注"

#: src/views/database/CreateUserModal.vue:87
msgid "Host (MySQL only)"
msgstr "主机（仅限MySQL）"

#: src/views/database/CreateUserModal.vue:103
#: src/views/database/UpdateUserModal.vue:58
#: src/views/database/UserList.vue:99
msgid "Privileges"
msgstr "权限"

#: src/views/database/CreateUserModal.vue:114
#: src/views/database/UpdateUserModal.vue:69
msgid "Enter database user comment"
msgstr "输入数据库用户备注"

#: src/views/database/DatabaseList.vue:47
msgid "Encoding"
msgstr "编码"

#: src/views/database/DatabaseList.vue:87
msgid "Are you sure you want to delete this database?"
msgstr "您确定要删除这个数据库吗？"

#: src/views/database/ServerList.vue:76
#: src/views/database/UserList.vue:70
#: src/views/file/ListTable.vue:516
#: src/views/file/ListTable.vue:536
#: src/views/file/SearchModal.vue:65
#: src/views/file/ToolBar.vue:145
#: src/views/file/ToolBar.vue:165
#: src/views/setting/TokenModal.vue:165
msgid "Copied successfully"
msgstr "复制成功"

#: src/views/database/ServerList.vue:80
#: src/views/database/UserList.vue:74
#: src/views/file/ListTable.vue:71
#: src/views/file/ListTable.vue:298
#: src/views/file/ToolBar.vue:232
msgid "Copy"
msgstr "复制"

#: src/views/database/ServerList.vue:121
#: src/views/database/UserList.vue:138
msgid "Valid"
msgstr "有效"

#: src/views/database/ServerList.vue:121
#: src/views/database/UserList.vue:138
msgid "Invalid"
msgstr "无效"

#: src/views/database/ServerList.vue:147
#: src/views/toolbox/SystemView.vue:87
msgid "Synchronized successfully"
msgstr "同步成功"

#: src/views/database/ServerList.vue:153
msgid "Are you sure you want to synchronize database users (excluding password) to the panel?"
msgstr "您确定要将数据库用户（不包括密码）同步到面板吗？"

#: src/views/database/ServerList.vue:165
msgid "Sync"
msgstr "同步"

#: src/views/database/ServerList.vue:195
msgid "Built-in servers cannot be deleted. If you need to delete them, please uninstall the corresponding app"
msgstr "内置服务器无法删除。如果需要删除它们，请卸载相应的应用"

#: src/views/database/ServerList.vue:206
msgid "Are you sure you want to delete the server?"
msgstr "您确定要删除该服务器吗？"

#: src/views/database/UpdateServerModal.vue:47
msgid "Modify Server"
msgstr "修改服务器"

#: src/views/database/UpdateUserModal.vue:41
msgid "Modify User"
msgstr "修改用户"

#: src/views/database/UserList.vue:61
msgid "Not saved"
msgstr "未保存"

#: src/views/database/UserList.vue:180
msgid "Are you sure you want to delete the user?"
msgstr "您确定要删除该用户吗？"

#: src/views/error-page/NotFound.vue:11
msgid "Sorry, the page you visited does not exist."
msgstr "抱歉，您访问的页面不存在。"

#: src/views/error-page/NotFound.vue:19
msgid "Back to Home"
msgstr "返回首页"

#: src/views/file/CompressModal.vue:41
msgid "Compressing..."
msgstr "压缩中……"

#: src/views/file/CompressModal.vue:49
msgid "Compressed successfully"
msgstr "压缩成功"

#: src/views/file/CompressModal.vue:73
#: src/views/file/CompressModal.vue:105
#: src/views/file/ListTable.vue:75
#: src/views/file/ListTable.vue:245
#: src/views/file/ListTable.vue:301
#: src/views/file/ToolBar.vue:234
msgid "Compress"
msgstr "压缩"

#: src/views/file/CompressModal.vue:81
msgid "Files to compress"
msgstr "要压缩的文件"

#: src/views/file/CompressModal.vue:84
msgid "Compress to"
msgstr "压缩到"

#: src/views/file/CompressModal.vue:87
msgid "Format"
msgstr "格式"

#: src/views/file/EditModal.vue:22
msgid "Edit - %{ file }"
msgstr "编辑 - %{ file }"

#: src/views/file/EditModal.vue:30
msgid "Refresh"
msgstr "刷新"

#: src/views/file/ListTable.vue:65
#: src/views/file/ListTable.vue:222
msgid "Open"
msgstr "打开"

#: src/views/file/ListTable.vue:67
#: src/views/file/ListTable.vue:220
msgid "Preview"
msgstr "预览"

#: src/views/file/ListTable.vue:72
#: src/views/file/ListTable.vue:299
#: src/views/file/ToolBar.vue:233
msgid "Move"
msgstr "移动"

#: src/views/file/ListTable.vue:75
#: src/views/file/ListTable.vue:247
msgid "Download"
msgstr "下载"

#: src/views/file/ListTable.vue:79
#: src/views/file/ListTable.vue:303
#: src/views/file/ListTable.vue:741
msgid "Uncompress"
msgstr "解压"

#: src/views/file/ListTable.vue:89
#: src/views/file/ToolBar.vue:229
msgid "Paste"
msgstr "粘贴"

#: src/views/file/ListTable.vue:279
#: src/views/file/SearchModal.vue:88
msgid "Are you sure you want to delete %{ name }?"
msgstr "您确定要删除 %{ name } 吗？"

#: src/views/file/ListTable.vue:320
#: src/views/file/ListTable.vue:335
#: src/views/file/ListTable.vue:575
#: src/views/file/ListTable.vue:588
#: src/views/file/ToolBar.vue:77
#: src/views/file/ToolBar.vue:94
msgid "Marked successfully, please navigate to the destination path to paste"
msgstr "标记成功，请导航到目标路径进行粘贴"

#: src/views/file/ListTable.vue:412
#: src/views/file/ListTable.vue:499
#: src/views/file/ToolBar.vue:128
msgid "Warning"
msgstr "警告"

#: src/views/file/ListTable.vue:413
msgid "There are items with the same name. Do you want to overwrite?"
msgstr "存在同名项目。您要覆盖吗？"

#: src/views/file/ListTable.vue:414
#: src/views/file/ListTable.vue:509
#: src/views/file/ToolBar.vue:138
msgid "Overwrite"
msgstr "覆盖"

#: src/views/file/ListTable.vue:421
#: src/views/file/ListTable.vue:437
msgid "Renamed %{ source } to %{ target } successfully"
msgstr "成功将 %{ source } 重命名为 %{ target }"

#: src/views/file/ListTable.vue:459
msgid "Uncompressing..."
msgstr "解压中……"

#: src/views/file/ListTable.vue:466
msgid "Uncompressed successfully"
msgstr "解压成功"

#: src/views/file/ListTable.vue:475
#: src/views/file/ToolBar.vue:104
msgid "Please mark the files/folders to copy or move first"
msgstr "请先标记要复制或移动的文件/文件夹"

#: src/views/file/ListTable.vue:500
#: src/views/file/ToolBar.vue:129
msgid "There are items with the same name. %{ items } Do you want to overwrite?"
msgstr "存在同名项目。%{ items } 是否要覆盖？"

#: src/views/file/ListTable.vue:522
#: src/views/file/ListTable.vue:542
#: src/views/file/ToolBar.vue:151
#: src/views/file/ToolBar.vue:171
msgid "Moved successfully"
msgstr "移动成功"

#: src/views/file/ListTable.vue:528
#: src/views/file/ToolBar.vue:157
msgid "Canceled"
msgstr "已取消"

#: src/views/file/ListTable.vue:711
msgid "Rename - %{ source }"
msgstr "重命名 - %{ source }"

#: src/views/file/ListTable.vue:729
msgid "Uncompress - %{ file }"
msgstr "解压 - %{ file }"

#: src/views/file/ListTable.vue:737
msgid "Uncompress to"
msgstr "解压到"

#: src/views/file/PathInput.vue:154
msgid "Enter search content"
msgstr "输入搜索内容"

#: src/views/file/PathInput.vue:157
msgid "Include subdirectories"
msgstr "包括子目录"

#: src/views/file/PermissionModal.vue:65
msgid "Batch modify permissions"
msgstr "批量修改权限"

#: src/views/file/PermissionModal.vue:66
msgid "Modify permissions - %{ path }"
msgstr "修改权限 - %{ path }"

#: src/views/file/PermissionModal.vue:86
#: src/views/file/PermissionModal.vue:116
msgid "Owner"
msgstr "所有者"

#: src/views/file/PermissionModal.vue:90
#: src/views/file/PermissionModal.vue:99
#: src/views/file/PermissionModal.vue:108
msgid "Execute"
msgstr "执行"

#: src/views/file/PermissionModal.vue:95
#: src/views/file/PermissionModal.vue:119
msgid "Group"
msgstr "组"

#: src/views/file/PermissionModal.vue:104
msgid "Others"
msgstr "其他"

#: src/views/file/PreviewModal.vue:31
msgid "Preview - "
msgstr "预览 - "

#: src/views/file/SearchModal.vue:71
msgid "Copy Path"
msgstr "复制路径"

#: src/views/file/SearchModal.vue:154
msgid "%{ keyword } - Search Results"
msgstr "%{ keyword } - 搜索结果"

#: src/views/file/ToolBar.vue:60
msgid "Download task created successfully"
msgstr "下载任务创建成功"

#: src/views/file/ToolBar.vue:66
msgid "Please select files/folders to copy"
msgstr "请选择要复制的文件/文件夹"

#: src/views/file/ToolBar.vue:83
msgid "Please select files/folders to move"
msgstr "请选择要移动的文件/文件夹"

#: src/views/file/ToolBar.vue:180
msgid "Please select files/folders to delete"
msgstr "请选择要删除的文件/文件夹"

#: src/views/file/ToolBar.vue:219
#: src/views/file/ToolBar.vue:249
msgid "New"
msgstr "新建"

#: src/views/file/ToolBar.vue:222
#: src/views/file/ToolBar.vue:267
msgid "Remote Download"
msgstr "远程下载"

#: src/views/file/ToolBar.vue:240
msgid "Are you sure you want to delete in bulk?"
msgstr "您确定要批量删除吗？"

#: src/views/file/ToolBar.vue:275
msgid "Download URL"
msgstr "下载 URL"

#: src/views/file/ToolBar.vue:278
msgid "Save as"
msgstr "另存为"

#: src/views/file/UploadModal.vue:20
msgid "Upload %{ fileName } successful"
msgstr "上传 %{ fileName } 成功"

#: src/views/file/UploadModal.vue:53
msgid "For large files, it is recommended to use SFTP and other methods to upload"
msgstr "对于大文件，建议使用 SFTP 等方法上传"

#: src/views/firewall/CreateForwardModal.vue:50
#: src/views/firewall/ForwardView.vue:166
msgid "Create Forwarding"
msgstr "创建转发"

#: src/views/firewall/CreateForwardModal.vue:58
#: src/views/firewall/CreateIpModal.vue:97
#: src/views/firewall/CreateModal.vue:101
#: src/views/firewall/ForwardView.vue:15
#: src/views/firewall/IpRuleView.vue:15
#: src/views/firewall/RuleView.vue:15
msgid "Transport Protocol"
msgstr "传输协议"

#: src/views/firewall/CreateForwardModal.vue:61
#: src/views/firewall/ForwardView.vue:44
msgid "Target IP"
msgstr "目标 IP"

#: src/views/firewall/CreateForwardModal.vue:66
msgid "Source Port"
msgstr "源端口"

#: src/views/firewall/CreateForwardModal.vue:76
#: src/views/firewall/ForwardView.vue:62
msgid "Target Port"
msgstr "目标端口"

#: src/views/firewall/CreateIpModal.vue:38
#: src/views/firewall/CreateModal.vue:38
#: src/views/firewall/IpRuleView.vue:69
#: src/views/firewall/RuleView.vue:103
msgid "Accept"
msgstr "接受"

#: src/views/firewall/CreateIpModal.vue:42
#: src/views/firewall/CreateModal.vue:42
#: src/views/firewall/IpRuleView.vue:71
#: src/views/firewall/RuleView.vue:105
msgid "Drop"
msgstr "丢弃"

#: src/views/firewall/CreateIpModal.vue:46
#: src/views/firewall/CreateModal.vue:46
#: src/views/firewall/IpRuleView.vue:73
#: src/views/firewall/RuleView.vue:107
msgid "Reject"
msgstr "拒绝"

#: src/views/firewall/CreateIpModal.vue:53
#: src/views/firewall/CreateModal.vue:53
#: src/views/firewall/IpRuleView.vue:98
#: src/views/firewall/RuleView.vue:132
msgid "Inbound"
msgstr "入站"

#: src/views/firewall/CreateIpModal.vue:57
#: src/views/firewall/CreateModal.vue:57
#: src/views/firewall/IpRuleView.vue:100
#: src/views/firewall/RuleView.vue:134
msgid "Outbound"
msgstr "出站"

#: src/views/firewall/CreateIpModal.vue:78
msgid "%{ address } created successfully"
msgstr "%{ address } 创建成功"

#: src/views/firewall/CreateIpModal.vue:89
#: src/views/firewall/CreateModal.vue:93
#: src/views/firewall/IpRuleView.vue:208
#: src/views/firewall/RuleView.vue:245
msgid "Create Rule"
msgstr "创建规则"

#: src/views/firewall/CreateIpModal.vue:100
#: src/views/firewall/CreateModal.vue:104
#: src/views/firewall/IpRuleView.vue:32
#: src/views/firewall/RuleView.vue:32
msgid "Network Protocol"
msgstr "网络协议"

#: src/views/firewall/CreateIpModal.vue:103
msgid "IP Address"
msgstr "IP 地址"

#: src/views/firewall/CreateIpModal.vue:108
#: src/views/firewall/CreateModal.vue:133
msgid "Optional IP or IP range: 127.0.0.1 or **********/24 (multiple separated by commas)"
msgstr "可选 IP 或 IP 范围：127.0.0.1 或 **********/24（多个用逗号分隔）"

#: src/views/firewall/CreateIpModal.vue:114
#: src/views/firewall/CreateModal.vue:139
#: src/views/firewall/IpRuleView.vue:49
#: src/views/firewall/RuleView.vue:83
msgid "Strategy"
msgstr "策略"

#: src/views/firewall/CreateIpModal.vue:117
#: src/views/firewall/CreateModal.vue:142
#: src/views/firewall/IpRuleView.vue:85
#: src/views/firewall/RuleView.vue:119
msgid "Direction"
msgstr "方向"

#: src/views/firewall/CreateModal.vue:109
msgid "Start Port"
msgstr "起始端口"

#: src/views/firewall/CreateModal.vue:119
msgid "End Port"
msgstr "结束端口"

#: src/views/firewall/CreateModal.vue:129
#: src/views/firewall/IpRuleView.vue:110
#: src/views/firewall/RuleView.vue:144
msgid "Target"
msgstr "目标"

#: src/views/firewall/ForwardView.vue:137
#: src/views/firewall/IpRuleView.vue:179
#: src/views/firewall/RuleView.vue:216
msgid "Please select rules to delete"
msgstr "请选择要删除的规则"

#: src/views/firewall/ForwardView.vue:172
#: src/views/firewall/IpRuleView.vue:214
#: src/views/firewall/RuleView.vue:251
#: src/views/website/IndexView.vue:357
msgid "Batch Delete"
msgstr "批量删除"

#: src/views/firewall/ForwardView.vue:175
#: src/views/firewall/IpRuleView.vue:217
#: src/views/firewall/RuleView.vue:254
msgid "Are you sure you want to batch delete?"
msgstr "您确定要批量删除吗？"

#: src/views/firewall/IndexView.vue:19
msgid "Port Rules"
msgstr "端口规则"

#: src/views/firewall/IndexView.vue:22
msgid "IP Rules"
msgstr "IP 规则"

#: src/views/firewall/IndexView.vue:25
msgid "Port Forwarding"
msgstr "端口转发"

#: src/views/firewall/IpRuleView.vue:75
#: src/views/firewall/RuleView.vue:109
msgid "Mark"
msgstr "标记"

#: src/views/firewall/RuleView.vue:74
msgid "In Use"
msgstr "使用中"

#: src/views/firewall/RuleView.vue:76
msgid "Not Used"
msgstr "未使用"

#: src/views/firewall/RuleView.vue:151
msgid "All"
msgstr "所有"

#: src/views/firewall/SettingView.vue:27
#: src/views/firewall/SettingView.vue:33
#: src/views/firewall/SettingView.vue:39
msgid "Settings saved successfully"
msgstr "设置保存成功"

#: src/views/firewall/SettingView.vue:46
msgid "System Firewall"
msgstr "系统防火墙"

#: src/views/firewall/SettingView.vue:49
msgid "SSH Switch"
msgstr "SSH 开关"

#: src/views/firewall/SettingView.vue:52
msgid "Allow Ping"
msgstr "允许 Ping"

#: src/views/firewall/SettingView.vue:55
msgid "SSH Port"
msgstr "SSH 端口"

#: src/views/login/IndexView.vue:49
msgid "Please enter username and password"
msgstr "请输入用户名和密码"

#: src/views/login/IndexView.vue:54
msgid "Failed to get encryption public key, please refresh the page and try again"
msgstr "获取加密公钥失败，请刷新页面后重试"

#: src/views/login/IndexView.vue:67
msgid "Login successful!"
msgstr "登录成功！"

#: src/views/login/IndexView.vue:153
msgid "2FA Code"
msgstr "两步验证代码"

#: src/views/login/IndexView.vue:161
msgid "Safe Login"
msgstr "安全登录"

#: src/views/login/IndexView.vue:162
msgid "Remember Me"
msgstr "记住我"

#: src/views/login/IndexView.vue:176
msgid "Login"
msgstr "登录"

#: src/views/monitor/IndexView.vue:60
msgid "Load"
msgstr "负载"

#: src/views/monitor/IndexView.vue:71
#: src/views/monitor/IndexView.vue:87
msgid "1 minute"
msgstr "1 分钟"

#: src/views/monitor/IndexView.vue:71
#: src/views/monitor/IndexView.vue:102
msgid "5 minutes"
msgstr "5 分钟"

#: src/views/monitor/IndexView.vue:71
#: src/views/monitor/IndexView.vue:124
msgid "15 minutes"
msgstr "15 分钟"

#: src/views/monitor/IndexView.vue:162
msgid "Unit %"
msgstr "单位 %"

#: src/views/monitor/IndexView.vue:221
#: src/views/monitor/IndexView.vue:307
msgid "Unit MB"
msgstr "单位 MB"

#: src/views/monitor/IndexView.vue:298
#: src/views/monitor/IndexView.vue:322
msgid "Total Out"
msgstr "总出流量"

#: src/views/monitor/IndexView.vue:299
#: src/views/monitor/IndexView.vue:344
msgid "Total In"
msgstr "总入流量"

#: src/views/monitor/IndexView.vue:300
#: src/views/monitor/IndexView.vue:366
msgid "Per Second Out"
msgstr "每秒出流量"

#: src/views/monitor/IndexView.vue:301
#: src/views/monitor/IndexView.vue:388
msgid "Per Second In"
msgstr "每秒入流量"

#: src/views/monitor/IndexView.vue:414
#: src/views/monitor/IndexView.vue:420
msgid "Operation successful"
msgstr "操作成功"

#: src/views/monitor/IndexView.vue:451
msgid "Clear Monitoring Records"
msgstr "清除监控记录"

#: src/views/monitor/IndexView.vue:454
#: src/views/website/EditView.vue:224
msgid "Are you sure you want to clear?"
msgstr "确定要清除吗？"

#: src/views/monitor/IndexView.vue:465
msgid "Enable Monitoring"
msgstr "启用监控"

#: src/views/monitor/IndexView.vue:468
msgid "Save Days"
msgstr "保存天数"

#: src/views/monitor/IndexView.vue:470
msgid "days"
msgstr "天"

#: src/views/monitor/IndexView.vue:476
msgid "Time Selection"
msgstr "时间选择"

#: src/views/setting/CreateModal.vue:44
msgid "Enter user name"
msgstr "输入用户名"

#: src/views/setting/CreateModal.vue:53
#: src/views/setting/PasswordModal.vue:40
msgid "Enter user password"
msgstr "输入用户密码"

#: src/views/setting/CreateModal.vue:60
msgid "Enter user email"
msgstr "输入用户邮箱"

#: src/views/setting/IndexView.vue:47
msgid "Panel is restarting, page will refresh in 3 seconds"
msgstr "面板正在重启，页面将在 3 秒后刷新"

#: src/views/setting/IndexView.vue:73
msgid "Basic"
msgstr "基本"

#: src/views/setting/IndexView.vue:76
msgid "Safe"
msgstr "安全"

#: src/views/setting/PasswordModal.vue:16
#: src/views/setting/TokenModal.vue:183
#: src/views/setting/TwoFaModal.vue:24
#: src/views/ssh/UpdateModal.vue:30
msgid "Updated successfully"
msgstr "更新成功"

#: src/views/setting/SettingBase.vue:20
msgid "Stable"
msgstr "稳定版"

#: src/views/setting/SettingBase.vue:24
msgid "Beta"
msgstr "测试版"

#: src/views/setting/SettingBase.vue:34
msgid "Modifying panel port/entrance requires corresponding changes in the browser address bar to access the panel!"
msgstr "修改面板端口/入口需要在浏览器地址栏中相应更改以访问面板！"

#: src/views/setting/SettingBase.vue:40
#: src/views/setting/SettingBase.vue:41
msgid "Panel Name"
msgstr "面板名称"

#: src/views/setting/SettingBase.vue:43
msgid "Language"
msgstr "语言"

#: src/views/setting/SettingBase.vue:46
msgid "Update Channel"
msgstr "更新渠道"

#: src/views/setting/SettingBase.vue:50
msgid "8888"
msgstr "8888"

#: src/views/setting/SettingBase.vue:52
msgid "Default Website Directory"
msgstr "默认网站目录"

#: src/views/setting/SettingBase.vue:53
msgid "/www/wwwroot"
msgstr "/www/wwwroot"

#: src/views/setting/SettingBase.vue:55
msgid "Default Backup Directory"
msgstr "默认备份目录"

#: src/views/setting/SettingBase.vue:56
msgid "/www/backup"
msgstr "/www/backup"

#: src/views/setting/SettingSafe.vue:12
msgid "Login Timeout"
msgstr "登录超时"

#: src/views/setting/SettingSafe.vue:15
msgid "120"
msgstr "120"

#: src/views/setting/SettingSafe.vue:21
#: src/views/website/ProxyBuilderModal.vue:188
msgid "minutes"
msgstr "分钟"

#: src/views/setting/SettingSafe.vue:25
msgid "Access Entrance"
msgstr "访问入口"

#: src/views/setting/SettingSafe.vue:26
msgid "admin"
msgstr "admin"

#: src/views/setting/SettingSafe.vue:28
msgid "Bind Domain"
msgstr "绑定域名"

#: src/views/setting/SettingSafe.vue:35
msgid "Bind IP"
msgstr "绑定 IP"

#: src/views/setting/SettingSafe.vue:38
msgid "Bind UA"
msgstr "绑定 UA"

#: src/views/setting/SettingSafe.vue:45
msgid "Offline Mode"
msgstr "离线模式"

#: src/views/setting/SettingSafe.vue:48
msgid "Auto Update"
msgstr "自动更新"

#: src/views/setting/SettingSafe.vue:51
msgid "Panel HTTPS"
msgstr "面板 HTTPS"

#: src/views/setting/SettingUser.vue:43
msgid "2FA"
msgstr "两步验证"

#: src/views/setting/SettingUser.vue:58
msgid "Disabled successfully"
msgstr "禁用成功"

#: src/views/setting/SettingUser.vue:93
#: src/views/setting/TokenModal.vue:204
msgid "Access Tokens"
msgstr "访问令牌"

#: src/views/setting/SettingUser.vue:121
msgid "Are you sure you want to delete this user?"
msgstr "您确定要删除该用户吗？"

#: src/views/setting/TokenModal.vue:27
msgid "ID"
msgstr "ID"

#: src/views/setting/TokenModal.vue:81
msgid "Are you sure you want to delete this access token?"
msgstr "您确定要删除此访问令牌吗？"

#: src/views/setting/TokenModal.vue:144
msgid "Token is only displayed once, please save it before closing the dialog."
msgstr "令牌只显示一次，请在关闭对话框前保存。"

#: src/views/setting/TokenModal.vue:161
msgid "Copy and close"
msgstr "复制并关闭"

#: src/views/setting/TokenModal.vue:168
msgid "Copy failed"
msgstr "复制失败"

#: src/views/setting/TokenModal.vue:214
#: src/views/setting/TokenModal.vue:242
msgid "Create Access Token"
msgstr "创建访问令牌"

#: src/views/setting/TokenModal.vue:251
#: src/views/setting/TokenModal.vue:284
msgid "IP White List"
msgstr "IP 白名单"

#: src/views/setting/TokenModal.vue:254
#: src/views/setting/TokenModal.vue:287
msgid "127.0.0.1"
msgstr "127.0.0.1"

#: src/views/setting/TokenModal.vue:262
#: src/views/setting/TokenModal.vue:295
msgid "Please select the expiration time"
msgstr "请选择到期时间"

#: src/views/setting/TokenModal.vue:275
msgid "Modify Access Token"
msgstr "修改访问令牌"

#: src/views/setting/TwoFaModal.vue:46
msgid "Enable 2FA"
msgstr "启用两步验证"

#: src/views/setting/TwoFaModal.vue:57
msgid "QR Code"
msgstr "二维码"

#: src/views/setting/TwoFaModal.vue:62
msgid "Scan the QR code with your 2FA app and enter the code below"
msgstr "用您的两步验证应用扫描二维码并在下方输入代码"

#: src/views/setting/TwoFaModal.vue:66
msgid "If you cannot scan the QR code, please enter the URL below in your 2FA app"
msgstr "如果您不能扫描二维码，请在您的两步验证应用中输入下面的 URL"

#: src/views/setting/TwoFaModal.vue:79
msgid "Code"
msgstr "代码"

#: src/views/setting/TwoFaModal.vue:83
msgid "Enter the code"
msgstr "输入代码"

#: src/views/ssh/CreateModal.vue:50
#: src/views/ssh/IndexView.vue:231
msgid "Create Host"
msgstr "创建主机"

#: src/views/ssh/CreateModal.vue:73
#: src/views/ssh/UpdateModal.vue:79
msgid "Authentication Method"
msgstr "认证方式"

#: src/views/ssh/CreateModal.vue:92
#: src/views/ssh/UpdateModal.vue:98
msgid "Remarks"
msgstr "备注"

#: src/views/ssh/IndexView.vue:43
msgid "Please create a host first"
msgstr "请先创建一个主机"

#: src/views/ssh/IndexView.vue:82
msgid "Are you sure you want to delete this host?"
msgstr "您确定要删除这个主机吗？"

#: src/views/ssh/IndexView.vue:162
msgid "Connection closed. Please refresh."
msgstr "连接已关闭。请刷新。"

#: src/views/ssh/IndexView.vue:167
msgid "Connection error. Please refresh."
msgstr "连接错误。请刷新。"

#: src/views/ssh/UpdateModal.vue:56
msgid "Update Host"
msgstr "更新主机"

#: src/views/task/CreateModal.vue:22
msgid "# Enter your script content here"
msgstr "# 在此处输入您的脚本内容"

#: src/views/task/CreateModal.vue:89
msgid "Create Scheduled Task"
msgstr "创建计划任务"

#: src/views/task/CreateModal.vue:96
#: src/views/task/CronView.vue:36
msgid "Task Type"
msgstr "任务类型"

#: src/views/task/CreateModal.vue:100
#: src/views/task/CronView.vue:49
msgid "Run Script"
msgstr "运行脚本"

#: src/views/task/CreateModal.vue:101
#: src/views/task/CronView.vue:51
msgid "Backup Data"
msgstr "备份数据"

#: src/views/task/CreateModal.vue:102
#: src/views/task/CronView.vue:52
msgid "Log Rotation"
msgstr "日志切割"

#: src/views/task/CreateModal.vue:107
#: src/views/task/CreateModal.vue:108
#: src/views/task/CronView.vue:29
#: src/views/task/CronView.vue:257
#: src/views/task/CronView.vue:258
#: src/views/task/TaskView.vue:15
msgid "Task Name"
msgstr "任务名称"

#: src/views/task/CreateModal.vue:110
#: src/views/task/CronView.vue:73
#: src/views/task/CronView.vue:260
msgid "Task Schedule"
msgstr "任务计划"

#: src/views/task/CreateModal.vue:114
msgid "Script Content"
msgstr "脚本内容"

#: src/views/task/CreateModal.vue:128
msgid "Backup Type"
msgstr "备份类型"

#: src/views/task/CreateModal.vue:132
msgid "MySQL Database"
msgstr "MySQL 数据库"

#: src/views/task/CreateModal.vue:135
msgid "PostgreSQL Database"
msgstr "PostgreSQL 数据库"

#: src/views/task/CreateModal.vue:164
msgid "Retention Count"
msgstr "保留数量"

#: src/views/task/CronView.vue:59
msgid "Enabled"
msgstr "已启用"

#: src/views/task/CronView.vue:93
msgid "Last Update Time"
msgstr "最后更新时间"

#: src/views/task/CronView.vue:144
msgid "Are you sure you want to delete this task?"
msgstr "您确定要删除此任务吗？"

#: src/views/task/CronView.vue:249
msgid "Edit Task"
msgstr "编辑任务"

#: src/views/task/IndexView.vue:25
msgid "Create Task"
msgstr "创建任务"

#: src/views/task/IndexView.vue:32
msgid "System Processes"
msgstr "系统进程"

#: src/views/task/IndexView.vue:35
msgid "Panel Tasks"
msgstr "面板任务"

#: src/views/task/SystemView.vue:25
msgid "Parent PID"
msgstr "父进程 PID"

#: src/views/task/SystemView.vue:31
msgid "Threads"
msgstr "线程"

#: src/views/task/SystemView.vue:52
msgid "Sleeping"
msgstr "睡眠"

#: src/views/task/SystemView.vue:56
msgid "Idle"
msgstr "空闲"

#: src/views/task/SystemView.vue:58
msgid "Zombie"
msgstr "僵尸进程"

#: src/views/task/SystemView.vue:60
#: src/views/task/TaskView.vue:30
msgid "Waiting"
msgstr "等待中"

#: src/views/task/SystemView.vue:62
msgid "Locked"
msgstr "已锁定"

#: src/views/task/SystemView.vue:87
msgid "Start Time"
msgstr "启动时间"

#: src/views/task/SystemView.vue:108
msgid "Process %{ pid } has been terminated"
msgstr "进程 %{ pid } 已被终止"

#: src/views/task/SystemView.vue:115
msgid "Are you sure you want to terminate process %{ pid }?"
msgstr "您确定要终止进程 %{ pid } 吗？"

#: src/views/task/SystemView.vue:127
msgid "Terminate"
msgstr "终止"

#: src/views/task/TaskView.vue:28
msgid "Completed"
msgstr "已完成"

#: src/views/task/TaskView.vue:32
msgid "Failed"
msgstr "失败"

#: src/views/task/TaskView.vue:46
msgid "Completion Time"
msgstr "完成时间"

#: src/views/task/TaskView.vue:136
msgid "If logs cannot be loaded, please disable ad blockers!"
msgstr "如果无法加载日志，请禁用广告拦截器！"

#: src/views/toolbox/BenchmarkView.vue:12
#: src/views/toolbox/BenchmarkView.vue:123
msgid "CPU"
msgstr "CPU"

#: src/views/toolbox/BenchmarkView.vue:43
#: src/views/toolbox/BenchmarkView.vue:44
#: src/views/toolbox/BenchmarkView.vue:50
#: src/views/toolbox/BenchmarkView.vue:51
#: src/views/toolbox/BenchmarkView.vue:54
#: src/views/toolbox/BenchmarkView.vue:55
#: src/views/toolbox/BenchmarkView.vue:58
#: src/views/toolbox/BenchmarkView.vue:59
#: src/views/toolbox/BenchmarkView.vue:114
#: src/views/toolbox/BenchmarkView.vue:179
#: src/views/toolbox/BenchmarkView.vue:210
msgid "Pending benchmark"
msgstr "待跑分"

#: src/views/toolbox/BenchmarkView.vue:90
msgid "Benchmark results are for reference only and may differ from actual performance due to system resource scheduling, caching, and other factors!"
msgstr "跑分结果仅供参考，由于系统资源调度、缓存和其他因素，可能与实际性能有所不同！"

#: src/views/toolbox/BenchmarkView.vue:97
msgid "Benchmarking in progress, it may take some time..."
msgstr "跑分正在进行中，可能需要一些时间……"

#: src/views/toolbox/BenchmarkView.vue:100
msgid "Current project: %{ current }"
msgstr "当前项目：%{ current }"

#: src/views/toolbox/BenchmarkView.vue:128
msgid "Image Processing"
msgstr "图像处理"

#: src/views/toolbox/BenchmarkView.vue:134
msgid "Machine Learning"
msgstr "机器学习"

#: src/views/toolbox/BenchmarkView.vue:140
msgid "Program Compilation"
msgstr "程序编译"

#: src/views/toolbox/BenchmarkView.vue:146
msgid "AES Encryption"
msgstr "AES 加密"

#: src/views/toolbox/BenchmarkView.vue:152
msgid "Compression/Decompression"
msgstr "压缩/解压缩"

#: src/views/toolbox/BenchmarkView.vue:158
msgid "Physics Simulation"
msgstr "物理模拟"

#: src/views/toolbox/BenchmarkView.vue:164
msgid "JSON Parsing"
msgstr "JSON 解析"

#: src/views/toolbox/BenchmarkView.vue:193
msgid "Memory Bandwidth"
msgstr "内存带宽"

#: src/views/toolbox/BenchmarkView.vue:197
msgid "Memory Latency"
msgstr "内存延迟"

#: src/views/toolbox/BenchmarkView.vue:224
msgid "4KB Read"
msgstr "4KB 读取"

#: src/views/toolbox/BenchmarkView.vue:230
msgid "4KB Write"
msgstr "4KB 写入"

#: src/views/toolbox/BenchmarkView.vue:236
msgid "64KB Read"
msgstr "64KB 读取"

#: src/views/toolbox/BenchmarkView.vue:242
msgid "64KB Write"
msgstr "64KB 写入"

#: src/views/toolbox/BenchmarkView.vue:248
msgid "1MB Read"
msgstr "1MB 读取"

#: src/views/toolbox/BenchmarkView.vue:254
msgid "1MB Write"
msgstr "1MB 写入"

#: src/views/toolbox/BenchmarkView.vue:273
msgid "Benchmarking..."
msgstr "跑分中……"

#: src/views/toolbox/BenchmarkView.vue:273
msgid "Start Benchmark"
msgstr "开始跑分"

#: src/views/toolbox/SystemView.vue:125
msgid "DNS modifications will revert to default after system restart."
msgstr "DNS 修改将在系统重启后恢复为默认设置。"

#: src/views/toolbox/SystemView.vue:141
msgid "Total %{ total }, used %{ used }, free %{ free }"
msgstr "总计 %{ total }，已使用 %{ used }，可用 %{ free }"

#: src/views/toolbox/SystemView.vue:159
msgid "Hostname"
msgstr "主机名"

#: src/views/toolbox/SystemView.vue:177
msgid "Time"
msgstr "时间"

#: src/views/toolbox/SystemView.vue:181
msgid "After manually changing the time, it may still be overwritten by system automatic time synchronization."
msgstr "手动更改时间后，系统自动时间同步可能仍会覆盖设置。"

#: src/views/toolbox/SystemView.vue:187
msgid "Select Timezone"
msgstr "选择时区"

#: src/views/toolbox/SystemView.vue:190
msgid "Please select a timezone"
msgstr "请选择时区"

#: src/views/toolbox/SystemView.vue:194
msgid "Modify Time"
msgstr "修改时间"

#: src/views/toolbox/SystemView.vue:197
msgid "NTP Time Synchronization"
msgstr "NTP 时间同步"

#: src/views/toolbox/SystemView.vue:199
msgid "Synchronize Time"
msgstr "同步时间"

#: src/views/website/BulkCreate.vue:21
msgid "The format is incorrect, please check"
msgstr "格式不正确，请检查"

#: src/views/website/BulkCreate.vue:59
#: src/views/website/IndexView.vue:295
msgid "Website %{ name } created successfully"
msgstr "网站 %{ name } 创建成功"

#: src/views/website/BulkCreate.vue:77
#: src/views/website/IndexView.vue:368
msgid "Bulk Create Website"
msgstr "批量创建网站"

#: src/views/website/BulkCreate.vue:88
msgid "Please enter the website name, domain, port, path, and remark in the text area below, one per line."
msgstr "请在下面的文本区域输入网站名称、域名、端口、路径和注释，每行一个。"

#: src/views/website/BulkCreate.vue:96
msgid "name|domain|port|path|remark"
msgstr "名称|域名|端口|路径|备注"

#: src/views/website/BulkCreate.vue:101
msgid "Name: The name of the website, which will be displayed in the website list, must be unique."
msgstr "名称：网站的名称，将显示在网站列表中，必须是唯一的。"

#: src/views/website/BulkCreate.vue:108
msgid "Domain: The domain name of the website, multiple domains can be separated by commas."
msgstr "域名：网站的域名，多个域名可以用英文逗号分隔。"

#: src/views/website/BulkCreate.vue:115
msgid "Port: The port number of the website, multiple ports can be separated by commas."
msgstr "端口：网站的端口号，多个端口可以用英文逗号分隔。"

#: src/views/website/BulkCreate.vue:121
msgid "Path: The path of the website, can be empty to use the default path."
msgstr "路径：网站的根目录，可以为空以使用默认路径。"

#: src/views/website/BulkCreate.vue:124
msgid "Remark: The remark of the website, can be empty."
msgstr "备注：网站的备注，可以为空。"

#: src/views/website/EditView.vue:54
#: src/views/website/IndexView.vue:209
msgid "Not used"
msgstr "未使用"

#: src/views/website/EditView.vue:83
msgid "Edit Website - %{ name }"
msgstr "编辑网站 - %{ name }"

#: src/views/website/EditView.vue:85
msgid "Edit Website"
msgstr "编辑网站"

#: src/views/website/EditView.vue:122
msgid "Reset successfully"
msgstr "重置成功"

#: src/views/website/EditView.vue:139
msgid "Issued successfully"
msgstr "签发成功"

#: src/views/website/EditView.vue:153
msgid "The selected certificate is invalid"
msgstr "所选证书无效"

#: src/views/website/EditView.vue:179
msgid "If you modify the original text, other modifications will not take effect after clicking save!"
msgstr "如果修改原文，点击保存后其他修改将不会生效！"

#: src/views/website/EditView.vue:188
msgid "Reset Configuration"
msgstr "重置配置"

#: src/views/website/EditView.vue:191
msgid "Are you sure you want to reset the configuration?"
msgstr "您确定要重置配置吗？"

#: src/views/website/EditView.vue:200
#: src/views/website/ProxyBuilderModal.vue:123
msgid "Generate Reverse Proxy Configuration"
msgstr "生成反向代理配置"

#: src/views/website/EditView.vue:211
msgid "One-click Certificate Issuance"
msgstr "一键签发证书"

#: src/views/website/EditView.vue:221
msgid "Clear Logs"
msgstr "清除日志"

#: src/views/website/EditView.vue:230
msgid "Domain & Listening"
msgstr "域名和监听"

#: src/views/website/EditView.vue:240
msgid "Listening Address"
msgstr "监听地址"

#: src/views/website/EditView.vue:258
msgid "Basic Settings"
msgstr "基本设置"

#: src/views/website/EditView.vue:260
msgid "Website Directory"
msgstr "网站目录"

#: src/views/website/EditView.vue:263
msgid "Enter website directory (absolute path)"
msgstr "输入网站目录（绝对路径）"

#: src/views/website/EditView.vue:266
msgid "Running Directory"
msgstr "运行目录"

#: src/views/website/EditView.vue:270
msgid "Enter running directory (needed for Laravel etc.) (absolute path)"
msgstr "输入运行目录（Laravel等需要）（绝对路径）"

#: src/views/website/EditView.vue:274
msgid "Default Document"
msgstr "默认文档"

#: src/views/website/EditView.vue:277
#: src/views/website/IndexView.vue:448
msgid "PHP Version"
msgstr "PHP 版本"

#: src/views/website/EditView.vue:282
#: src/views/website/IndexView.vue:452
msgid "Select PHP Version"
msgstr "选择 PHP 版本"

#: src/views/website/EditView.vue:287
msgid "Anti-cross-site Attack (PHP)"
msgstr "防跨站攻击 (PHP)"

#: src/views/website/EditView.vue:296
msgid "Certificate Information"
msgstr "证书信息"

#: src/views/website/EditView.vue:298
msgid "Certificate Validity"
msgstr "证书有效期"

#: src/views/website/EditView.vue:312
msgid "Domains"
msgstr "域名"

#: src/views/website/EditView.vue:327
msgid "Main Switch"
msgstr "主开关"

#: src/views/website/EditView.vue:333
msgid "Use Existing Certificate"
msgstr "使用现有证书"

#: src/views/website/EditView.vue:347
msgid "HTTP Redirect"
msgstr "HTTP 重定向"

#: src/views/website/EditView.vue:350
msgid "OCSP Stapling"
msgstr "OCSP 装订"

#: src/views/website/EditView.vue:375
msgid "Rewrite"
msgstr "伪静态"

#: src/views/website/EditView.vue:378
msgid "Presets"
msgstr "预设"

#: src/views/website/EditView.vue:405
msgid "If you do not understand the configuration rules, please do not modify them arbitrarily, otherwise it may cause the website to be inaccessible or panel function abnormalities! If you have already encountered a problem, try resetting the configuration!"
msgstr "如果您不了解配置规则，请不要随意修改，否则可能导致网站无法访问或面板功能异常！如果您已经遇到问题，请尝试重置配置！"

#: src/views/website/EditView.vue:424
msgid "Access Log"
msgstr "访问日志"

#: src/views/website/EditView.vue:428
#: src/views/website/EditView.vue:440
msgid "All logs can be viewed by downloading the file"
msgstr "所有日志可通过下载文件查看"

#: src/views/website/EditView.vue:430
#: src/views/website/EditView.vue:442
msgid "view"
msgstr "查看"

#: src/views/website/EditView.vue:436
msgid "Error Log"
msgstr "错误日志"

#: src/views/website/IndexView.vue:24
#: src/views/website/IndexView.vue:411
msgid "Website Name"
msgstr "网站名称"

#: src/views/website/IndexView.vue:77
#: src/views/website/IndexView.vue:529
#: src/views/website/IndexView.vue:534
msgid "Remark"
msgstr "备注"

#: src/views/website/IndexView.vue:133
msgid "Are you sure you want to delete website %{ name }?"
msgstr "您确定要删除网站 %{ name } 吗？"

#: src/views/website/IndexView.vue:144
msgid "Delete website directory"
msgstr "删除网站目录"

#: src/views/website/IndexView.vue:152
msgid "Delete local database with the same name"
msgstr "删除同名的本地数据库"

#: src/views/website/IndexView.vue:316
msgid "Please select the websites to delete"
msgstr "请选择要删除的网站"

#: src/views/website/IndexView.vue:351
#: src/views/website/IndexView.vue:545
msgid "Modify Default Page"
msgstr "修改默认页面"

#: src/views/website/IndexView.vue:361
msgid "This will delete the website directory but not the database with the same name. Are you sure you want to delete the selected websites?"
msgstr "这将删除网站目录，但不会删除同名的数据库。您确定要删除所选网站吗？"

#: src/views/website/IndexView.vue:372
#: src/views/website/IndexView.vue:402
msgid "Create Website"
msgstr "创建网站"

#: src/views/website/IndexView.vue:417
msgid "Recommended to use English for the website name, it cannot be modified after setting"
msgstr "建议使用英文作为网站名称，设置后无法修改"

#: src/views/website/IndexView.vue:464
msgid "Select Database"
msgstr "选择数据库"

#: src/views/website/IndexView.vue:492
#: src/views/website/IndexView.vue:497
msgid "Database User"
msgstr "数据库用户"

#: src/views/website/IndexView.vue:506
#: src/views/website/IndexView.vue:512
msgid "Database Password"
msgstr "数据库密码"

#: src/views/website/IndexView.vue:523
msgid "Website root directory (if left empty, defaults to website directory/website name)"
msgstr "网站根目录（如果留空，默认为网站目录/网站名称）"

#: src/views/website/IndexView.vue:553
#: src/views/website/IndexView.vue:553
msgid "Default Page"
msgstr "默认页面"

#: src/views/website/IndexView.vue:567
#: src/views/website/IndexView.vue:567
msgid "Stop Page"
msgstr "停止页面"

#: src/views/website/ProxyBuilderModal.vue:24
msgid "Disabled buffer and enabled cache cannot be used simultaneously"
msgstr "禁用缓冲区和启用缓存不能同时使用"

#: src/views/website/ProxyBuilderModal.vue:29
msgid "Matching expression cannot be empty"
msgstr "匹配表达式不能为空"

#: src/views/website/ProxyBuilderModal.vue:33
msgid "Proxy address cannot be empty"
msgstr "代理地址不能为空"

#: src/views/website/ProxyBuilderModal.vue:37
msgid "Exact match expression must start with /"
msgstr "精确匹配表达式必须以 / 开头"

#: src/views/website/ProxyBuilderModal.vue:44
msgid "Prefix match expression must start with /"
msgstr "前缀匹配表达式必须以 / 开头"

#: src/views/website/ProxyBuilderModal.vue:50
msgid "Proxy address format error"
msgstr "代理地址格式错误"

#: src/views/website/ProxyBuilderModal.vue:102
msgid "Configuration generated successfully"
msgstr "配置生成成功"

#: src/views/website/ProxyBuilderModal.vue:132
msgid "After generating the reverse proxy configuration, the original rewrite rules will be overwritten."
msgstr "生成反向代理配置后，原有的伪静态规则将被覆盖。"

#: src/views/website/ProxyBuilderModal.vue:139
msgid "If you need to proxy static resources like JS/CSS, please remove the static log recording part from the original configuration."
msgstr "如果您需要代理JS/CSS等静态资源，请从原始配置中删除静态日志记录部分。"

#: src/views/website/ProxyBuilderModal.vue:145
msgid "Auto Refresh Resolution"
msgstr "自动刷新解析"

#: src/views/website/ProxyBuilderModal.vue:148
msgid "Enable SNI"
msgstr "启用 SNI"

#: src/views/website/ProxyBuilderModal.vue:151
msgid "Enable Cache"
msgstr "启用缓存"

#: src/views/website/ProxyBuilderModal.vue:154
msgid "Disable Buffer"
msgstr "禁用缓冲区"

#: src/views/website/ProxyBuilderModal.vue:159
msgid "Match Type"
msgstr "匹配类型"

#: src/views/website/ProxyBuilderModal.vue:163
msgid "Exact Match (=)"
msgstr "精确匹配 (=)"

#: src/views/website/ProxyBuilderModal.vue:164
msgid "Priority Prefix Match (^~)"
msgstr "优先前缀匹配 (^~)"

#: src/views/website/ProxyBuilderModal.vue:165
msgid "Normal Prefix Match ( )"
msgstr "普通前缀匹配 ( )"

#: src/views/website/ProxyBuilderModal.vue:166
msgid "Case Sensitive Regex Match (~)"
msgstr "区分大小写的正则匹配 (~)"

#: src/views/website/ProxyBuilderModal.vue:167
msgid "Case Insensitive Regex Match (~*)"
msgstr "不区分大小写的正则匹配 (~*)"

#: src/views/website/ProxyBuilderModal.vue:171
msgid "Match Expression"
msgstr "匹配表达式"

#: src/views/website/ProxyBuilderModal.vue:174
msgid "Proxy Address"
msgstr "代理地址"

#: src/views/website/ProxyBuilderModal.vue:177
msgid "Send Domain"
msgstr "发送域名"

#: src/views/website/ProxyBuilderModal.vue:180
msgid "Cache Time"
msgstr "缓存时间"

#: src/views/website/ProxyBuilderModal.vue:186
msgid "Cache time (minutes)"
msgstr "缓存时间（分钟）"

#: src/views/website/ProxyBuilderModal.vue:191
msgid "Content Replacement"
msgstr "内容替换"

#: src/views/website/ProxyBuilderModal.vue:196
msgid "Target content"
msgstr "目标内容"

#: src/views/website/ProxyBuilderModal.vue:197
msgid "Replacement content"
msgstr "替换内容"

