msgid ""
msgstr ""
"Content-Type: text/plain; charset=UTF-8\n"

#: src/components/common/AppFooter.vue:13
#: src/views/dashboard/IndexView.vue:439
msgid "Rat Panel"
msgstr ""

#: src/components/common/AppFooter.vue:15
msgid "All Rights Reserved."
msgstr ""

#: src/components/common/AppFooter.vue:23
msgid "QQ Group"
msgstr ""

#: src/components/common/AppFooter.vue:31
msgid "Documentation"
msgstr ""

#: src/components/common/AppFooter.vue:39
msgid "Community"
msgstr ""

#: src/components/common/AppFooter.vue:47
msgid "Sponsor"
msgstr ""

#: src/components/common/CodeEditor.vue:27
msgid "Retrieved successfully"
msgstr ""

#: src/components/common/CodeEditor.vue:36
msgid "Cannot save in current state"
msgstr ""

#: src/components/common/CodeEditor.vue:40
#: src/views/apps/codeserver/IndexView.vue:24
#: src/views/apps/docker/IndexView.vue:24
#: src/views/apps/fail2ban/IndexView.vue:164
#: src/views/apps/frp/IndexView.vue:28
#: src/views/apps/gitea/IndexView.vue:22
#: src/views/apps/memcached/IndexView.vue:44
#: src/views/apps/minio/IndexView.vue:22
#: src/views/apps/mysql/IndexView.vue:47
#: src/views/apps/nginx/IndexView.vue:44
#: src/views/apps/php/PhpView.vue:146
#: src/views/apps/php/PhpView.vue:152
#: src/views/apps/phpmyadmin/IndexView.vue:37
#: src/views/apps/phpmyadmin/IndexView.vue:44
#: src/views/apps/podman/IndexView.vue:26
#: src/views/apps/podman/IndexView.vue:32
#: src/views/apps/postgresql/IndexView.vue:47
#: src/views/apps/postgresql/IndexView.vue:52
#: src/views/apps/pureftpd/IndexView.vue:117
#: src/views/apps/redis/IndexView.vue:41
#: src/views/apps/rsync/IndexView.vue:136
#: src/views/apps/rsync/IndexView.vue:180
#: src/views/apps/supervisor/IndexView.vue:227
#: src/views/apps/supervisor/IndexView.vue:292
#: src/views/setting/IndexView.vue:44
#: src/views/toolbox/SystemView.vue:51
#: src/views/toolbox/SystemView.vue:57
#: src/views/toolbox/SystemView.vue:66
#: src/views/toolbox/SystemView.vue:72
#: src/views/toolbox/SystemView.vue:81
#: src/views/website/EditView.vue:115
msgid "Saved successfully"
msgstr ""

#: src/components/common/PathSelector.vue:20
msgid "Select Directory"
msgstr ""

#: src/components/common/PathSelector.vue:20
msgid "Select File"
msgstr ""

#: src/components/common/PathSelector.vue:42
#: src/components/common/PathSelector.vue:331
#: src/views/apps/fail2ban/IndexView.vue:38
#: src/views/apps/rsync/IndexView.vue:40
#: src/views/apps/rsync/IndexView.vue:280
#: src/views/apps/supervisor/IndexView.vue:48
#: src/views/apps/supervisor/IndexView.vue:399
#: src/views/container/ComposeView.vue:31
#: src/views/container/NetworkView.vue:45
#: src/views/container/VolumeView.vue:26
#: src/views/database/CreateServerModal.vue:55
#: src/views/database/ServerList.vue:40
#: src/views/database/UpdateServerModal.vue:55
#: src/views/file/ListTable.vue:103
#: src/views/file/SearchModal.vue:20
#: src/views/file/ToolBar.vue:257
#: src/views/ssh/CreateModal.vue:57
#: src/views/ssh/UpdateModal.vue:63
#: src/views/task/SystemView.vue:18
msgid "Name"
msgstr ""

#: src/components/common/PathSelector.vue:83
msgid "Permissions"
msgstr ""

#: src/components/common/PathSelector.vue:95
#: src/views/file/ListTable.vue:157
msgid "Owner / Group"
msgstr ""

#: src/components/common/PathSelector.vue:107
#: src/views/backup/ListView.vue:43
#: src/views/container/ImageView.vue:53
#: src/views/file/ListTable.vue:169
#: src/views/file/SearchModal.vue:28
msgid "Size"
msgstr ""

#: src/components/common/PathSelector.vue:115
#: src/views/file/ListTable.vue:177
#: src/views/file/SearchModal.vue:36
msgid "Modification Time"
msgstr ""

#: src/components/common/PathSelector.vue:148
#: src/views/file/ListTable.vue:456
#: src/views/file/PathInput.vue:33
msgid "Invalid path"
msgstr ""

#: src/components/common/PathSelector.vue:208
#: src/views/file/ListTable.vue:405
#: src/views/file/ToolBar.vue:37
#: src/views/file/ToolBar.vue:51
msgid "Invalid name"
msgstr ""

#: src/components/common/PathSelector.vue:216
#: src/views/backup/ListView.vue:125
#: src/views/cert/CreateAccountModal.vue:52
#: src/views/cert/CreateCertModal.vue:50
#: src/views/cert/CreateDnsModal.vue:35
#: src/views/cert/UploadCertModal.vue:21
#: src/views/container/ComposeView.vue:246
#: src/views/container/ContainerCreate.vue:106
#: src/views/container/NetworkView.vue:174
#: src/views/container/VolumeView.vue:128
#: src/views/database/CreateDatabaseModal.vue:28
#: src/views/database/CreateUserModal.vue:28
#: src/views/file/ToolBar.vue:45
#: src/views/firewall/CreateForwardModal.vue:41
#: src/views/firewall/CreateModal.vue:84
#: src/views/setting/CreateModal.vue:19
#: src/views/setting/TokenModal.vue:127
#: src/views/ssh/CreateModal.vue:38
#: src/views/task/CreateModal.vue:51
msgid "Created successfully"
msgstr ""

#: src/components/common/PathSelector.vue:255
#: src/views/file/ToolBar.vue:214
msgid "File"
msgstr ""

#: src/components/common/PathSelector.vue:256
#: src/views/file/ToolBar.vue:215
msgid "Folder"
msgstr ""

#: src/components/common/PathSelector.vue:260
#: src/components/common/PathSelector.vue:323
#: src/views/setting/TokenModal.vue:268
#: src/views/website/BulkCreate.vue:127
#: src/views/website/IndexView.vue:539
msgid "Create"
msgstr ""

#: src/components/common/PathSelector.vue:269
#: src/views/file/PathInput.vue:133
msgid "Root Directory"
msgstr ""

#: src/components/common/PathSelector.vue:335
#: src/views/app/VersionModal.vue:96
#: src/views/apps/fail2ban/IndexView.vue:375
#: src/views/apps/pureftpd/IndexView.vue:249
#: src/views/apps/pureftpd/IndexView.vue:271
#: src/views/apps/rsync/IndexView.vue:329
#: src/views/apps/s3fs/IndexView.vue:184
#: src/views/apps/supervisor/IndexView.vue:435
#: src/views/backup/ListView.vue:244
#: src/views/backup/ListView.vue:268
#: src/views/cert/AccountView.vue:268
#: src/views/cert/CertView.vue:535
#: src/views/cert/CertView.vue:559
#: src/views/cert/CreateAccountModal.vue:125
#: src/views/cert/CreateCertModal.vue:115
#: src/views/cert/CreateDnsModal.vue:237
#: src/views/cert/DnsView.vue:373
#: src/views/cert/ObtainModal.vue:130
#: src/views/cert/UploadCertModal.vue:55
#: src/views/container/ComposeView.vue:340
#: src/views/container/ComposeView.vue:370
#: src/views/container/ContainerCreate.vue:370
#: src/views/container/ContainerView.vue:481
#: src/views/container/ImageView.vue:217
#: src/views/container/NetworkView.vue:313
#: src/views/container/VolumeView.vue:213
#: src/views/database/CreateDatabaseModal.vue:131
#: src/views/database/CreateServerModal.vue:120
#: src/views/database/CreateUserModal.vue:118
#: src/views/database/UpdateServerModal.vue:112
#: src/views/database/UpdateUserModal.vue:73
#: src/views/file/ToolBar.vue:261
#: src/views/file/ToolBar.vue:282
#: src/views/firewall/CreateForwardModal.vue:88
#: src/views/firewall/CreateIpModal.vue:122
#: src/views/firewall/CreateModal.vue:147
#: src/views/setting/CreateModal.vue:64
#: src/views/setting/PasswordModal.vue:44
#: src/views/setting/TwoFaModal.vue:87
#: src/views/ssh/CreateModal.vue:99
#: src/views/ssh/UpdateModal.vue:105
#: src/views/task/CreateModal.vue:171
#: src/views/website/ProxyBuilderModal.vue:201
msgid "Submit"
msgstr ""

#: src/components/common/RealtimeLog.vue:29
msgid "Path or service cannot be empty"
msgstr ""

#: src/components/common/RealtimeLog.vue:44
#: src/components/common/RealtimeLogModal.vue:33
msgid "Failed to get log stream"
msgstr ""

#: src/components/common/RealtimeLogModal.vue:67
#: src/views/apps/supervisor/IndexView.vue:91
#: src/views/container/ContainerView.vue:102
#: src/views/container/ContainerView.vue:442
#: src/views/task/CronView.vue:120
#: src/views/task/TaskView.vue:74
msgid "Logs"
msgstr ""

#: src/components/common/ServiceStatus.vue:25
#: src/views/dashboard/IndexView.vue:733
#: src/views/dashboard/IndexView.vue:741
#: src/views/dashboard/IndexView.vue:748
#: src/views/dashboard/IndexView.vue:754
#: src/views/dashboard/IndexView.vue:765
#: src/views/dashboard/IndexView.vue:777
msgid "Loading..."
msgstr ""

#: src/components/common/ServiceStatus.vue:26
#: src/views/task/SystemView.vue:50
#: src/views/task/TaskView.vue:33
#: src/views/website/IndexView.vue:31
msgid "Running"
msgstr ""

#: src/components/common/ServiceStatus.vue:26
#: src/views/task/SystemView.vue:54
msgid "Stopped"
msgstr ""

#: src/components/common/ServiceStatus.vue:42
#: src/views/container/ComposeView.vue:104
msgid "Starting..."
msgstr ""

#: src/components/common/ServiceStatus.vue:48
#: src/views/apps/supervisor/IndexView.vue:248
#: src/views/website/IndexView.vue:239
msgid "Started successfully"
msgstr ""

#: src/components/common/ServiceStatus.vue:57
msgid "Stopping..."
msgstr ""

#: src/components/common/ServiceStatus.vue:63
#: src/views/apps/supervisor/IndexView.vue:255
#: src/views/website/IndexView.vue:241
msgid "Stopped successfully"
msgstr ""

#: src/components/common/ServiceStatus.vue:72
msgid "Restarting..."
msgstr ""

#: src/components/common/ServiceStatus.vue:78
#: src/views/apps/supervisor/IndexView.vue:262
msgid "Restarted successfully"
msgstr ""

#: src/components/common/ServiceStatus.vue:87
msgid "Reloading..."
msgstr ""

#: src/components/common/ServiceStatus.vue:93
msgid "Reloaded successfully"
msgstr ""

#: src/components/common/ServiceStatus.vue:102
msgid "Setting autostart..."
msgstr ""

#: src/components/common/ServiceStatus.vue:109
msgid "Autostart enabled successfully"
msgstr ""

#: src/components/common/ServiceStatus.vue:118
msgid "Autostart disabled successfully"
msgstr ""

#: src/components/common/ServiceStatus.vue:134
#: src/views/apps/codeserver/IndexView.vue:43
#: src/views/apps/docker/IndexView.vue:43
#: src/views/apps/fail2ban/IndexView.vue:252
#: src/views/apps/gitea/IndexView.vue:41
#: src/views/apps/memcached/IndexView.vue:63
#: src/views/apps/minio/IndexView.vue:36
#: src/views/apps/mysql/IndexView.vue:101
#: src/views/apps/nginx/IndexView.vue:78
#: src/views/apps/php/PhpView.vue:225
#: src/views/apps/podman/IndexView.vue:60
#: src/views/apps/postgresql/IndexView.vue:88
#: src/views/apps/pureftpd/IndexView.vue:175
#: src/views/apps/redis/IndexView.vue:60
#: src/views/apps/rsync/IndexView.vue:214
#: src/views/apps/supervisor/IndexView.vue:330
#: src/views/container/ContainerView.vue:80
msgid "Running Status"
msgstr ""

#: src/components/common/ServiceStatus.vue:141
msgid "Autostart On"
msgstr ""

#: src/components/common/ServiceStatus.vue:142
msgid "Autostart Off"
msgstr ""

#: src/components/common/ServiceStatus.vue:152
#: src/views/apps/supervisor/IndexView.vue:119
#: src/views/container/ComposeView.vue:158
#: src/views/container/ContainerView.vue:126
#: src/views/container/ContainerView.vue:408
msgid "Start"
msgstr ""

#: src/components/common/ServiceStatus.vue:158
#: src/views/apps/supervisor/IndexView.vue:145
#: src/views/container/ComposeView.vue:189
#: src/views/container/ContainerView.vue:131
#: src/views/container/ContainerView.vue:409
msgid "Stop"
msgstr ""

#: src/components/common/ServiceStatus.vue:161
msgid "Are you sure you want to stop %{ service }?"
msgstr ""

#: src/components/common/ServiceStatus.vue:165
#: src/views/apps/supervisor/IndexView.vue:174
#: src/views/container/ContainerView.vue:136
#: src/views/container/ContainerView.vue:410
#: src/views/dashboard/IndexView.vue:447
msgid "Restart"
msgstr ""

#: src/components/common/ServiceStatus.vue:174
#: src/layout/tab/components/ContextMenu.vue:34
msgid "Reload"
msgstr ""

#: src/layout/header/components/FullScreen.vue:16
msgid "Fullscreen Display"
msgstr ""

#: src/layout/header/components/MenuCollapse.vue:17
msgid "Menu Zoom"
msgstr ""

#: src/layout/header/components/ReloadPage.vue:20
msgid "Refresh Tab"
msgstr ""

#: src/layout/header/components/ThemeMode.vue:17
msgid "Switch Theme"
msgstr ""

#: src/layout/header/components/ThemeSetting.vue:19
msgid "Set Theme Color"
msgstr ""

#: src/layout/header/components/UserAvatar.vue:13
#: src/views/apps/pureftpd/IndexView.vue:65
#: src/views/apps/pureftpd/IndexView.vue:256
#: src/views/setting/PasswordModal.vue:26
#: src/views/setting/SettingUser.vue:109
msgid "Change Password"
msgstr ""

#: src/layout/header/components/UserAvatar.vue:18
msgid "Logout"
msgstr ""

#: src/layout/header/components/UserAvatar.vue:27
msgid "Confirm logout?"
msgstr ""

#: src/layout/header/components/UserAvatar.vue:28
msgid "Prompt"
msgstr ""

#: src/layout/header/components/UserAvatar.vue:29
#: src/views/dashboard/UpdateView.vue:26
#: src/views/monitor/IndexView.vue:474
msgid "Confirm"
msgstr ""

#: src/layout/header/components/UserAvatar.vue:30
#: src/views/dashboard/UpdateView.vue:27
#: src/views/file/ListTable.vue:415
#: src/views/file/ListTable.vue:510
#: src/views/file/ToolBar.vue:139
#: src/views/file/ToolBar.vue:226
msgid "Cancel"
msgstr ""

#: src/layout/header/components/UserAvatar.vue:35
msgid "Logged out successfully!"
msgstr ""

#: src/layout/header/components/UserAvatar.vue:48
#: src/views/cert/DnsView.vue:56
#: src/views/firewall/IpRuleView.vue:77
#: src/views/firewall/IpRuleView.vue:102
#: src/views/firewall/RuleView.vue:111
#: src/views/firewall/RuleView.vue:136
msgid "Unknown"
msgstr ""

#: src/layout/sidebar/components/SideSetting.vue:66
#: src/layout/sidebar/components/SideSetting.vue:71
msgid "Menu Settings"
msgstr ""

#: src/layout/sidebar/components/SideSetting.vue:83
msgid "Settings are saved in the browser and will be reset after clearing the browser cache"
msgstr ""

#: src/layout/sidebar/components/SideSetting.vue:88
msgid "Custom Logo"
msgstr ""

#: src/layout/sidebar/components/SideSetting.vue:91
msgid "Please enter the complete URL"
msgstr ""

#: src/layout/sidebar/components/SideSetting.vue:94
msgid "Hide Menu"
msgstr ""

#: src/layout/tab/components/ContextMenu.vue:28
msgid "Close"
msgstr ""

#: src/layout/tab/components/ContextMenu.vue:40
msgid "Pin"
msgstr ""

#: src/layout/tab/components/ContextMenu.vue:46
msgid "Unpin"
msgstr ""

#: src/layout/tab/components/ContextMenu.vue:52
msgid "Close Others"
msgstr ""

#: src/layout/tab/components/ContextMenu.vue:58
msgid "Close Left"
msgstr ""

#: src/layout/tab/components/ContextMenu.vue:64
msgid "Close Right"
msgstr ""

#: src/locales/menu.ts:7
msgid "Apps"
msgstr ""

#: src/locales/menu.ts:8
msgid "Backup"
msgstr ""

#: src/locales/menu.ts:9
#: src/views/cert/CertView.vue:497
#: src/views/cert/CertView.vue:573
#: src/views/cert/UploadCertModal.vue:38
#: src/views/setting/SettingSafe.vue:54
#: src/views/website/EditView.vue:355
msgid "Certificate"
msgstr ""

#: src/locales/menu.ts:10
msgid "Container"
msgstr ""

#: src/locales/menu.ts:11
msgid "Dashboard"
msgstr ""

#: src/locales/menu.ts:12
#: src/views/app/IndexView.vue:96
#: src/views/dashboard/IndexView.vue:451
#: src/views/setting/TokenModal.vue:301
msgid "Update"
msgstr ""

#: src/locales/menu.ts:13
#: src/views/backup/ListView.vue:264
#: src/views/dashboard/IndexView.vue:425
#: src/views/database/IndexView.vue:45
#: src/views/website/IndexView.vue:460
msgid "Database"
msgstr ""

#: src/locales/menu.ts:14
msgid "Files"
msgstr ""

#: src/locales/menu.ts:15
msgid "Firewall"
msgstr ""

#: src/locales/menu.ts:16
msgid "Monitoring"
msgstr ""

#: src/locales/menu.ts:17
#: src/views/firewall/IndexView.vue:28
msgid "Settings"
msgstr ""

#: src/locales/menu.ts:18
msgid "Terminal"
msgstr ""

#: src/locales/menu.ts:19
msgid "Tasks"
msgstr ""

#: src/locales/menu.ts:20
msgid "Toolbox"
msgstr ""

#: src/locales/menu.ts:21
msgid "System"
msgstr ""

#: src/locales/menu.ts:22
msgid "Benchmark"
msgstr ""

#: src/locales/menu.ts:23
#: src/views/apps/fail2ban/IndexView.vue:322
#: src/views/backup/IndexView.vue:37
#: src/views/backup/ListView.vue:220
#: src/views/backup/ListView.vue:257
#: src/views/cert/CertView.vue:466
#: src/views/cert/CertView.vue:549
#: src/views/cert/CreateCertModal.vue:90
#: src/views/dashboard/IndexView.vue:421
#: src/views/task/CreateModal.vue:130
msgid "Website"
msgstr ""

#: src/locales/menu.ts:24
msgid "Website Edit"
msgstr ""

#: src/locales/menu.ts:26
msgid "Fail2ban Manager"
msgstr ""

#: src/locales/menu.ts:27
msgid "S3fs Manager"
msgstr ""

#: src/locales/menu.ts:28
msgid "Supervisor Manager"
msgstr ""

#: src/locales/menu.ts:29
msgid "Rsync Manager"
msgstr ""

#: src/locales/menu.ts:30
msgid "Frp Manager"
msgstr ""

#: src/router/routes/index.ts:18
msgid "Login Page"
msgstr ""

#: src/views/app/IndexView.vue:19
#: src/views/app/IndexView.vue:154
#: src/views/app/IndexView.vue:159
#: src/views/apps/php/PhpView.vue:81
msgid "Install"
msgstr ""

#: src/views/app/IndexView.vue:37
msgid "App Name"
msgstr ""

#: src/views/app/IndexView.vue:43
#: src/views/apps/php/PhpView.vue:50
msgid "Description"
msgstr ""

#: src/views/app/IndexView.vue:49
msgid "Installed Version"
msgstr ""

#: src/views/app/IndexView.vue:55
msgid "Show in Home"
msgstr ""

#: src/views/app/IndexView.vue:68
#: src/views/apps/fail2ban/IndexView.vue:60
#: src/views/apps/fail2ban/IndexView.vue:121
#: src/views/apps/php/PhpView.vue:57
#: src/views/apps/pureftpd/IndexView.vue:46
#: src/views/apps/rsync/IndexView.vue:69
#: src/views/apps/s3fs/IndexView.vue:33
#: src/views/apps/supervisor/IndexView.vue:76
#: src/views/backup/ListView.vue:58
#: src/views/cert/AccountView.vue:79
#: src/views/cert/CertView.vue:181
#: src/views/cert/DnsView.vue:64
#: src/views/container/ComposeView.vue:74
#: src/views/container/ContainerCreate.vue:174
#: src/views/container/ContainerCreate.vue:255
#: src/views/container/ContainerView.vue:87
#: src/views/container/ImageView.vue:69
#: src/views/container/NetworkView.vue:109
#: src/views/container/VolumeView.vue:63
#: src/views/database/DatabaseList.vue:74
#: src/views/database/ServerList.vue:135
#: src/views/database/UserList.vue:152
#: src/views/file/ListTable.vue:189
#: src/views/file/SearchModal.vue:48
#: src/views/firewall/ForwardView.vue:80
#: src/views/firewall/IpRuleView.vue:122
#: src/views/firewall/RuleView.vue:159
#: src/views/setting/SettingUser.vue:76
#: src/views/setting/TokenModal.vue:52
#: src/views/task/CronView.vue:102
#: src/views/task/SystemView.vue:96
#: src/views/task/TaskView.vue:55
#: src/views/website/IndexView.vue:94
msgid "Actions"
msgstr ""

#: src/views/app/IndexView.vue:83
msgid "Updating app %{ app } may reset related configurations to default state, are you sure to continue?"
msgstr ""

#: src/views/app/IndexView.vue:115
msgid "Manage"
msgstr ""

#: src/views/app/IndexView.vue:128
msgid "Are you sure to uninstall app %{ app }?"
msgstr ""

#: src/views/app/IndexView.vue:138
msgid "Uninstall"
msgstr ""

#: src/views/app/IndexView.vue:183
msgid "Setup successfully"
msgstr ""

#: src/views/app/IndexView.vue:190
#: src/views/app/IndexView.vue:198
#: src/views/app/VersionModal.vue:32
msgid "Task submitted, please check the progress in background tasks"
msgstr ""

#: src/views/app/IndexView.vue:210
msgid "Cache updated successfully"
msgstr ""

#: src/views/app/IndexView.vue:224
msgid "Update Cache"
msgstr ""

#: src/views/app/IndexView.vue:229
msgid "Before updating apps, it is strongly recommended to backup/snapshot first, so you can roll back immediately if there are any issues!"
msgstr ""

#: src/views/app/VersionModal.vue:73
msgid "Channel"
msgstr ""

#: src/views/app/VersionModal.vue:80
msgid "Version"
msgstr ""

#: src/views/app/VersionModal.vue:83
msgid "Please select a channel"
msgstr ""

#: src/views/apps/codeserver/IndexView.vue:39
#: src/views/apps/docker/IndexView.vue:39
#: src/views/apps/frp/IndexView.vue:48
#: src/views/apps/frp/IndexView.vue:73
#: src/views/apps/gitea/IndexView.vue:37
#: src/views/apps/memcached/IndexView.vue:59
#: src/views/apps/minio/IndexView.vue:32
#: src/views/apps/mysql/IndexView.vue:79
#: src/views/apps/nginx/IndexView.vue:65
#: src/views/apps/php/PhpView.vue:194
#: src/views/apps/php/PhpView.vue:203
#: src/views/apps/phpmyadmin/IndexView.vue:58
#: src/views/apps/phpmyadmin/IndexView.vue:67
#: src/views/apps/podman/IndexView.vue:47
#: src/views/apps/podman/IndexView.vue:56
#: src/views/apps/postgresql/IndexView.vue:71
#: src/views/apps/postgresql/IndexView.vue:80
#: src/views/apps/pureftpd/IndexView.vue:162
#: src/views/apps/redis/IndexView.vue:56
#: src/views/apps/rsync/IndexView.vue:201
#: src/views/apps/supervisor/IndexView.vue:313
#: src/views/file/EditModal.vue:31
#: src/views/file/ListTable.vue:723
#: src/views/setting/IndexView.vue:65
#: src/views/toolbox/SystemView.vue:97
#: src/views/toolbox/SystemView.vue:101
#: src/views/toolbox/SystemView.vue:105
#: src/views/toolbox/SystemView.vue:109
#: src/views/website/EditView.vue:215
msgid "Save"
msgstr ""

#: src/views/apps/codeserver/IndexView.vue:46
#: src/views/apps/frp/IndexView.vue:44
#: src/views/apps/frp/IndexView.vue:69
#: src/views/apps/gitea/IndexView.vue:44
#: src/views/apps/mysql/IndexView.vue:118
#: src/views/apps/nginx/IndexView.vue:81
#: src/views/apps/phpmyadmin/IndexView.vue:84
msgid "Modify Configuration"
msgstr ""

#: src/views/apps/codeserver/IndexView.vue:50
msgid "This modifies the Code Server configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/codeserver/IndexView.vue:69
#: src/views/apps/docker/IndexView.vue:65
#: src/views/apps/fail2ban/IndexView.vue:289
#: src/views/apps/gitea/IndexView.vue:67
#: src/views/apps/memcached/IndexView.vue:92
#: src/views/apps/minio/IndexView.vue:62
#: src/views/apps/mysql/IndexView.vue:151
#: src/views/apps/nginx/IndexView.vue:114
#: src/views/apps/php/PhpView.vue:299
#: src/views/apps/podman/IndexView.vue:118
#: src/views/apps/postgresql/IndexView.vue:147
#: src/views/apps/redis/IndexView.vue:96
#: src/views/apps/rsync/IndexView.vue:264
#: src/views/apps/supervisor/IndexView.vue:380
msgid "Runtime Logs"
msgstr ""

#: src/views/apps/docker/IndexView.vue:46
#: src/views/website/EditView.vue:401
msgid "Configuration"
msgstr ""

#: src/views/apps/docker/IndexView.vue:49
msgid "This modifies the Docker configuration file (/etc/docker/daemon.json)"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:44
#: src/views/apps/phpmyadmin/IndexView.vue:71
#: src/views/apps/supervisor/IndexView.vue:55
#: src/views/container/ComposeView.vue:58
#: src/views/container/ContainerView.vue:32
#: src/views/database/ServerList.vue:114
#: src/views/database/UserList.vue:131
#: src/views/firewall/RuleView.vue:62
#: src/views/task/SystemView.vue:43
#: src/views/task/TaskView.vue:22
msgid "Status"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:56
#: src/views/apps/fail2ban/IndexView.vue:365
msgid "Max Retries"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:57
#: src/views/apps/fail2ban/IndexView.vue:371
msgid "Ban Time"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:58
#: src/views/apps/fail2ban/IndexView.vue:368
msgid "Find Time"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:78
#: src/views/cert/CertView.vue:263
msgid "View"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:89
msgid "Are you sure you want to delete rule %{ name }?"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:100
#: src/views/apps/php/PhpView.vue:109
#: src/views/apps/pureftpd/IndexView.vue:89
#: src/views/apps/rsync/IndexView.vue:107
#: src/views/apps/supervisor/IndexView.vue:202
#: src/views/backup/ListView.vue:98
#: src/views/cert/AccountView.vue:127
#: src/views/cert/CertView.vue:314
#: src/views/cert/DnsView.vue:111
#: src/views/container/ComposeView.vue:220
#: src/views/container/ContainerCreate.vue:235
#: src/views/container/ContainerCreate.vue:286
#: src/views/container/ContainerView.vue:156
#: src/views/container/ContainerView.vue:414
#: src/views/container/ImageView.vue:94
#: src/views/container/NetworkView.vue:134
#: src/views/container/VolumeView.vue:88
#: src/views/database/DatabaseList.vue:98
#: src/views/database/ServerList.vue:217
#: src/views/database/UserList.vue:191
#: src/views/file/ListTable.vue:85
#: src/views/file/ListTable.vue:289
#: src/views/file/SearchModal.vue:98
#: src/views/file/ToolBar.vue:238
#: src/views/firewall/ForwardView.vue:104
#: src/views/firewall/IpRuleView.vue:146
#: src/views/firewall/RuleView.vue:183
#: src/views/setting/SettingUser.vue:132
#: src/views/setting/TokenModal.vue:92
#: src/views/ssh/IndexView.vue:93
#: src/views/task/CronView.vue:155
#: src/views/task/TaskView.vue:98
#: src/views/website/IndexView.vue:167
msgid "Delete"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:134
msgid "Are you sure you want to unban %{ ip }?"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:144
msgid "Unban"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:192
#: src/views/apps/pureftpd/IndexView.vue:130
#: src/views/apps/rsync/IndexView.vue:153
#: src/views/apps/s3fs/IndexView.vue:84
#: src/views/apps/supervisor/IndexView.vue:241
#: src/views/database/CreateServerModal.vue:37
msgid "Added successfully"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:200
#: src/views/apps/pureftpd/IndexView.vue:147
#: src/views/apps/rsync/IndexView.vue:161
#: src/views/apps/s3fs/IndexView.vue:91
#: src/views/apps/supervisor/IndexView.vue:269
#: src/views/backup/ListView.vue:148
#: src/views/database/DatabaseList.vue:123
#: src/views/database/ServerList.vue:242
#: src/views/database/UserList.vue:216
#: src/views/file/ListTable.vue:272
#: src/views/file/ListTable.vue:615
#: src/views/file/SearchModal.vue:81
#: src/views/file/ToolBar.vue:189
#: src/views/firewall/ForwardView.vue:131
#: src/views/firewall/ForwardView.vue:149
#: src/views/firewall/IpRuleView.vue:173
#: src/views/firewall/IpRuleView.vue:191
#: src/views/firewall/RuleView.vue:210
#: src/views/firewall/RuleView.vue:228
#: src/views/setting/SettingUser.vue:162
#: src/views/setting/TokenModal.vue:116
#: src/views/task/CronView.vue:198
#: src/views/task/TaskView.vue:124
#: src/views/website/IndexView.vue:269
#: src/views/website/IndexView.vue:325
msgid "Deleted successfully"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:213
msgid "Unbanned successfully"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:239
msgid "Save Whitelist"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:248
#: src/views/apps/fail2ban/IndexView.vue:294
#: src/views/apps/fail2ban/IndexView.vue:298
msgid "Add Rule"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:255
msgid "IP Whitelist"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:260
msgid "IP whitelist, separated by commas"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:265
msgid "Rule Management"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:266
msgid "Rule List"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:304
msgid "If an IP exceeds the maximum retries within the find time (seconds), it will be banned for the ban time (seconds)"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:311
msgid "Protected ports are automatically obtained. If you modify the port corresponding to a rule, please delete and re-add the rule, otherwise protection may not be effective"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:318
#: src/views/cert/CertView.vue:87
#: src/views/cert/DnsView.vue:38
#: src/views/cert/ObtainModal.vue:58
#: src/views/database/CreateServerModal.vue:63
#: src/views/database/DatabaseList.vue:12
#: src/views/database/ServerList.vue:17
#: src/views/database/UserList.vue:17
msgid "Type"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:323
#: src/views/apps/fail2ban/IndexView.vue:354
msgid "Service"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:328
#: src/views/apps/fail2ban/IndexView.vue:332
#: src/views/task/CreateModal.vue:144
#: src/views/task/CreateModal.vue:149
msgid "Select Website"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:335
msgid "Protection Mode"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:340
#: src/views/apps/pureftpd/IndexView.vue:39
msgid "Path"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:347
#: src/views/apps/fail2ban/IndexView.vue:351
msgid "Protection Path"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:379
#: src/views/apps/fail2ban/IndexView.vue:383
msgid "View Rule"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:387
msgid "Rule Information"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:390
msgid "Currently Banned"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:394
msgid "Total Bans"
msgstr ""

#: src/views/apps/fail2ban/IndexView.vue:399
msgid "Ban List"
msgstr ""

#: src/views/apps/gitea/IndexView.vue:48
msgid "This modifies the Gitea configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/memcached/IndexView.vue:18
#: src/views/apps/mysql/IndexView.vue:31
#: src/views/apps/nginx/IndexView.vue:28
#: src/views/apps/php/PhpView.vue:124
#: src/views/apps/postgresql/IndexView.vue:31
#: src/views/apps/redis/IndexView.vue:25
msgid "Property"
msgstr ""

#: src/views/apps/memcached/IndexView.vue:25
#: src/views/apps/mysql/IndexView.vue:38
#: src/views/apps/nginx/IndexView.vue:35
#: src/views/apps/php/PhpView.vue:131
#: src/views/apps/postgresql/IndexView.vue:38
#: src/views/apps/redis/IndexView.vue:32
msgid "Current Value"
msgstr ""

#: src/views/apps/memcached/IndexView.vue:66
msgid "Service Configuration"
msgstr ""

#: src/views/apps/memcached/IndexView.vue:82
#: src/views/apps/mysql/IndexView.vue:141
#: src/views/apps/nginx/IndexView.vue:104
#: src/views/apps/php/PhpView.vue:289
#: src/views/apps/postgresql/IndexView.vue:137
#: src/views/apps/redis/IndexView.vue:86
#: src/views/dashboard/IndexView.vue:462
msgid "Load Status"
msgstr ""

#: src/views/apps/minio/IndexView.vue:39
#: src/views/container/ComposeView.vue:330
#: src/views/container/ComposeView.vue:360
#: src/views/container/ContainerCreate.vue:352
msgid "Environment Variables"
msgstr ""

#: src/views/apps/minio/IndexView.vue:43
msgid "This is modifying the Minio environment variable file /etc/default/minio. If you do not understand the meaning of each parameter, please do not modify it arbitrarily!"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:53
#: src/views/apps/mysql/IndexView.vue:59
#: src/views/apps/nginx/IndexView.vue:50
#: src/views/apps/php/PhpView.vue:158
#: src/views/apps/php/PhpView.vue:164
#: src/views/apps/postgresql/IndexView.vue:57
#: src/views/apps/supervisor/IndexView.vue:233
#: src/views/website/EditView.vue:160
msgid "Cleared successfully"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:65
#: src/views/apps/pureftpd/IndexView.vue:140
#: src/views/database/DatabaseList.vue:129
#: src/views/database/ServerList.vue:248
#: src/views/database/UpdateServerModal.vue:21
#: src/views/database/UpdateUserModal.vue:18
#: src/views/database/UserList.vue:222
#: src/views/file/PermissionModal.vue:29
#: src/views/setting/SettingUser.vue:156
#: src/views/task/CronView.vue:180
#: src/views/task/CronView.vue:207
#: src/views/website/IndexView.vue:252
#: src/views/website/IndexView.vue:278
msgid "Modified successfully"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:88
#: src/views/apps/nginx/IndexView.vue:74
#: src/views/apps/postgresql/IndexView.vue:84
#: src/views/apps/supervisor/IndexView.vue:326
msgid "Clear Log"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:97
#: src/views/apps/php/PhpView.vue:221
msgid "Clear Slow Log"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:104
#: src/views/toolbox/SystemView.vue:205
#: src/views/toolbox/SystemView.vue:207
msgid "Root Password"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:112
msgid "Save Changes"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:122
msgid "This modifies the MySQL main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/mysql/IndexView.vue:154
msgid "Slow Query Log"
msgstr ""

#: src/views/apps/nginx/IndexView.vue:85
msgid "This modifies the OpenResty main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/nginx/IndexView.vue:117
#: src/views/apps/php/PhpView.vue:302
msgid "Error Logs"
msgstr ""

#: src/views/apps/php/PhpView.vue:43
msgid "Extension Name"
msgstr ""

#: src/views/apps/php/PhpView.vue:71
msgid "Are you sure you want to install %{ name }?"
msgstr ""

#: src/views/apps/php/PhpView.vue:97
msgid "Are you sure you want to uninstall %{ name }?"
msgstr ""

#: src/views/apps/php/PhpView.vue:140
msgid "Set successfully"
msgstr ""

#: src/views/apps/php/PhpView.vue:170
#: src/views/apps/php/PhpView.vue:176
msgid "Task submitted, please check progress in background tasks"
msgstr ""

#: src/views/apps/php/PhpView.vue:185
msgid "Set as CLI Default Version"
msgstr ""

#: src/views/apps/php/PhpView.vue:212
msgid "Clear Error Log"
msgstr ""

#: src/views/apps/php/PhpView.vue:228
msgid "Extension Management"
msgstr ""

#: src/views/apps/php/PhpView.vue:241
#: src/views/apps/postgresql/IndexView.vue:91
#: src/views/apps/redis/IndexView.vue:63
#: src/views/apps/rsync/IndexView.vue:241
#: src/views/apps/supervisor/IndexView.vue:357
msgid "Main Configuration"
msgstr ""

#: src/views/apps/php/PhpView.vue:245
msgid "This modifies the PHP %{ version } main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/php/PhpView.vue:265
msgid "FPM Configuration"
msgstr ""

#: src/views/apps/php/PhpView.vue:269
msgid "This modifies the PHP %{ version } FPM configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/php/PhpView.vue:305
#: src/views/apps/postgresql/IndexView.vue:150
msgid "Slow Logs"
msgstr ""

#: src/views/apps/phpmyadmin/IndexView.vue:73
msgid "Access Information"
msgstr ""

#: src/views/apps/phpmyadmin/IndexView.vue:75
msgid "Access URL:"
msgstr ""

#: src/views/apps/phpmyadmin/IndexView.vue:78
msgid "Modify Port"
msgstr ""

#: src/views/apps/phpmyadmin/IndexView.vue:80
msgid "Modify phpMyAdmin access port"
msgstr ""

#: src/views/apps/phpmyadmin/IndexView.vue:88
msgid "This modifies the OpenResty configuration file for phpMyAdmin. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/podman/IndexView.vue:64
msgid "Podman is a daemonless container management tool. Being in a stopped state is normal and does not affect usage!"
msgstr ""

#: src/views/apps/podman/IndexView.vue:72
msgid "Registry Configuration"
msgstr ""

#: src/views/apps/podman/IndexView.vue:76
msgid "This modifies the Podman registry configuration file (/etc/containers/registries.conf)"
msgstr ""

#: src/views/apps/podman/IndexView.vue:95
msgid "Storage Configuration"
msgstr ""

#: src/views/apps/podman/IndexView.vue:99
msgid "This modifies the Podman storage configuration file (/etc/containers/storage.conf)"
msgstr ""

#: src/views/apps/postgresql/IndexView.vue:95
msgid "This modifies the PostgreSQL main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/postgresql/IndexView.vue:114
msgid "User Configuration"
msgstr ""

#: src/views/apps/postgresql/IndexView.vue:118
msgid "This modifies the PostgreSQL user configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:32
#: src/views/apps/pureftpd/IndexView.vue:221
#: src/views/container/ImageView.vue:198
#: src/views/database/CreateDatabaseModal.vue:93
#: src/views/database/CreateServerModal.vue:94
#: src/views/database/CreateUserModal.vue:70
#: src/views/database/ServerList.vue:47
#: src/views/database/UpdateServerModal.vue:86
#: src/views/database/UserList.vue:40
#: src/views/login/IndexView.vue:133
#: src/views/setting/CreateModal.vue:40
#: src/views/setting/SettingUser.vue:19
#: src/views/ssh/CreateModal.vue:83
#: src/views/ssh/UpdateModal.vue:89
msgid "Username"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:76
msgid "Are you sure you want to delete user %{ username }?"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:171
msgid "Add User"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:178
msgid "Port Settings"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:180
msgid "Modify Pure-Ftpd listening port"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:184
msgid "User Management"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:208
msgid "Run Log"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:213
#: src/views/apps/pureftpd/IndexView.vue:217
#: src/views/database/CreateDatabaseModal.vue:78
#: src/views/database/CreateUserModal.vue:54
#: src/views/database/IndexView.vue:36
#: src/views/setting/CreateModal.vue:32
#: src/views/setting/IndexView.vue:69
msgid "Create User"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:226
#: src/views/container/ImageView.vue:203
#: src/views/database/CreateDatabaseModal.vue:98
#: src/views/database/CreateUserModal.vue:75
msgid "Enter username"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:229
#: src/views/apps/pureftpd/IndexView.vue:260
#: src/views/apps/rsync/IndexView.vue:304
#: src/views/apps/rsync/IndexView.vue:358
#: src/views/container/ImageView.vue:206
#: src/views/database/CreateDatabaseModal.vue:101
#: src/views/database/CreateServerModal.vue:102
#: src/views/database/CreateUserModal.vue:78
#: src/views/database/ServerList.vue:56
#: src/views/database/UpdateServerModal.vue:94
#: src/views/database/UpdateUserModal.vue:49
#: src/views/database/UserList.vue:50
#: src/views/login/IndexView.vue:142
#: src/views/setting/CreateModal.vue:47
#: src/views/setting/PasswordModal.vue:34
#: src/views/ssh/CreateModal.vue:77
#: src/views/ssh/CreateModal.vue:86
#: src/views/ssh/UpdateModal.vue:83
#: src/views/ssh/UpdateModal.vue:92
msgid "Password"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:236
#: src/views/apps/pureftpd/IndexView.vue:266
msgid "It is recommended to use the generator to generate a random password"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:240
#: src/views/apps/rsync/IndexView.vue:47
#: src/views/apps/rsync/IndexView.vue:288
#: src/views/apps/rsync/IndexView.vue:342
#: src/views/container/ComposeView.vue:38
#: src/views/website/IndexView.vue:44
#: src/views/website/IndexView.vue:517
msgid "Directory"
msgstr ""

#: src/views/apps/pureftpd/IndexView.vue:245
msgid "Enter the directory authorized to the user"
msgstr ""

#: src/views/apps/redis/IndexView.vue:67
msgid "This modifies the Redis main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:54
#: src/views/apps/rsync/IndexView.vue:296
#: src/views/apps/rsync/IndexView.vue:350
#: src/views/database/IndexView.vue:48
#: src/views/setting/IndexView.vue:79
#: src/views/task/SystemView.vue:37
msgid "User"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:61
#: src/views/apps/rsync/IndexView.vue:312
#: src/views/apps/rsync/IndexView.vue:367
#: src/views/database/CreateDatabaseModal.vue:110
#: src/views/database/CreateServerModal.vue:73
#: src/views/database/ServerList.vue:87
#: src/views/database/UpdateServerModal.vue:65
#: src/views/database/UserList.vue:81
#: src/views/ssh/CreateModal.vue:62
#: src/views/ssh/UpdateModal.vue:68
#: src/views/toolbox/SystemView.vue:156
msgid "Host"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:67
#: src/views/apps/rsync/IndexView.vue:320
#: src/views/apps/rsync/IndexView.vue:375
#: src/views/database/CreateServerModal.vue:111
#: src/views/database/CreateUserModal.vue:109
#: src/views/database/DatabaseList.vue:57
#: src/views/database/ServerList.vue:97
#: src/views/database/UpdateServerModal.vue:103
#: src/views/database/UpdateUserModal.vue:64
#: src/views/database/UserList.vue:114
msgid "Comment"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:83
#: src/views/apps/supervisor/IndexView.vue:104
msgid "Configure"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:94
msgid "Are you sure you want to delete module %{ name }?"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:210
#: src/views/apps/rsync/IndexView.vue:272
msgid "Add Module"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:217
msgid "Module Management"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:245
msgid "This modifies the Rsync main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:285
#: src/views/apps/supervisor/IndexView.vue:404
msgid "Name cannot contain Chinese characters"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:293
#: src/views/apps/rsync/IndexView.vue:347
msgid "Please enter absolute path"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:301
#: src/views/apps/rsync/IndexView.vue:355
msgid "Enter module username"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:309
#: src/views/apps/rsync/IndexView.vue:364
msgid "Enter module password"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:317
#: src/views/apps/rsync/IndexView.vue:372
msgid "Enter allowed hosts, separate multiple hosts with spaces"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:325
#: src/views/apps/rsync/IndexView.vue:380
msgid "Enter comments"
msgstr ""

#: src/views/apps/rsync/IndexView.vue:334
msgid "Module Configuration"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:25
msgid "Mount Path"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:46
msgid "Are you sure you want to delete mount %{ path }?"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:58
msgid "Unmount"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:105
#: src/views/apps/s3fs/IndexView.vue:131
#: src/views/apps/s3fs/IndexView.vue:135
msgid "Add Mount"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:144
msgid "Enter Bucket name (COS format: xxxx-ID)"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:152
msgid "Enter AK key"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:160
msgid "Enter SK key"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:163
msgid "Region Endpoint"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:169
msgid "Enter complete URL of region endpoint (e.g., https://oss-cn-beijing.aliyuncs.com)"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:175
msgid "Mount Directory"
msgstr ""

#: src/views/apps/s3fs/IndexView.vue:180
msgid "Enter mount directory (e.g., /oss)"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:69
msgid "Uptime"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:132
msgid "Are you sure you want to stop process %{ name }?"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:161
msgid "Are you sure you want to restart process %{ name }?"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:189
msgid "Are you sure you want to delete process %{ name }?"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:322
#: src/views/apps/supervisor/IndexView.vue:391
msgid "Add Process"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:333
msgid "Process Management"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:361
msgid "This modifies the Supervisor main configuration file. If you do not understand the meaning of each parameter, please do not modify it randomly!"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:383
msgid "Daemon Logs"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:407
msgid "Start Command"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:412
msgid "Please enter absolute path for files in start command"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:415
msgid "Working Directory"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:420
msgid "Please enter absolute path for working directory"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:423
msgid "Run As User"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:428
msgid "Usually www is sufficient"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:431
msgid "Number of Processes"
msgstr ""

#: src/views/apps/supervisor/IndexView.vue:441
msgid "Process Configuration"
msgstr ""

#: src/views/backup/ListView.vue:36
msgid "Filename"
msgstr ""

#: src/views/backup/ListView.vue:49
#: src/views/database/ServerList.vue:126
#: src/views/database/UserList.vue:143
msgid "Update Date"
msgstr ""

#: src/views/backup/ListView.vue:76
msgid "Restore"
msgstr ""

#: src/views/backup/ListView.vue:87
msgid "Are you sure you want to delete this backup?"
msgstr ""

#: src/views/backup/ListView.vue:131
msgid "Restoring..."
msgstr ""

#: src/views/backup/ListView.vue:138
msgid "Restored successfully"
msgstr ""

#: src/views/backup/ListView.vue:182
#: src/views/backup/ListView.vue:212
msgid "Create Backup"
msgstr ""

#: src/views/backup/ListView.vue:185
#: src/views/backup/UploadModal.vue:39
msgid "Upload Backup"
msgstr ""

#: src/views/backup/ListView.vue:224
#: src/views/backup/ListView.vue:261
msgid "Select website"
msgstr ""

#: src/views/backup/ListView.vue:227
#: src/views/database/CreateDatabaseModal.vue:70
#: src/views/database/DatabaseList.vue:35
#: src/views/task/CreateModal.vue:154
#: src/views/task/CreateModal.vue:156
#: src/views/website/IndexView.vue:481
#: src/views/website/IndexView.vue:486
msgid "Database Name"
msgstr ""

#: src/views/backup/ListView.vue:232
#: src/views/database/CreateDatabaseModal.vue:75
#: src/views/database/CreateUserModal.vue:106
#: src/views/database/UpdateUserModal.vue:61
msgid "Enter database name"
msgstr ""

#: src/views/backup/ListView.vue:235
#: src/views/task/CreateModal.vue:158
#: src/views/task/CreateModal.vue:161
msgid "Save Directory"
msgstr ""

#: src/views/backup/ListView.vue:240
msgid "Leave empty to use default path"
msgstr ""

#: src/views/backup/ListView.vue:249
msgid "Restore Backup"
msgstr ""

#: src/views/backup/UploadModal.vue:20
msgid "Upload %{ filename } successfully"
msgstr ""

#: src/views/backup/UploadModal.vue:51
#: src/views/file/UploadModal.vue:50
msgid "Click or drag files to this area to upload"
msgstr ""

#: src/views/backup/UploadModal.vue:53
msgid "For large files, it is recommended to use SFTP or other methods to upload"
msgstr ""

#: src/views/cert/AccountView.vue:44
#: src/views/cert/AccountView.vue:243
#: src/views/cert/CreateAccountModal.vue:100
#: src/views/setting/CreateModal.vue:56
#: src/views/setting/SettingUser.vue:26
msgid "Email"
msgstr ""

#: src/views/cert/AccountView.vue:72
#: src/views/cert/AccountView.vue:235
#: src/views/cert/CertView.vue:458
#: src/views/cert/CreateAccountModal.vue:92
#: src/views/cert/CreateCertModal.vue:82
msgid "Key Type"
msgstr ""

#: src/views/cert/AccountView.vue:101
#: src/views/cert/CertView.vue:288
#: src/views/cert/DnsView.vue:85
#: src/views/database/ServerList.vue:184
#: src/views/database/UserList.vue:169
#: src/views/file/PermissionModal.vue:123
#: src/views/setting/TokenModal.vue:69
#: src/views/toolbox/SystemView.vue:118
msgid "Modify"
msgstr ""

#: src/views/cert/AccountView.vue:109
#: src/views/cert/CertView.vue:297
#: src/views/cert/DnsView.vue:94
msgid "Deletion successful"
msgstr ""

#: src/views/cert/AccountView.vue:116
msgid "Are you sure you want to delete the account?"
msgstr ""

#: src/views/cert/AccountView.vue:150
#: src/views/cert/CreateAccountModal.vue:39
msgid "Registering account with CA, please wait patiently"
msgstr ""

#: src/views/cert/AccountView.vue:162
#: src/views/cert/CertView.vue:348
#: src/views/cert/CertView.vue:365
#: src/views/cert/DnsView.vue:139
#: src/views/container/ComposeView.vue:264
msgid "Update successful"
msgstr ""

#: src/views/cert/AccountView.vue:207
msgid "Modify Account"
msgstr ""

#: src/views/cert/AccountView.vue:215
#: src/views/cert/CreateAccountModal.vue:72
msgid "Google and SSL.com require obtaining KID and HMAC from their official websites first"
msgstr ""

#: src/views/cert/AccountView.vue:221
#: src/views/cert/CreateAccountModal.vue:78
msgid "Google is not accessible in mainland China, other CAs depend on network conditions, recommend using Let's Encrypt"
msgstr ""

#: src/views/cert/AccountView.vue:227
#: src/views/cert/CreateAccountModal.vue:84
msgid "CA"
msgstr ""

#: src/views/cert/AccountView.vue:230
#: src/views/cert/CreateAccountModal.vue:87
msgid "Select CA"
msgstr ""

#: src/views/cert/AccountView.vue:238
#: src/views/cert/CertView.vue:461
#: src/views/cert/CreateAccountModal.vue:95
#: src/views/cert/CreateCertModal.vue:85
msgid "Select key type"
msgstr ""

#: src/views/cert/AccountView.vue:248
#: src/views/cert/CreateAccountModal.vue:105
msgid "Enter email address"
msgstr ""

#: src/views/cert/AccountView.vue:256
#: src/views/cert/CreateAccountModal.vue:113
msgid "Enter KID"
msgstr ""

#: src/views/cert/AccountView.vue:264
#: src/views/cert/CreateAccountModal.vue:121
msgid "Enter HMAC"
msgstr ""

#: src/views/cert/CertView.vue:64
#: src/views/cert/CertView.vue:450
#: src/views/cert/CreateCertModal.vue:74
#: src/views/cert/ObtainModal.vue:57
#: src/views/website/EditView.vue:232
#: src/views/website/IndexView.vue:425
msgid "Domain"
msgstr ""

#: src/views/cert/CertView.vue:70
#: src/views/cert/CertView.vue:124
#: src/views/cert/CertView.vue:135
#: src/views/cert/CertView.vue:154
#: src/views/container/ContainerCreate.vue:55
#: src/views/database/ServerList.vue:52
#: src/views/database/ServerList.vue:67
#: src/views/database/UserList.vue:46
#: src/views/database/UserList.vue:86
#: src/views/firewall/ForwardView.vue:26
#: src/views/firewall/IpRuleView.vue:26
#: src/views/firewall/IpRuleView.vue:43
#: src/views/firewall/RuleView.vue:26
#: src/views/firewall/RuleView.vue:43
msgid "None"
msgstr ""

#: src/views/cert/CertView.vue:109
#: src/views/file/ToolBar.vue:221
#: src/views/file/UploadModal.vue:38
msgid "Upload"
msgstr ""

#: src/views/cert/CertView.vue:117
msgid "Associated Account"
msgstr ""

#: src/views/cert/CertView.vue:130
#: src/views/website/EditView.vue:306
msgid "Issuer"
msgstr ""

#: src/views/cert/CertView.vue:139
#: src/views/setting/TokenModal.vue:43
#: src/views/setting/TokenModal.vue:258
#: src/views/setting/TokenModal.vue:291
msgid "Expiration Time"
msgstr ""

#: src/views/cert/CertView.vue:167
msgid "Auto Renew"
msgstr ""

#: src/views/cert/CertView.vue:200
#: src/views/cert/ObtainModal.vue:78
msgid "Issue"
msgstr ""

#: src/views/cert/CertView.vue:219
msgid "Deploy"
msgstr ""

#: src/views/cert/CertView.vue:231
#: src/views/cert/ObtainModal.vue:24
#: src/views/cert/ObtainModal.vue:81
#: src/views/website/EditView.vue:133
msgid "Please wait..."
msgstr ""

#: src/views/cert/CertView.vue:237
msgid "Renewal successful"
msgstr ""

#: src/views/cert/CertView.vue:245
msgid "Renew"
msgstr ""

#: src/views/cert/CertView.vue:303
msgid "Are you sure you want to delete the certificate?"
msgstr ""

#: src/views/cert/CertView.vue:389
msgid "Deployment successful"
msgstr ""

#: src/views/cert/CertView.vue:435
msgid "Modify Certificate"
msgstr ""

#: src/views/cert/CertView.vue:444
msgid "You can automatically issue and deploy certificates by selecting any website/DNS, or manually enter domain names and set DNS resolution to issue certificates, or fill in deployment scripts to automatically deploy certificates."
msgstr ""

#: src/views/cert/CertView.vue:469
#: src/views/cert/CreateCertModal.vue:93
msgid "Select website for certificate deployment"
msgstr ""

#: src/views/cert/CertView.vue:477
#: src/views/cert/CreateCertModal.vue:98
msgid "Account"
msgstr ""

#: src/views/cert/CertView.vue:481
#: src/views/cert/CreateCertModal.vue:101
msgid "Select account for certificate issuance"
msgstr ""

#: src/views/cert/CertView.vue:486
#: src/views/cert/CreateCertModal.vue:106
#: src/views/cert/CreateDnsModal.vue:59
#: src/views/cert/DnsView.vue:196
msgid "DNS"
msgstr ""

#: src/views/cert/CertView.vue:489
#: src/views/cert/CreateCertModal.vue:109
msgid "Select DNS for certificate issuance"
msgstr ""

#: src/views/cert/CertView.vue:502
#: src/views/cert/UploadCertModal.vue:42
#: src/views/website/EditView.vue:359
msgid "Enter the content of the PEM certificate file"
msgstr ""

#: src/views/cert/CertView.vue:509
#: src/views/cert/CertView.vue:585
#: src/views/cert/UploadCertModal.vue:46
#: src/views/setting/SettingSafe.vue:61
#: src/views/ssh/CreateModal.vue:78
#: src/views/ssh/CreateModal.vue:89
#: src/views/ssh/UpdateModal.vue:84
#: src/views/ssh/UpdateModal.vue:95
#: src/views/website/EditView.vue:363
msgid "Private Key"
msgstr ""

#: src/views/cert/CertView.vue:514
#: src/views/cert/UploadCertModal.vue:50
#: src/views/website/EditView.vue:367
msgid "Enter the content of the KEY private key file"
msgstr ""

#: src/views/cert/CertView.vue:521
msgid "Deployment Script"
msgstr ""

#: src/views/cert/CertView.vue:527
msgid "The {cert} and {key} in the script will be replaced with the certificate and private key content"
msgstr ""

#: src/views/cert/CertView.vue:541
msgid "Deploy Certificate"
msgstr ""

#: src/views/cert/CertView.vue:552
msgid "Select websites to deploy the certificate"
msgstr ""

#: src/views/cert/CertView.vue:565
msgid "View Certificate"
msgstr ""

#: src/views/cert/CreateAccountModal.vue:64
#: src/views/cert/IndexView.vue:106
msgid "Create Account"
msgstr ""

#: src/views/cert/CreateCertModal.vue:59
#: src/views/cert/IndexView.vue:102
msgid "Create Certificate"
msgstr ""

#: src/views/cert/CreateCertModal.vue:68
msgid "You can automatically issue and deploy certificates by selecting either Website or DNS, or you can manually enter domain names and set up DNS resolution to issue certificates"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:44
#: src/views/cert/IndexView.vue:110
msgid "Create DNS"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:52
msgid "Comment Name"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:56
msgid "Enter comment name"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:62
#: src/views/cert/DnsView.vue:199
msgid "Select DNS"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:71
#: src/views/cert/DnsView.vue:208
msgid "Enter Aliyun Access Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:78
#: src/views/cert/DnsView.vue:215
msgid "Enter Aliyun Secret Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:85
#: src/views/cert/DnsView.vue:222
msgid "Enter Tencent Cloud SecretId"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:92
#: src/views/cert/DnsView.vue:229
msgid "Enter Tencent Cloud SecretKey"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:99
#: src/views/cert/DnsView.vue:236
msgid "Enter Huawei Cloud AccessKeyId"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:106
#: src/views/cert/DnsView.vue:243
msgid "Enter Huawei Cloud SecretAccessKey"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:113
#: src/views/cert/DnsView.vue:250
msgid "Enter West.cn Username"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:120
#: src/views/cert/DnsView.vue:257
msgid "Enter West.cn API Password"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:127
#: src/views/cert/DnsView.vue:264
msgid "Enter Cloudflare API Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:134
#: src/views/cert/DnsView.vue:271
msgid "Enter GoDaddy Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:141
#: src/views/cert/DnsView.vue:278
msgid "Enter G-Core API Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:148
#: src/views/cert/DnsView.vue:285
msgid "Enter Porkbun API Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:155
#: src/views/cert/DnsView.vue:292
msgid "Enter Porkbun Secret Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:162
#: src/views/cert/DnsView.vue:299
msgid "Enter Namecheap API Username"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:169
#: src/views/cert/DnsView.vue:306
msgid "Enter Namecheap API Key"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:176
#: src/views/cert/DnsView.vue:313
msgid "Enter NameSilo API Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:183
#: src/views/cert/DnsView.vue:320
msgid "Enter Name.com Username"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:190
#: src/views/cert/DnsView.vue:327
msgid "Enter Name.com Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:198
#: src/views/cert/DnsView.vue:334
msgid "Enter ClouDNS Auth ID (use Sub Auth ID by adding sub-prefix)"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:205
#: src/views/cert/DnsView.vue:341
msgid "Enter ClouDNS Auth Password"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:212
#: src/views/cert/DnsView.vue:348
msgid "Enter Duck DNS Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:219
#: src/views/cert/DnsView.vue:355
msgid "Enter Hetzner Auth API Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:226
#: src/views/cert/DnsView.vue:362
msgid "Enter Linode Token"
msgstr ""

#: src/views/cert/CreateDnsModal.vue:233
#: src/views/cert/DnsView.vue:369
msgid "Enter Vercel Token"
msgstr ""

#: src/views/cert/DnsView.vue:31
#: src/views/cert/DnsView.vue:189
msgid "Note Name"
msgstr ""

#: src/views/cert/DnsView.vue:100
msgid "Are you sure you want to delete the DNS?"
msgstr ""

#: src/views/cert/DnsView.vue:181
msgid "Modify DNS"
msgstr ""

#: src/views/cert/DnsView.vue:193
msgid "Enter note name"
msgstr ""

#: src/views/cert/IndexView.vue:98
#: src/views/cert/UploadCertModal.vue:30
msgid "Upload Certificate"
msgstr ""

#: src/views/cert/IndexView.vue:115
msgid "Certificate List"
msgstr ""

#: src/views/cert/IndexView.vue:118
msgid "Account List"
msgstr ""

#: src/views/cert/IndexView.vue:121
msgid "DNS List"
msgstr ""

#: src/views/cert/ObtainModal.vue:18
msgid "Automatic"
msgstr ""

#: src/views/cert/ObtainModal.vue:19
msgid "Manual"
msgstr ""

#: src/views/cert/ObtainModal.vue:20
msgid "Self-signed"
msgstr ""

#: src/views/cert/ObtainModal.vue:33
#: src/views/cert/ObtainModal.vue:89
#: src/views/cert/ObtainModal.vue:107
msgid "Issuance successful"
msgstr ""

#: src/views/cert/ObtainModal.vue:42
msgid "Please set up DNS resolution for the domain first, then continue with the issuance"
msgstr ""

#: src/views/cert/ObtainModal.vue:48
msgid "DNS Records to Set"
msgstr ""

#: src/views/cert/ObtainModal.vue:59
msgid "Host Record"
msgstr ""

#: src/views/cert/ObtainModal.vue:60
msgid "Record Value"
msgstr ""

#: src/views/cert/ObtainModal.vue:120
msgid "Issue Certificate"
msgstr ""

#: src/views/cert/ObtainModal.vue:127
msgid "Issuance Mode"
msgstr ""

#: src/views/container/ComposeView.vue:65
#: src/views/container/ImageView.vue:60
#: src/views/container/NetworkView.vue:100
#: src/views/container/VolumeView.vue:54
#: src/views/setting/SettingUser.vue:67
#: src/views/setting/TokenModal.vue:34
#: src/views/task/CronView.vue:83
#: src/views/task/TaskView.vue:37
msgid "Creation Time"
msgstr ""

#: src/views/container/ComposeView.vue:96
#: src/views/file/ListTable.vue:68
#: src/views/file/ListTable.vue:220
#: src/views/ssh/IndexView.vue:71
#: src/views/task/CronView.vue:133
#: src/views/website/IndexView.vue:109
msgid "Edit"
msgstr ""

#: src/views/container/ComposeView.vue:111
#: src/views/container/ContainerView.vue:237
#: src/views/container/ContainerView.vue:301
msgid "Start successful"
msgstr ""

#: src/views/container/ComposeView.vue:132
msgid "Are you sure you want to start compose %{ name }?"
msgstr ""

#: src/views/container/ComposeView.vue:143
msgid "Force pull images"
msgstr ""

#: src/views/container/ComposeView.vue:170
#: src/views/container/ContainerView.vue:244
#: src/views/container/ContainerView.vue:315
msgid "Stop successful"
msgstr ""

#: src/views/container/ComposeView.vue:176
msgid "Are you sure you want to stop compose %{ name }?"
msgstr ""

#: src/views/container/ComposeView.vue:201
#: src/views/container/ContainerView.vue:279
#: src/views/container/ContainerView.vue:357
#: src/views/container/ImageView.vue:118
#: src/views/container/NetworkView.vue:158
#: src/views/container/VolumeView.vue:112
msgid "Delete successful"
msgstr ""

#: src/views/container/ComposeView.vue:207
msgid "Are you sure you want to delete compose %{ name }?"
msgstr ""

#: src/views/container/ComposeView.vue:286
#: src/views/container/ComposeView.vue:313
msgid "Create Compose"
msgstr ""

#: src/views/container/ComposeView.vue:320
msgid "Compose Name"
msgstr ""

#: src/views/container/ComposeView.vue:323
#: src/views/container/ComposeView.vue:353
#: src/views/container/IndexView.vue:24
msgid "Compose"
msgstr ""

#: src/views/container/ComposeView.vue:334
#: src/views/container/ComposeView.vue:364
#: src/views/container/ContainerCreate.vue:356
msgid "Variable Name"
msgstr ""

#: src/views/container/ComposeView.vue:335
#: src/views/container/ComposeView.vue:365
#: src/views/container/ContainerCreate.vue:357
msgid "Variable Value"
msgstr ""

#: src/views/container/ComposeView.vue:346
msgid "Edit Compose"
msgstr ""

#: src/views/container/ContainerCreate.vue:56
msgid "Always"
msgstr ""

#: src/views/container/ContainerCreate.vue:57
msgid "On failure (default 5 retries)"
msgstr ""

#: src/views/container/ContainerCreate.vue:58
msgid "Unless stopped"
msgstr ""

#: src/views/container/ContainerCreate.vue:127
#: src/views/container/ContainerView.vue:402
msgid "Create Container"
msgstr ""

#: src/views/container/ContainerCreate.vue:137
#: src/views/container/ContainerView.vue:25
msgid "Container Name"
msgstr ""

#: src/views/container/ContainerCreate.vue:140
#: src/views/container/ContainerView.vue:52
#: src/views/container/ImageView.vue:36
msgid "Image"
msgstr ""

#: src/views/container/ContainerCreate.vue:143
msgid "Ports"
msgstr ""

#: src/views/container/ContainerCreate.vue:149
msgid "Map Ports"
msgstr ""

#: src/views/container/ContainerCreate.vue:156
msgid "Expose All"
msgstr ""

#: src/views/container/ContainerCreate.vue:161
msgid "Port Mapping"
msgstr ""

#: src/views/container/ContainerCreate.vue:169
msgid "Host (Start)"
msgstr ""

#: src/views/container/ContainerCreate.vue:170
msgid "Host (End)"
msgstr ""

#: src/views/container/ContainerCreate.vue:171
msgid "Container (Start)"
msgstr ""

#: src/views/container/ContainerCreate.vue:172
msgid "Container (End)"
msgstr ""

#: src/views/container/ContainerCreate.vue:173
msgid "Protocol"
msgstr ""

#: src/views/container/ContainerCreate.vue:184
msgid "Optional"
msgstr ""

#: src/views/container/ContainerCreate.vue:241
#: src/views/container/ContainerCreate.vue:292
msgid "Add"
msgstr ""

#: src/views/container/ContainerCreate.vue:244
#: src/views/dashboard/IndexView.vue:141
#: src/views/dashboard/IndexView.vue:797
#: src/views/monitor/IndexView.vue:286
msgid "Network"
msgstr ""

#: src/views/container/ContainerCreate.vue:247
msgid "Mount"
msgstr ""

#: src/views/container/ContainerCreate.vue:252
msgid "Host Directory"
msgstr ""

#: src/views/container/ContainerCreate.vue:253
msgid "Container Directory"
msgstr ""

#: src/views/container/ContainerCreate.vue:254
#: src/views/file/ListTable.vue:73
#: src/views/file/ListTable.vue:145
#: src/views/file/ListTable.vue:300
#: src/views/file/PermissionModal.vue:113
#: src/views/file/ToolBar.vue:235
msgid "Permission"
msgstr ""

#: src/views/container/ContainerCreate.vue:273
msgid "Read-Write"
msgstr ""

#: src/views/container/ContainerCreate.vue:281
msgid "Read-Only"
msgstr ""

#: src/views/container/ContainerCreate.vue:295
#: src/views/container/ContainerCreate.vue:296
msgid "Command"
msgstr ""

#: src/views/container/ContainerCreate.vue:298
#: src/views/container/ContainerCreate.vue:301
msgid "Entrypoint"
msgstr ""

#: src/views/container/ContainerCreate.vue:306
#: src/views/dashboard/IndexView.vue:533
#: src/views/monitor/IndexView.vue:205
#: src/views/monitor/IndexView.vue:216
#: src/views/monitor/IndexView.vue:238
#: src/views/task/SystemView.vue:78
#: src/views/toolbox/BenchmarkView.vue:188
msgid "Memory"
msgstr ""

#: src/views/container/ContainerCreate.vue:316
msgid "CPU Shares"
msgstr ""

#: src/views/container/ContainerCreate.vue:323
msgid "TTY (-t)"
msgstr ""

#: src/views/container/ContainerCreate.vue:328
msgid "STDIN (-i)"
msgstr ""

#: src/views/container/ContainerCreate.vue:333
msgid "Auto Remove"
msgstr ""

#: src/views/container/ContainerCreate.vue:338
msgid "Privileged Mode"
msgstr ""

#: src/views/container/ContainerCreate.vue:343
msgid "Restart Policy"
msgstr ""

#: src/views/container/ContainerCreate.vue:346
#: src/views/container/ContainerCreate.vue:349
msgid "Select restart policy"
msgstr ""

#: src/views/container/ContainerCreate.vue:360
#: src/views/container/NetworkView.vue:295
#: src/views/container/VolumeView.vue:195
msgid "Labels"
msgstr ""

#: src/views/container/ContainerCreate.vue:364
#: src/views/container/NetworkView.vue:299
#: src/views/container/VolumeView.vue:199
msgid "Label Name"
msgstr ""

#: src/views/container/ContainerCreate.vue:365
#: src/views/container/NetworkView.vue:300
#: src/views/container/VolumeView.vue:200
msgid "Label Value"
msgstr ""

#: src/views/container/ContainerView.vue:63
msgid "Ports (Host->Container)"
msgstr ""

#: src/views/container/ContainerView.vue:118
#: src/views/container/ContainerView.vue:465
#: src/views/file/ListTable.vue:84
#: src/views/file/ListTable.vue:264
msgid "Rename"
msgstr ""

#: src/views/container/ContainerView.vue:141
#: src/views/container/ContainerView.vue:411
msgid "Force Stop"
msgstr ""

#: src/views/container/ContainerView.vue:146
#: src/views/container/ContainerView.vue:412
msgid "Pause"
msgstr ""

#: src/views/container/ContainerView.vue:151
#: src/views/container/ContainerView.vue:413
msgid "Resume"
msgstr ""

#: src/views/container/ContainerView.vue:196
#: src/views/file/ListTable.vue:364
msgid "More"
msgstr ""

#: src/views/container/ContainerView.vue:229
msgid "Rename successful"
msgstr ""

#: src/views/container/ContainerView.vue:251
#: src/views/container/ContainerView.vue:329
msgid "Restart successful"
msgstr ""

#: src/views/container/ContainerView.vue:258
#: src/views/container/ContainerView.vue:343
msgid "Force stop successful"
msgstr ""

#: src/views/container/ContainerView.vue:265
#: src/views/container/ContainerView.vue:371
msgid "Pause successful"
msgstr ""

#: src/views/container/ContainerView.vue:272
#: src/views/container/ContainerView.vue:385
msgid "Resume successful"
msgstr ""

#: src/views/container/ContainerView.vue:286
#: src/views/container/ImageView.vue:125
#: src/views/container/NetworkView.vue:165
#: src/views/container/VolumeView.vue:119
msgid "Cleanup successful"
msgstr ""

#: src/views/container/ContainerView.vue:292
msgid "Please select containers to start"
msgstr ""

#: src/views/container/ContainerView.vue:306
msgid "Please select containers to stop"
msgstr ""

#: src/views/container/ContainerView.vue:320
msgid "Please select containers to restart"
msgstr ""

#: src/views/container/ContainerView.vue:334
msgid "Please select containers to force stop"
msgstr ""

#: src/views/container/ContainerView.vue:348
msgid "Please select containers to delete"
msgstr ""

#: src/views/container/ContainerView.vue:362
msgid "Please select containers to pause"
msgstr ""

#: src/views/container/ContainerView.vue:376
msgid "Please select containers to resume"
msgstr ""

#: src/views/container/ContainerView.vue:405
msgid "Cleanup Containers"
msgstr ""

#: src/views/container/ContainerView.vue:472
#: src/views/file/ListTable.vue:719
msgid "New Name"
msgstr ""

#: src/views/container/ContainerView.vue:477
msgid "Enter new name"
msgstr ""

#: src/views/container/ImageView.vue:29
msgid "Container Count"
msgstr ""

#: src/views/container/ImageView.vue:84
#: src/views/container/NetworkView.vue:124
#: src/views/container/VolumeView.vue:78
#: src/views/firewall/ForwardView.vue:93
#: src/views/firewall/IpRuleView.vue:135
#: src/views/firewall/RuleView.vue:172
#: src/views/task/TaskView.vue:87
msgid "Are you sure you want to delete?"
msgstr ""

#: src/views/container/ImageView.vue:134
msgid "Pull successful"
msgstr ""

#: src/views/container/ImageView.vue:150
#: src/views/container/ImageView.vue:180
msgid "Pull Image"
msgstr ""

#: src/views/container/ImageView.vue:152
msgid "Cleanup Images"
msgstr ""

#: src/views/container/ImageView.vue:187
msgid "Image Name"
msgstr ""

#: src/views/container/ImageView.vue:192
msgid "docker.io/php:8.3-fpm"
msgstr ""

#: src/views/container/ImageView.vue:195
msgid "Authentication"
msgstr ""

#: src/views/container/ImageView.vue:212
#: src/views/database/CreateDatabaseModal.vue:107
#: src/views/database/CreateUserModal.vue:84
#: src/views/database/UpdateUserModal.vue:55
msgid "Enter password"
msgstr ""

#: src/views/container/IndexView.vue:21
msgid "Containers"
msgstr ""

#: src/views/container/IndexView.vue:27
msgid "Images"
msgstr ""

#: src/views/container/IndexView.vue:30
msgid "Networks"
msgstr ""

#: src/views/container/IndexView.vue:33
msgid "Volumes"
msgstr ""

#: src/views/container/NetworkView.vue:52
#: src/views/container/NetworkView.vue:232
#: src/views/container/VolumeView.vue:33
#: src/views/container/VolumeView.vue:186
msgid "Driver"
msgstr ""

#: src/views/container/NetworkView.vue:59
#: src/views/container/VolumeView.vue:40
msgid "Scope"
msgstr ""

#: src/views/container/NetworkView.vue:66
#: src/views/container/NetworkView.vue:244
#: src/views/container/NetworkView.vue:271
msgid "Subnet"
msgstr ""

#: src/views/container/NetworkView.vue:83
#: src/views/container/NetworkView.vue:252
#: src/views/container/NetworkView.vue:279
msgid "Gateway"
msgstr ""

#: src/views/container/NetworkView.vue:191
#: src/views/container/NetworkView.vue:222
msgid "Create Network"
msgstr ""

#: src/views/container/NetworkView.vue:194
msgid "Cleanup Networks"
msgstr ""

#: src/views/container/NetworkView.vue:229
msgid "Network Name"
msgstr ""

#: src/views/container/NetworkView.vue:249
#: src/views/container/NetworkView.vue:265
msgid "***********/24"
msgstr ""

#: src/views/container/NetworkView.vue:257
msgid "*************"
msgstr ""

#: src/views/container/NetworkView.vue:260
#: src/views/container/NetworkView.vue:287
msgid "IP Range"
msgstr ""

#: src/views/container/NetworkView.vue:276
msgid "2408:400e::/48"
msgstr ""

#: src/views/container/NetworkView.vue:284
msgid "2408:400e::1"
msgstr ""

#: src/views/container/NetworkView.vue:292
msgid "2408:400e::/64"
msgstr ""

#: src/views/container/NetworkView.vue:303
#: src/views/container/VolumeView.vue:203
msgid "Options"
msgstr ""

#: src/views/container/NetworkView.vue:307
#: src/views/container/VolumeView.vue:207
msgid "Option Name"
msgstr ""

#: src/views/container/NetworkView.vue:308
#: src/views/container/VolumeView.vue:208
msgid "Option Value"
msgstr ""

#: src/views/container/VolumeView.vue:47
#: src/views/dashboard/IndexView.vue:644
msgid "Mount Point"
msgstr ""

#: src/views/container/VolumeView.vue:145
#: src/views/container/VolumeView.vue:176
msgid "Create Volume"
msgstr ""

#: src/views/container/VolumeView.vue:148
msgid "Cleanup Volumes"
msgstr ""

#: src/views/container/VolumeView.vue:183
msgid "Volume Name"
msgstr ""

#: src/views/dashboard/IndexView.vue:129
msgid "Running blocked"
msgstr ""

#: src/views/dashboard/IndexView.vue:131
msgid "Running slowly"
msgstr ""

#: src/views/dashboard/IndexView.vue:133
msgid "Running normally"
msgstr ""

#: src/views/dashboard/IndexView.vue:135
msgid "Running smoothly"
msgstr ""

#: src/views/dashboard/IndexView.vue:141
#: src/views/dashboard/IndexView.vue:798
#: src/views/dashboard/IndexView.vue:818
#: src/views/toolbox/BenchmarkView.vue:219
msgid "Disk"
msgstr ""

#: src/views/dashboard/IndexView.vue:164
#: src/views/dashboard/IndexView.vue:181
msgid "Send"
msgstr ""

#: src/views/dashboard/IndexView.vue:164
#: src/views/dashboard/IndexView.vue:205
msgid "Receive"
msgstr ""

#: src/views/dashboard/IndexView.vue:165
#: src/views/dashboard/IndexView.vue:181
#: src/views/dashboard/IndexView.vue:842
#: src/views/file/PermissionModal.vue:88
#: src/views/file/PermissionModal.vue:97
#: src/views/file/PermissionModal.vue:106
msgid "Read"
msgstr ""

#: src/views/dashboard/IndexView.vue:165
#: src/views/dashboard/IndexView.vue:205
#: src/views/dashboard/IndexView.vue:843
#: src/views/file/PermissionModal.vue:89
#: src/views/file/PermissionModal.vue:98
#: src/views/file/PermissionModal.vue:107
msgid "Write"
msgstr ""

#: src/views/dashboard/IndexView.vue:173
msgid "Unit %{unit}"
msgstr ""

#: src/views/dashboard/IndexView.vue:187
#: src/views/dashboard/IndexView.vue:211
#: src/views/monitor/IndexView.vue:93
#: src/views/monitor/IndexView.vue:115
#: src/views/monitor/IndexView.vue:137
#: src/views/monitor/IndexView.vue:192
#: src/views/monitor/IndexView.vue:251
#: src/views/monitor/IndexView.vue:273
#: src/views/monitor/IndexView.vue:335
#: src/views/monitor/IndexView.vue:357
#: src/views/monitor/IndexView.vue:379
#: src/views/monitor/IndexView.vue:401
msgid "Maximum"
msgstr ""

#: src/views/dashboard/IndexView.vue:188
#: src/views/dashboard/IndexView.vue:212
#: src/views/monitor/IndexView.vue:94
#: src/views/monitor/IndexView.vue:116
#: src/views/monitor/IndexView.vue:138
#: src/views/monitor/IndexView.vue:193
#: src/views/monitor/IndexView.vue:252
#: src/views/monitor/IndexView.vue:274
#: src/views/monitor/IndexView.vue:336
#: src/views/monitor/IndexView.vue:358
#: src/views/monitor/IndexView.vue:380
#: src/views/monitor/IndexView.vue:402
msgid "Minimum"
msgstr ""

#: src/views/dashboard/IndexView.vue:192
#: src/views/dashboard/IndexView.vue:216
#: src/views/monitor/IndexView.vue:98
#: src/views/monitor/IndexView.vue:120
#: src/views/monitor/IndexView.vue:142
#: src/views/monitor/IndexView.vue:197
#: src/views/monitor/IndexView.vue:256
#: src/views/monitor/IndexView.vue:278
#: src/views/monitor/IndexView.vue:340
#: src/views/monitor/IndexView.vue:362
#: src/views/monitor/IndexView.vue:384
#: src/views/monitor/IndexView.vue:406
msgid "Average"
msgstr ""

#: src/views/dashboard/IndexView.vue:324
msgid "Panel restarting..."
msgstr ""

#: src/views/dashboard/IndexView.vue:326
msgid "Panel restarted successfully"
msgstr ""

#: src/views/dashboard/IndexView.vue:338
msgid "Current version is the latest"
msgstr ""

#: src/views/dashboard/IndexView.vue:434
#: src/views/task/IndexView.vue:29
msgid "Scheduled Tasks"
msgstr ""

#: src/views/dashboard/IndexView.vue:443
msgid "Sponsor Support"
msgstr ""

#: src/views/dashboard/IndexView.vue:449
msgid "Are you sure you want to restart the panel?"
msgstr ""

#: src/views/dashboard/IndexView.vue:457
msgid "Resource Overview"
msgstr ""

#: src/views/dashboard/IndexView.vue:474
msgid "Last 1 minute"
msgstr ""

#: src/views/dashboard/IndexView.vue:481
msgid "Last 5 minutes"
msgstr ""

#: src/views/dashboard/IndexView.vue:488
msgid "Last 15 minutes"
msgstr ""

#: src/views/dashboard/IndexView.vue:506
#: src/views/dashboard/IndexView.vue:517
msgid "cores"
msgstr ""

#: src/views/dashboard/IndexView.vue:511
msgid "Model"
msgstr ""

#: src/views/dashboard/IndexView.vue:515
msgid "Parameters"
msgstr ""

#: src/views/dashboard/IndexView.vue:518
msgid "cache"
msgstr ""

#: src/views/dashboard/IndexView.vue:524
#: src/views/monitor/IndexView.vue:179
msgid "Usage"
msgstr ""

#: src/views/dashboard/IndexView.vue:525
msgid "Frequency"
msgstr ""

#: src/views/dashboard/IndexView.vue:545
msgid "Active"
msgstr ""

#: src/views/dashboard/IndexView.vue:551
msgid "Inactive"
msgstr ""

#: src/views/dashboard/IndexView.vue:557
msgid "Free"
msgstr ""

#: src/views/dashboard/IndexView.vue:563
msgid "Shared"
msgstr ""

#: src/views/dashboard/IndexView.vue:569
msgid "Committed"
msgstr ""

#: src/views/dashboard/IndexView.vue:575
msgid "Commit Limit"
msgstr ""

#: src/views/dashboard/IndexView.vue:581
#: src/views/toolbox/SystemView.vue:149
msgid "SWAP Size"
msgstr ""

#: src/views/dashboard/IndexView.vue:587
msgid "SWAP Used"
msgstr ""

#: src/views/dashboard/IndexView.vue:593
msgid "SWAP Available"
msgstr ""

#: src/views/dashboard/IndexView.vue:599
msgid "Physical Memory Size"
msgstr ""

#: src/views/dashboard/IndexView.vue:605
msgid "Physical Memory Used"
msgstr ""

#: src/views/dashboard/IndexView.vue:611
msgid "Physical Memory Available"
msgstr ""

#: src/views/dashboard/IndexView.vue:648
msgid "File System"
msgstr ""

#: src/views/dashboard/IndexView.vue:652
msgid "Inodes Usage"
msgstr ""

#: src/views/dashboard/IndexView.vue:656
msgid "Inodes Total"
msgstr ""

#: src/views/dashboard/IndexView.vue:660
msgid "Inodes Used"
msgstr ""

#: src/views/dashboard/IndexView.vue:664
msgid "Inodes Available"
msgstr ""

#: src/views/dashboard/IndexView.vue:681
msgid "Quick Apps"
msgstr ""

#: src/views/dashboard/IndexView.vue:724
msgid "You have not set any apps to display here!"
msgstr ""

#: src/views/dashboard/IndexView.vue:728
msgid "Environment Information"
msgstr ""

#: src/views/dashboard/IndexView.vue:731
msgid "System Hostname"
msgstr ""

#: src/views/dashboard/IndexView.vue:737
msgid "System Version"
msgstr ""

#: src/views/dashboard/IndexView.vue:746
msgid "System Kernel Version"
msgstr ""

#: src/views/dashboard/IndexView.vue:752
msgid "System Uptime"
msgstr ""

#: src/views/dashboard/IndexView.vue:758
msgid "Panel Internal Version"
msgstr ""

#: src/views/dashboard/IndexView.vue:770
msgid "Panel Compile Information"
msgstr ""

#: src/views/dashboard/IndexView.vue:787
msgid "Real-time Monitoring"
msgstr ""

#: src/views/dashboard/IndexView.vue:801
msgid "Unit"
msgstr ""

#: src/views/dashboard/IndexView.vue:809
msgid "Network Card"
msgstr ""

#: src/views/dashboard/IndexView.vue:829
msgid "Total Sent"
msgstr ""

#: src/views/dashboard/IndexView.vue:831
msgid "Total Received"
msgstr ""

#: src/views/dashboard/IndexView.vue:834
msgid "Real-time Sent"
msgstr ""

#: src/views/dashboard/IndexView.vue:838
msgid "Real-time Received"
msgstr ""

#: src/views/dashboard/IndexView.vue:845
msgid "Real-time Read/Write"
msgstr ""

#: src/views/dashboard/IndexView.vue:848
msgid "Read/Write Latency"
msgstr ""

#: src/views/dashboard/UpdateView.vue:24
msgid "Update Panel"
msgstr ""

#: src/views/dashboard/UpdateView.vue:25
msgid "Are you sure you want to update the panel?"
msgstr ""

#: src/views/dashboard/UpdateView.vue:29
msgid "Panel updating..."
msgstr ""

#: src/views/dashboard/UpdateView.vue:40
msgid "Panel updated successfully"
msgstr ""

#: src/views/dashboard/UpdateView.vue:47
msgid "Update canceled"
msgstr ""

#: src/views/dashboard/UpdateView.vue:59
msgid "Update Now"
msgstr ""

#: src/views/dashboard/UpdateView.vue:85
msgid "Loading update information, please wait a moment"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:20
#: src/views/database/CreateUserModal.vue:20
msgid "Local (localhost)"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:21
#: src/views/database/CreateUserModal.vue:21
msgid "All (%)"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:22
#: src/views/database/CreateUserModal.vue:22
msgid "Specific"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:54
#: src/views/database/IndexView.vue:32
msgid "Create Database"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:62
#: src/views/database/CreateUserModal.vue:62
#: src/views/database/DatabaseList.vue:42
#: src/views/database/IndexView.vue:51
#: src/views/database/UserList.vue:91
msgid "Server"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:66
#: src/views/database/CreateUserModal.vue:66
msgid "Select server"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:84
msgid "Authorized User"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:90
msgid "Enter authorized username (leave empty for no authorization)"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:114
#: src/views/database/CreateUserModal.vue:91
msgid "Select host"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:121
#: src/views/database/CreateUserModal.vue:95
msgid "Specific Host"
msgstr ""

#: src/views/database/CreateDatabaseModal.vue:127
#: src/views/database/CreateUserModal.vue:100
msgid "Enter supported host address"
msgstr ""

#: src/views/database/CreateServerModal.vue:47
#: src/views/database/IndexView.vue:40
msgid "Add Server"
msgstr ""

#: src/views/database/CreateServerModal.vue:60
#: src/views/database/UpdateServerModal.vue:60
msgid "Enter database server name"
msgstr ""

#: src/views/database/CreateServerModal.vue:67
msgid "Select database type"
msgstr ""

#: src/views/database/CreateServerModal.vue:78
#: src/views/database/UpdateServerModal.vue:70
msgid "Enter database server host"
msgstr ""

#: src/views/database/CreateServerModal.vue:84
#: src/views/database/UpdateServerModal.vue:76
#: src/views/firewall/ForwardView.vue:32
#: src/views/firewall/RuleView.vue:49
#: src/views/setting/SettingBase.vue:49
#: src/views/ssh/CreateModal.vue:68
#: src/views/ssh/UpdateModal.vue:74
#: src/views/website/IndexView.vue:436
msgid "Port"
msgstr ""

#: src/views/database/CreateServerModal.vue:89
#: src/views/database/UpdateServerModal.vue:81
msgid "Enter database server port"
msgstr ""

#: src/views/database/CreateServerModal.vue:99
#: src/views/database/UpdateServerModal.vue:91
msgid "Enter database server username"
msgstr ""

#: src/views/database/CreateServerModal.vue:108
#: src/views/database/UpdateServerModal.vue:100
msgid "Enter database server password"
msgstr ""

#: src/views/database/CreateServerModal.vue:116
#: src/views/database/UpdateServerModal.vue:108
msgid "Enter database server comment"
msgstr ""

#: src/views/database/CreateUserModal.vue:87
msgid "Host (MySQL only)"
msgstr ""

#: src/views/database/CreateUserModal.vue:103
#: src/views/database/UpdateUserModal.vue:58
#: src/views/database/UserList.vue:99
msgid "Privileges"
msgstr ""

#: src/views/database/CreateUserModal.vue:114
#: src/views/database/UpdateUserModal.vue:69
msgid "Enter database user comment"
msgstr ""

#: src/views/database/DatabaseList.vue:47
msgid "Encoding"
msgstr ""

#: src/views/database/DatabaseList.vue:87
msgid "Are you sure you want to delete this database?"
msgstr ""

#: src/views/database/ServerList.vue:76
#: src/views/database/UserList.vue:70
#: src/views/file/ListTable.vue:516
#: src/views/file/ListTable.vue:536
#: src/views/file/SearchModal.vue:65
#: src/views/file/ToolBar.vue:145
#: src/views/file/ToolBar.vue:165
#: src/views/setting/TokenModal.vue:165
msgid "Copied successfully"
msgstr ""

#: src/views/database/ServerList.vue:80
#: src/views/database/UserList.vue:74
#: src/views/file/ListTable.vue:71
#: src/views/file/ListTable.vue:298
#: src/views/file/ToolBar.vue:232
msgid "Copy"
msgstr ""

#: src/views/database/ServerList.vue:121
#: src/views/database/UserList.vue:138
msgid "Valid"
msgstr ""

#: src/views/database/ServerList.vue:121
#: src/views/database/UserList.vue:138
msgid "Invalid"
msgstr ""

#: src/views/database/ServerList.vue:147
#: src/views/toolbox/SystemView.vue:87
msgid "Synchronized successfully"
msgstr ""

#: src/views/database/ServerList.vue:153
msgid "Are you sure you want to synchronize database users (excluding password) to the panel?"
msgstr ""

#: src/views/database/ServerList.vue:165
msgid "Sync"
msgstr ""

#: src/views/database/ServerList.vue:195
msgid "Built-in servers cannot be deleted. If you need to delete them, please uninstall the corresponding app"
msgstr ""

#: src/views/database/ServerList.vue:206
msgid "Are you sure you want to delete the server?"
msgstr ""

#: src/views/database/UpdateServerModal.vue:47
msgid "Modify Server"
msgstr ""

#: src/views/database/UpdateUserModal.vue:41
msgid "Modify User"
msgstr ""

#: src/views/database/UserList.vue:61
msgid "Not saved"
msgstr ""

#: src/views/database/UserList.vue:180
msgid "Are you sure you want to delete the user?"
msgstr ""

#: src/views/error-page/NotFound.vue:11
msgid "Sorry, the page you visited does not exist."
msgstr ""

#: src/views/error-page/NotFound.vue:19
msgid "Back to Home"
msgstr ""

#: src/views/file/CompressModal.vue:41
msgid "Compressing..."
msgstr ""

#: src/views/file/CompressModal.vue:49
msgid "Compressed successfully"
msgstr ""

#: src/views/file/CompressModal.vue:73
#: src/views/file/CompressModal.vue:105
#: src/views/file/ListTable.vue:75
#: src/views/file/ListTable.vue:245
#: src/views/file/ListTable.vue:301
#: src/views/file/ToolBar.vue:234
msgid "Compress"
msgstr ""

#: src/views/file/CompressModal.vue:81
msgid "Files to compress"
msgstr ""

#: src/views/file/CompressModal.vue:84
msgid "Compress to"
msgstr ""

#: src/views/file/CompressModal.vue:87
msgid "Format"
msgstr ""

#: src/views/file/EditModal.vue:22
msgid "Edit - %{ file }"
msgstr ""

#: src/views/file/EditModal.vue:30
msgid "Refresh"
msgstr ""

#: src/views/file/ListTable.vue:65
#: src/views/file/ListTable.vue:222
msgid "Open"
msgstr ""

#: src/views/file/ListTable.vue:67
#: src/views/file/ListTable.vue:220
msgid "Preview"
msgstr ""

#: src/views/file/ListTable.vue:72
#: src/views/file/ListTable.vue:299
#: src/views/file/ToolBar.vue:233
msgid "Move"
msgstr ""

#: src/views/file/ListTable.vue:75
#: src/views/file/ListTable.vue:247
msgid "Download"
msgstr ""

#: src/views/file/ListTable.vue:79
#: src/views/file/ListTable.vue:303
#: src/views/file/ListTable.vue:741
msgid "Uncompress"
msgstr ""

#: src/views/file/ListTable.vue:89
#: src/views/file/ToolBar.vue:229
msgid "Paste"
msgstr ""

#: src/views/file/ListTable.vue:279
#: src/views/file/SearchModal.vue:88
msgid "Are you sure you want to delete %{ name }?"
msgstr ""

#: src/views/file/ListTable.vue:320
#: src/views/file/ListTable.vue:335
#: src/views/file/ListTable.vue:575
#: src/views/file/ListTable.vue:588
#: src/views/file/ToolBar.vue:77
#: src/views/file/ToolBar.vue:94
msgid "Marked successfully, please navigate to the destination path to paste"
msgstr ""

#: src/views/file/ListTable.vue:412
#: src/views/file/ListTable.vue:499
#: src/views/file/ToolBar.vue:128
msgid "Warning"
msgstr ""

#: src/views/file/ListTable.vue:413
msgid "There are items with the same name. Do you want to overwrite?"
msgstr ""

#: src/views/file/ListTable.vue:414
#: src/views/file/ListTable.vue:509
#: src/views/file/ToolBar.vue:138
msgid "Overwrite"
msgstr ""

#: src/views/file/ListTable.vue:421
#: src/views/file/ListTable.vue:437
msgid "Renamed %{ source } to %{ target } successfully"
msgstr ""

#: src/views/file/ListTable.vue:459
msgid "Uncompressing..."
msgstr ""

#: src/views/file/ListTable.vue:466
msgid "Uncompressed successfully"
msgstr ""

#: src/views/file/ListTable.vue:475
#: src/views/file/ToolBar.vue:104
msgid "Please mark the files/folders to copy or move first"
msgstr ""

#: src/views/file/ListTable.vue:500
#: src/views/file/ToolBar.vue:129
msgid "There are items with the same name. %{ items } Do you want to overwrite?"
msgstr ""

#: src/views/file/ListTable.vue:522
#: src/views/file/ListTable.vue:542
#: src/views/file/ToolBar.vue:151
#: src/views/file/ToolBar.vue:171
msgid "Moved successfully"
msgstr ""

#: src/views/file/ListTable.vue:528
#: src/views/file/ToolBar.vue:157
msgid "Canceled"
msgstr ""

#: src/views/file/ListTable.vue:711
msgid "Rename - %{ source }"
msgstr ""

#: src/views/file/ListTable.vue:729
msgid "Uncompress - %{ file }"
msgstr ""

#: src/views/file/ListTable.vue:737
msgid "Uncompress to"
msgstr ""

#: src/views/file/PathInput.vue:154
msgid "Enter search content"
msgstr ""

#: src/views/file/PathInput.vue:157
msgid "Include subdirectories"
msgstr ""

#: src/views/file/PermissionModal.vue:65
msgid "Batch modify permissions"
msgstr ""

#: src/views/file/PermissionModal.vue:66
msgid "Modify permissions - %{ path }"
msgstr ""

#: src/views/file/PermissionModal.vue:86
#: src/views/file/PermissionModal.vue:116
msgid "Owner"
msgstr ""

#: src/views/file/PermissionModal.vue:90
#: src/views/file/PermissionModal.vue:99
#: src/views/file/PermissionModal.vue:108
msgid "Execute"
msgstr ""

#: src/views/file/PermissionModal.vue:95
#: src/views/file/PermissionModal.vue:119
msgid "Group"
msgstr ""

#: src/views/file/PermissionModal.vue:104
msgid "Others"
msgstr ""

#: src/views/file/PreviewModal.vue:31
msgid "Preview - "
msgstr ""

#: src/views/file/SearchModal.vue:71
msgid "Copy Path"
msgstr ""

#: src/views/file/SearchModal.vue:154
msgid "%{ keyword } - Search Results"
msgstr ""

#: src/views/file/ToolBar.vue:60
msgid "Download task created successfully"
msgstr ""

#: src/views/file/ToolBar.vue:66
msgid "Please select files/folders to copy"
msgstr ""

#: src/views/file/ToolBar.vue:83
msgid "Please select files/folders to move"
msgstr ""

#: src/views/file/ToolBar.vue:180
msgid "Please select files/folders to delete"
msgstr ""

#: src/views/file/ToolBar.vue:219
#: src/views/file/ToolBar.vue:249
msgid "New"
msgstr ""

#: src/views/file/ToolBar.vue:222
#: src/views/file/ToolBar.vue:267
msgid "Remote Download"
msgstr ""

#: src/views/file/ToolBar.vue:240
msgid "Are you sure you want to delete in bulk?"
msgstr ""

#: src/views/file/ToolBar.vue:275
msgid "Download URL"
msgstr ""

#: src/views/file/ToolBar.vue:278
msgid "Save as"
msgstr ""

#: src/views/file/UploadModal.vue:20
msgid "Upload %{ fileName } successful"
msgstr ""

#: src/views/file/UploadModal.vue:53
msgid "For large files, it is recommended to use SFTP and other methods to upload"
msgstr ""

#: src/views/firewall/CreateForwardModal.vue:50
#: src/views/firewall/ForwardView.vue:166
msgid "Create Forwarding"
msgstr ""

#: src/views/firewall/CreateForwardModal.vue:58
#: src/views/firewall/CreateIpModal.vue:97
#: src/views/firewall/CreateModal.vue:101
#: src/views/firewall/ForwardView.vue:15
#: src/views/firewall/IpRuleView.vue:15
#: src/views/firewall/RuleView.vue:15
msgid "Transport Protocol"
msgstr ""

#: src/views/firewall/CreateForwardModal.vue:61
#: src/views/firewall/ForwardView.vue:44
msgid "Target IP"
msgstr ""

#: src/views/firewall/CreateForwardModal.vue:66
msgid "Source Port"
msgstr ""

#: src/views/firewall/CreateForwardModal.vue:76
#: src/views/firewall/ForwardView.vue:62
msgid "Target Port"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:38
#: src/views/firewall/CreateModal.vue:38
#: src/views/firewall/IpRuleView.vue:69
#: src/views/firewall/RuleView.vue:103
msgid "Accept"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:42
#: src/views/firewall/CreateModal.vue:42
#: src/views/firewall/IpRuleView.vue:71
#: src/views/firewall/RuleView.vue:105
msgid "Drop"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:46
#: src/views/firewall/CreateModal.vue:46
#: src/views/firewall/IpRuleView.vue:73
#: src/views/firewall/RuleView.vue:107
msgid "Reject"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:53
#: src/views/firewall/CreateModal.vue:53
#: src/views/firewall/IpRuleView.vue:98
#: src/views/firewall/RuleView.vue:132
msgid "Inbound"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:57
#: src/views/firewall/CreateModal.vue:57
#: src/views/firewall/IpRuleView.vue:100
#: src/views/firewall/RuleView.vue:134
msgid "Outbound"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:78
msgid "%{ address } created successfully"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:89
#: src/views/firewall/CreateModal.vue:93
#: src/views/firewall/IpRuleView.vue:208
#: src/views/firewall/RuleView.vue:245
msgid "Create Rule"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:100
#: src/views/firewall/CreateModal.vue:104
#: src/views/firewall/IpRuleView.vue:32
#: src/views/firewall/RuleView.vue:32
msgid "Network Protocol"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:103
msgid "IP Address"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:108
#: src/views/firewall/CreateModal.vue:133
msgid "Optional IP or IP range: 127.0.0.1 or **********/24 (multiple separated by commas)"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:114
#: src/views/firewall/CreateModal.vue:139
#: src/views/firewall/IpRuleView.vue:49
#: src/views/firewall/RuleView.vue:83
msgid "Strategy"
msgstr ""

#: src/views/firewall/CreateIpModal.vue:117
#: src/views/firewall/CreateModal.vue:142
#: src/views/firewall/IpRuleView.vue:85
#: src/views/firewall/RuleView.vue:119
msgid "Direction"
msgstr ""

#: src/views/firewall/CreateModal.vue:109
msgid "Start Port"
msgstr ""

#: src/views/firewall/CreateModal.vue:119
msgid "End Port"
msgstr ""

#: src/views/firewall/CreateModal.vue:129
#: src/views/firewall/IpRuleView.vue:110
#: src/views/firewall/RuleView.vue:144
msgid "Target"
msgstr ""

#: src/views/firewall/ForwardView.vue:137
#: src/views/firewall/IpRuleView.vue:179
#: src/views/firewall/RuleView.vue:216
msgid "Please select rules to delete"
msgstr ""

#: src/views/firewall/ForwardView.vue:172
#: src/views/firewall/IpRuleView.vue:214
#: src/views/firewall/RuleView.vue:251
#: src/views/website/IndexView.vue:357
msgid "Batch Delete"
msgstr ""

#: src/views/firewall/ForwardView.vue:175
#: src/views/firewall/IpRuleView.vue:217
#: src/views/firewall/RuleView.vue:254
msgid "Are you sure you want to batch delete?"
msgstr ""

#: src/views/firewall/IndexView.vue:19
msgid "Port Rules"
msgstr ""

#: src/views/firewall/IndexView.vue:22
msgid "IP Rules"
msgstr ""

#: src/views/firewall/IndexView.vue:25
msgid "Port Forwarding"
msgstr ""

#: src/views/firewall/IpRuleView.vue:75
#: src/views/firewall/RuleView.vue:109
msgid "Mark"
msgstr ""

#: src/views/firewall/RuleView.vue:74
msgid "In Use"
msgstr ""

#: src/views/firewall/RuleView.vue:76
msgid "Not Used"
msgstr ""

#: src/views/firewall/RuleView.vue:151
msgid "All"
msgstr ""

#: src/views/firewall/SettingView.vue:27
#: src/views/firewall/SettingView.vue:33
#: src/views/firewall/SettingView.vue:39
msgid "Settings saved successfully"
msgstr ""

#: src/views/firewall/SettingView.vue:46
msgid "System Firewall"
msgstr ""

#: src/views/firewall/SettingView.vue:49
msgid "SSH Switch"
msgstr ""

#: src/views/firewall/SettingView.vue:52
msgid "Allow Ping"
msgstr ""

#: src/views/firewall/SettingView.vue:55
msgid "SSH Port"
msgstr ""

#: src/views/login/IndexView.vue:49
msgid "Please enter username and password"
msgstr ""

#: src/views/login/IndexView.vue:54
msgid "Failed to get encryption public key, please refresh the page and try again"
msgstr ""

#: src/views/login/IndexView.vue:67
msgid "Login successful!"
msgstr ""

#: src/views/login/IndexView.vue:153
msgid "2FA Code"
msgstr ""

#: src/views/login/IndexView.vue:161
msgid "Safe Login"
msgstr ""

#: src/views/login/IndexView.vue:162
msgid "Remember Me"
msgstr ""

#: src/views/login/IndexView.vue:176
msgid "Login"
msgstr ""

#: src/views/monitor/IndexView.vue:60
msgid "Load"
msgstr ""

#: src/views/monitor/IndexView.vue:71
#: src/views/monitor/IndexView.vue:87
msgid "1 minute"
msgstr ""

#: src/views/monitor/IndexView.vue:71
#: src/views/monitor/IndexView.vue:102
msgid "5 minutes"
msgstr ""

#: src/views/monitor/IndexView.vue:71
#: src/views/monitor/IndexView.vue:124
msgid "15 minutes"
msgstr ""

#: src/views/monitor/IndexView.vue:162
msgid "Unit %"
msgstr ""

#: src/views/monitor/IndexView.vue:221
#: src/views/monitor/IndexView.vue:307
msgid "Unit MB"
msgstr ""

#: src/views/monitor/IndexView.vue:298
#: src/views/monitor/IndexView.vue:322
msgid "Total Out"
msgstr ""

#: src/views/monitor/IndexView.vue:299
#: src/views/monitor/IndexView.vue:344
msgid "Total In"
msgstr ""

#: src/views/monitor/IndexView.vue:300
#: src/views/monitor/IndexView.vue:366
msgid "Per Second Out"
msgstr ""

#: src/views/monitor/IndexView.vue:301
#: src/views/monitor/IndexView.vue:388
msgid "Per Second In"
msgstr ""

#: src/views/monitor/IndexView.vue:414
#: src/views/monitor/IndexView.vue:420
msgid "Operation successful"
msgstr ""

#: src/views/monitor/IndexView.vue:451
msgid "Clear Monitoring Records"
msgstr ""

#: src/views/monitor/IndexView.vue:454
#: src/views/website/EditView.vue:224
msgid "Are you sure you want to clear?"
msgstr ""

#: src/views/monitor/IndexView.vue:465
msgid "Enable Monitoring"
msgstr ""

#: src/views/monitor/IndexView.vue:468
msgid "Save Days"
msgstr ""

#: src/views/monitor/IndexView.vue:470
msgid "days"
msgstr ""

#: src/views/monitor/IndexView.vue:476
msgid "Time Selection"
msgstr ""

#: src/views/setting/CreateModal.vue:44
msgid "Enter user name"
msgstr ""

#: src/views/setting/CreateModal.vue:53
#: src/views/setting/PasswordModal.vue:40
msgid "Enter user password"
msgstr ""

#: src/views/setting/CreateModal.vue:60
msgid "Enter user email"
msgstr ""

#: src/views/setting/IndexView.vue:47
msgid "Panel is restarting, page will refresh in 3 seconds"
msgstr ""

#: src/views/setting/IndexView.vue:73
msgid "Basic"
msgstr ""

#: src/views/setting/IndexView.vue:76
msgid "Safe"
msgstr ""

#: src/views/setting/PasswordModal.vue:16
#: src/views/setting/TokenModal.vue:183
#: src/views/setting/TwoFaModal.vue:24
#: src/views/ssh/UpdateModal.vue:30
msgid "Updated successfully"
msgstr ""

#: src/views/setting/SettingBase.vue:20
msgid "Stable"
msgstr ""

#: src/views/setting/SettingBase.vue:24
msgid "Beta"
msgstr ""

#: src/views/setting/SettingBase.vue:34
msgid "Modifying panel port/entrance requires corresponding changes in the browser address bar to access the panel!"
msgstr ""

#: src/views/setting/SettingBase.vue:40
#: src/views/setting/SettingBase.vue:41
msgid "Panel Name"
msgstr ""

#: src/views/setting/SettingBase.vue:43
msgid "Language"
msgstr ""

#: src/views/setting/SettingBase.vue:46
msgid "Update Channel"
msgstr ""

#: src/views/setting/SettingBase.vue:50
msgid "8888"
msgstr ""

#: src/views/setting/SettingBase.vue:52
msgid "Default Website Directory"
msgstr ""

#: src/views/setting/SettingBase.vue:53
msgid "/www/wwwroot"
msgstr ""

#: src/views/setting/SettingBase.vue:55
msgid "Default Backup Directory"
msgstr ""

#: src/views/setting/SettingBase.vue:56
msgid "/www/backup"
msgstr ""

#: src/views/setting/SettingSafe.vue:12
msgid "Login Timeout"
msgstr ""

#: src/views/setting/SettingSafe.vue:15
msgid "120"
msgstr ""

#: src/views/setting/SettingSafe.vue:21
#: src/views/website/ProxyBuilderModal.vue:188
msgid "minutes"
msgstr ""

#: src/views/setting/SettingSafe.vue:25
msgid "Access Entrance"
msgstr ""

#: src/views/setting/SettingSafe.vue:26
msgid "admin"
msgstr ""

#: src/views/setting/SettingSafe.vue:28
msgid "Bind Domain"
msgstr ""

#: src/views/setting/SettingSafe.vue:35
msgid "Bind IP"
msgstr ""

#: src/views/setting/SettingSafe.vue:38
msgid "Bind UA"
msgstr ""

#: src/views/setting/SettingSafe.vue:45
msgid "Offline Mode"
msgstr ""

#: src/views/setting/SettingSafe.vue:48
msgid "Auto Update"
msgstr ""

#: src/views/setting/SettingSafe.vue:51
msgid "Panel HTTPS"
msgstr ""

#: src/views/setting/SettingUser.vue:43
msgid "2FA"
msgstr ""

#: src/views/setting/SettingUser.vue:58
msgid "Disabled successfully"
msgstr ""

#: src/views/setting/SettingUser.vue:93
#: src/views/setting/TokenModal.vue:204
msgid "Access Tokens"
msgstr ""

#: src/views/setting/SettingUser.vue:121
msgid "Are you sure you want to delete this user?"
msgstr ""

#: src/views/setting/TokenModal.vue:27
msgid "ID"
msgstr ""

#: src/views/setting/TokenModal.vue:81
msgid "Are you sure you want to delete this access token?"
msgstr ""

#: src/views/setting/TokenModal.vue:144
msgid "Token is only displayed once, please save it before closing the dialog."
msgstr ""

#: src/views/setting/TokenModal.vue:161
msgid "Copy and close"
msgstr ""

#: src/views/setting/TokenModal.vue:168
msgid "Copy failed"
msgstr ""

#: src/views/setting/TokenModal.vue:214
#: src/views/setting/TokenModal.vue:242
msgid "Create Access Token"
msgstr ""

#: src/views/setting/TokenModal.vue:251
#: src/views/setting/TokenModal.vue:284
msgid "IP White List"
msgstr ""

#: src/views/setting/TokenModal.vue:254
#: src/views/setting/TokenModal.vue:287
msgid "127.0.0.1"
msgstr ""

#: src/views/setting/TokenModal.vue:262
#: src/views/setting/TokenModal.vue:295
msgid "Please select the expiration time"
msgstr ""

#: src/views/setting/TokenModal.vue:275
msgid "Modify Access Token"
msgstr ""

#: src/views/setting/TwoFaModal.vue:46
msgid "Enable 2FA"
msgstr ""

#: src/views/setting/TwoFaModal.vue:57
msgid "QR Code"
msgstr ""

#: src/views/setting/TwoFaModal.vue:62
msgid "Scan the QR code with your 2FA app and enter the code below"
msgstr ""

#: src/views/setting/TwoFaModal.vue:66
msgid "If you cannot scan the QR code, please enter the URL below in your 2FA app"
msgstr ""

#: src/views/setting/TwoFaModal.vue:79
msgid "Code"
msgstr ""

#: src/views/setting/TwoFaModal.vue:83
msgid "Enter the code"
msgstr ""

#: src/views/ssh/CreateModal.vue:50
#: src/views/ssh/IndexView.vue:231
msgid "Create Host"
msgstr ""

#: src/views/ssh/CreateModal.vue:73
#: src/views/ssh/UpdateModal.vue:79
msgid "Authentication Method"
msgstr ""

#: src/views/ssh/CreateModal.vue:92
#: src/views/ssh/UpdateModal.vue:98
msgid "Remarks"
msgstr ""

#: src/views/ssh/IndexView.vue:43
msgid "Please create a host first"
msgstr ""

#: src/views/ssh/IndexView.vue:82
msgid "Are you sure you want to delete this host?"
msgstr ""

#: src/views/ssh/IndexView.vue:162
msgid "Connection closed. Please refresh."
msgstr ""

#: src/views/ssh/IndexView.vue:167
msgid "Connection error. Please refresh."
msgstr ""

#: src/views/ssh/UpdateModal.vue:56
msgid "Update Host"
msgstr ""

#: src/views/task/CreateModal.vue:22
msgid "# Enter your script content here"
msgstr ""

#: src/views/task/CreateModal.vue:89
msgid "Create Scheduled Task"
msgstr ""

#: src/views/task/CreateModal.vue:96
#: src/views/task/CronView.vue:36
msgid "Task Type"
msgstr ""

#: src/views/task/CreateModal.vue:100
#: src/views/task/CronView.vue:49
msgid "Run Script"
msgstr ""

#: src/views/task/CreateModal.vue:101
#: src/views/task/CronView.vue:51
msgid "Backup Data"
msgstr ""

#: src/views/task/CreateModal.vue:102
#: src/views/task/CronView.vue:52
msgid "Log Rotation"
msgstr ""

#: src/views/task/CreateModal.vue:107
#: src/views/task/CreateModal.vue:108
#: src/views/task/CronView.vue:29
#: src/views/task/CronView.vue:257
#: src/views/task/CronView.vue:258
#: src/views/task/TaskView.vue:15
msgid "Task Name"
msgstr ""

#: src/views/task/CreateModal.vue:110
#: src/views/task/CronView.vue:73
#: src/views/task/CronView.vue:260
msgid "Task Schedule"
msgstr ""

#: src/views/task/CreateModal.vue:114
msgid "Script Content"
msgstr ""

#: src/views/task/CreateModal.vue:128
msgid "Backup Type"
msgstr ""

#: src/views/task/CreateModal.vue:132
msgid "MySQL Database"
msgstr ""

#: src/views/task/CreateModal.vue:135
msgid "PostgreSQL Database"
msgstr ""

#: src/views/task/CreateModal.vue:164
msgid "Retention Count"
msgstr ""

#: src/views/task/CronView.vue:59
msgid "Enabled"
msgstr ""

#: src/views/task/CronView.vue:93
msgid "Last Update Time"
msgstr ""

#: src/views/task/CronView.vue:144
msgid "Are you sure you want to delete this task?"
msgstr ""

#: src/views/task/CronView.vue:249
msgid "Edit Task"
msgstr ""

#: src/views/task/IndexView.vue:25
msgid "Create Task"
msgstr ""

#: src/views/task/IndexView.vue:32
msgid "System Processes"
msgstr ""

#: src/views/task/IndexView.vue:35
msgid "Panel Tasks"
msgstr ""

#: src/views/task/SystemView.vue:25
msgid "Parent PID"
msgstr ""

#: src/views/task/SystemView.vue:31
msgid "Threads"
msgstr ""

#: src/views/task/SystemView.vue:52
msgid "Sleeping"
msgstr ""

#: src/views/task/SystemView.vue:56
msgid "Idle"
msgstr ""

#: src/views/task/SystemView.vue:58
msgid "Zombie"
msgstr ""

#: src/views/task/SystemView.vue:60
#: src/views/task/TaskView.vue:30
msgid "Waiting"
msgstr ""

#: src/views/task/SystemView.vue:62
msgid "Locked"
msgstr ""

#: src/views/task/SystemView.vue:87
msgid "Start Time"
msgstr ""

#: src/views/task/SystemView.vue:108
msgid "Process %{ pid } has been terminated"
msgstr ""

#: src/views/task/SystemView.vue:115
msgid "Are you sure you want to terminate process %{ pid }?"
msgstr ""

#: src/views/task/SystemView.vue:127
msgid "Terminate"
msgstr ""

#: src/views/task/TaskView.vue:28
msgid "Completed"
msgstr ""

#: src/views/task/TaskView.vue:32
msgid "Failed"
msgstr ""

#: src/views/task/TaskView.vue:46
msgid "Completion Time"
msgstr ""

#: src/views/task/TaskView.vue:136
msgid "If logs cannot be loaded, please disable ad blockers!"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:12
#: src/views/toolbox/BenchmarkView.vue:123
msgid "CPU"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:43
#: src/views/toolbox/BenchmarkView.vue:44
#: src/views/toolbox/BenchmarkView.vue:50
#: src/views/toolbox/BenchmarkView.vue:51
#: src/views/toolbox/BenchmarkView.vue:54
#: src/views/toolbox/BenchmarkView.vue:55
#: src/views/toolbox/BenchmarkView.vue:58
#: src/views/toolbox/BenchmarkView.vue:59
#: src/views/toolbox/BenchmarkView.vue:114
#: src/views/toolbox/BenchmarkView.vue:179
#: src/views/toolbox/BenchmarkView.vue:210
msgid "Pending benchmark"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:90
msgid "Benchmark results are for reference only and may differ from actual performance due to system resource scheduling, caching, and other factors!"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:97
msgid "Benchmarking in progress, it may take some time..."
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:100
msgid "Current project: %{ current }"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:128
msgid "Image Processing"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:134
msgid "Machine Learning"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:140
msgid "Program Compilation"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:146
msgid "AES Encryption"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:152
msgid "Compression/Decompression"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:158
msgid "Physics Simulation"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:164
msgid "JSON Parsing"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:193
msgid "Memory Bandwidth"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:197
msgid "Memory Latency"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:224
msgid "4KB Read"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:230
msgid "4KB Write"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:236
msgid "64KB Read"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:242
msgid "64KB Write"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:248
msgid "1MB Read"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:254
msgid "1MB Write"
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:273
msgid "Benchmarking..."
msgstr ""

#: src/views/toolbox/BenchmarkView.vue:273
msgid "Start Benchmark"
msgstr ""

#: src/views/toolbox/SystemView.vue:125
msgid "DNS modifications will revert to default after system restart."
msgstr ""

#: src/views/toolbox/SystemView.vue:141
msgid "Total %{ total }, used %{ used }, free %{ free }"
msgstr ""

#: src/views/toolbox/SystemView.vue:159
msgid "Hostname"
msgstr ""

#: src/views/toolbox/SystemView.vue:177
msgid "Time"
msgstr ""

#: src/views/toolbox/SystemView.vue:181
msgid "After manually changing the time, it may still be overwritten by system automatic time synchronization."
msgstr ""

#: src/views/toolbox/SystemView.vue:187
msgid "Select Timezone"
msgstr ""

#: src/views/toolbox/SystemView.vue:190
msgid "Please select a timezone"
msgstr ""

#: src/views/toolbox/SystemView.vue:194
msgid "Modify Time"
msgstr ""

#: src/views/toolbox/SystemView.vue:197
msgid "NTP Time Synchronization"
msgstr ""

#: src/views/toolbox/SystemView.vue:199
msgid "Synchronize Time"
msgstr ""

#: src/views/website/BulkCreate.vue:21
msgid "The format is incorrect, please check"
msgstr ""

#: src/views/website/BulkCreate.vue:59
#: src/views/website/IndexView.vue:295
msgid "Website %{ name } created successfully"
msgstr ""

#: src/views/website/BulkCreate.vue:77
#: src/views/website/IndexView.vue:368
msgid "Bulk Create Website"
msgstr ""

#: src/views/website/BulkCreate.vue:88
msgid "Please enter the website name, domain, port, path, and remark in the text area below, one per line."
msgstr ""

#: src/views/website/BulkCreate.vue:96
msgid "name|domain|port|path|remark"
msgstr ""

#: src/views/website/BulkCreate.vue:101
msgid "Name: The name of the website, which will be displayed in the website list, must be unique."
msgstr ""

#: src/views/website/BulkCreate.vue:108
msgid "Domain: The domain name of the website, multiple domains can be separated by commas."
msgstr ""

#: src/views/website/BulkCreate.vue:115
msgid "Port: The port number of the website, multiple ports can be separated by commas."
msgstr ""

#: src/views/website/BulkCreate.vue:121
msgid "Path: The path of the website, can be empty to use the default path."
msgstr ""

#: src/views/website/BulkCreate.vue:124
msgid "Remark: The remark of the website, can be empty."
msgstr ""

#: src/views/website/EditView.vue:54
#: src/views/website/IndexView.vue:209
msgid "Not used"
msgstr ""

#: src/views/website/EditView.vue:83
msgid "Edit Website - %{ name }"
msgstr ""

#: src/views/website/EditView.vue:85
msgid "Edit Website"
msgstr ""

#: src/views/website/EditView.vue:122
msgid "Reset successfully"
msgstr ""

#: src/views/website/EditView.vue:139
msgid "Issued successfully"
msgstr ""

#: src/views/website/EditView.vue:153
msgid "The selected certificate is invalid"
msgstr ""

#: src/views/website/EditView.vue:179
msgid "If you modify the original text, other modifications will not take effect after clicking save!"
msgstr ""

#: src/views/website/EditView.vue:188
msgid "Reset Configuration"
msgstr ""

#: src/views/website/EditView.vue:191
msgid "Are you sure you want to reset the configuration?"
msgstr ""

#: src/views/website/EditView.vue:200
#: src/views/website/ProxyBuilderModal.vue:123
msgid "Generate Reverse Proxy Configuration"
msgstr ""

#: src/views/website/EditView.vue:211
msgid "One-click Certificate Issuance"
msgstr ""

#: src/views/website/EditView.vue:221
msgid "Clear Logs"
msgstr ""

#: src/views/website/EditView.vue:230
msgid "Domain & Listening"
msgstr ""

#: src/views/website/EditView.vue:240
msgid "Listening Address"
msgstr ""

#: src/views/website/EditView.vue:258
msgid "Basic Settings"
msgstr ""

#: src/views/website/EditView.vue:260
msgid "Website Directory"
msgstr ""

#: src/views/website/EditView.vue:263
msgid "Enter website directory (absolute path)"
msgstr ""

#: src/views/website/EditView.vue:266
msgid "Running Directory"
msgstr ""

#: src/views/website/EditView.vue:270
msgid "Enter running directory (needed for Laravel etc.) (absolute path)"
msgstr ""

#: src/views/website/EditView.vue:274
msgid "Default Document"
msgstr ""

#: src/views/website/EditView.vue:277
#: src/views/website/IndexView.vue:448
msgid "PHP Version"
msgstr ""

#: src/views/website/EditView.vue:282
#: src/views/website/IndexView.vue:452
msgid "Select PHP Version"
msgstr ""

#: src/views/website/EditView.vue:287
msgid "Anti-cross-site Attack (PHP)"
msgstr ""

#: src/views/website/EditView.vue:296
msgid "Certificate Information"
msgstr ""

#: src/views/website/EditView.vue:298
msgid "Certificate Validity"
msgstr ""

#: src/views/website/EditView.vue:312
msgid "Domains"
msgstr ""

#: src/views/website/EditView.vue:327
msgid "Main Switch"
msgstr ""

#: src/views/website/EditView.vue:333
msgid "Use Existing Certificate"
msgstr ""

#: src/views/website/EditView.vue:347
msgid "HTTP Redirect"
msgstr ""

#: src/views/website/EditView.vue:350
msgid "OCSP Stapling"
msgstr ""

#: src/views/website/EditView.vue:375
msgid "Rewrite"
msgstr ""

#: src/views/website/EditView.vue:378
msgid "Presets"
msgstr ""

#: src/views/website/EditView.vue:405
msgid "If you do not understand the configuration rules, please do not modify them arbitrarily, otherwise it may cause the website to be inaccessible or panel function abnormalities! If you have already encountered a problem, try resetting the configuration!"
msgstr ""

#: src/views/website/EditView.vue:424
msgid "Access Log"
msgstr ""

#: src/views/website/EditView.vue:428
#: src/views/website/EditView.vue:440
msgid "All logs can be viewed by downloading the file"
msgstr ""

#: src/views/website/EditView.vue:430
#: src/views/website/EditView.vue:442
msgid "view"
msgstr ""

#: src/views/website/EditView.vue:436
msgid "Error Log"
msgstr ""

#: src/views/website/IndexView.vue:24
#: src/views/website/IndexView.vue:411
msgid "Website Name"
msgstr ""

#: src/views/website/IndexView.vue:77
#: src/views/website/IndexView.vue:529
#: src/views/website/IndexView.vue:534
msgid "Remark"
msgstr ""

#: src/views/website/IndexView.vue:133
msgid "Are you sure you want to delete website %{ name }?"
msgstr ""

#: src/views/website/IndexView.vue:144
msgid "Delete website directory"
msgstr ""

#: src/views/website/IndexView.vue:152
msgid "Delete local database with the same name"
msgstr ""

#: src/views/website/IndexView.vue:316
msgid "Please select the websites to delete"
msgstr ""

#: src/views/website/IndexView.vue:351
#: src/views/website/IndexView.vue:545
msgid "Modify Default Page"
msgstr ""

#: src/views/website/IndexView.vue:361
msgid "This will delete the website directory but not the database with the same name. Are you sure you want to delete the selected websites?"
msgstr ""

#: src/views/website/IndexView.vue:372
#: src/views/website/IndexView.vue:402
msgid "Create Website"
msgstr ""

#: src/views/website/IndexView.vue:417
msgid "Recommended to use English for the website name, it cannot be modified after setting"
msgstr ""

#: src/views/website/IndexView.vue:464
msgid "Select Database"
msgstr ""

#: src/views/website/IndexView.vue:492
#: src/views/website/IndexView.vue:497
msgid "Database User"
msgstr ""

#: src/views/website/IndexView.vue:506
#: src/views/website/IndexView.vue:512
msgid "Database Password"
msgstr ""

#: src/views/website/IndexView.vue:523
msgid "Website root directory (if left empty, defaults to website directory/website name)"
msgstr ""

#: src/views/website/IndexView.vue:553
#: src/views/website/IndexView.vue:553
msgid "Default Page"
msgstr ""

#: src/views/website/IndexView.vue:567
#: src/views/website/IndexView.vue:567
msgid "Stop Page"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:24
msgid "Disabled buffer and enabled cache cannot be used simultaneously"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:29
msgid "Matching expression cannot be empty"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:33
msgid "Proxy address cannot be empty"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:37
msgid "Exact match expression must start with /"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:44
msgid "Prefix match expression must start with /"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:50
msgid "Proxy address format error"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:102
msgid "Configuration generated successfully"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:132
msgid "After generating the reverse proxy configuration, the original rewrite rules will be overwritten."
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:139
msgid "If you need to proxy static resources like JS/CSS, please remove the static log recording part from the original configuration."
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:145
msgid "Auto Refresh Resolution"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:148
msgid "Enable SNI"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:151
msgid "Enable Cache"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:154
msgid "Disable Buffer"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:159
msgid "Match Type"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:163
msgid "Exact Match (=)"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:164
msgid "Priority Prefix Match (^~)"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:165
msgid "Normal Prefix Match ( )"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:166
msgid "Case Sensitive Regex Match (~)"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:167
msgid "Case Insensitive Regex Match (~*)"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:171
msgid "Match Expression"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:174
msgid "Proxy Address"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:177
msgid "Send Domain"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:180
msgid "Cache Time"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:186
msgid "Cache time (minutes)"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:191
msgid "Content Replacement"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:196
msgid "Target content"
msgstr ""

#: src/views/website/ProxyBuilderModal.vue:197
msgid "Replacement content"
msgstr ""
