import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { MessagePlugin } from 'tdesign-vue-next';
import router from '@/router';
import { useUserStore } from '@/store/modules/user';

// 响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 创建axios实例
const request = axios.create({
  baseURL: '/api',
  timeout: 30000,
});

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const userStore = useUserStore();
    const token = userStore.token;
    
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }
    
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const res = response.data;
    
    // 如果返回的code不为0，说明有错误
    if (res.code !== 0) {
      MessagePlugin.error(res.message || '请求失败');
      
      // 处理令牌过期
      if (res.code === 1101) {
        const userStore = useUserStore();
        userStore.clearToken();
        router.push('/login');
      }
      
      return Promise.reject(new Error(res.message || '请求失败'));
    }
    
    return res;
  },
  (error: AxiosError) => {
    MessagePlugin.error(error.message || '网络错误');
    return Promise.reject(error);
  }
);

export default request;
