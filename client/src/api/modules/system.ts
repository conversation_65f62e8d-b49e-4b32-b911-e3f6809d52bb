import request from '../request';
import { SystemInfo, NetworkInfo, SystemStatus } from '@/types/system';

// 获取系统信息
export function getSystemInfo() {
  return request<{ system: SystemInfo; network: NetworkInfo[] }>({
    url: '/system/info',
    method: 'get',
  });
}

// 获取系统状态
export function getSystemStatus() {
  return request<SystemStatus>({
    url: '/system/status',
    method: 'get',
  });
}

// 获取实时数据
export function getRealtime(nets: string[] = [], disks: string[] = []) {
  return request({
    url: '/system/realtime',
    method: 'post',
    data: { nets, disks },
  });
}

// 获取统计信息
export function getCountInfo() {
  return request({
    url: '/system/count',
    method: 'get',
  });
}

// 获取服务列表
export function getServices(filter?: string) {
  return request({
    url: '/system/service',
    method: 'get',
    params: { filter },
  });
}

// 获取服务详情
export function getService(name: string) {
  return request({
    url: `/system/service/${name}`,
    method: 'get',
  });
}

// 启动服务
export function startService(name: string) {
  return request({
    url: `/system/service/${name}/start`,
    method: 'post',
  });
}

// 停止服务
export function stopService(name: string) {
  return request({
    url: `/system/service/${name}/stop`,
    method: 'post',
  });
}

// 重启服务
export function restartService(name: string) {
  return request({
    url: `/system/service/${name}/restart`,
    method: 'post',
  });
}
