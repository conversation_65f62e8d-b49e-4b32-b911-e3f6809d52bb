import request from '../request';

// 容器信息接口
export interface Container {
  id: string;
  name: string;
  image: string;
  command: string;
  created: string;
  status: string;
  ports: Port[];
  state: string;
  size: string;
  networks: string[];
  mounts: Mount[];
  labels: Record<string, string>;
  is_running: boolean;
}

// 端口映射接口
export interface Port {
  ip: string;
  private_port: number;
  public_port: number;
  type: string;
}

// 挂载点接口
export interface Mount {
  type: string;
  source: string;
  destination: string;
  mode: string;
  rw: boolean;
}

// 镜像信息接口
export interface Image {
  id: string;
  repository: string;
  tag: string;
  created: string;
  size: string;
}

// 网络信息接口
export interface Network {
  id: string;
  name: string;
  driver: string;
  scope: string;
}

// 数据卷信息接口
export interface Volume {
  name: string;
  driver: string;
  mountpoint: string;
  created_at: string;
  labels: Record<string, string>;
}

// 创建容器选项接口
export interface CreateContainerOptions {
  name: string;
  image: string;
  command?: string;
  ports?: Port[];
  env?: Record<string, string>;
  mounts?: Mount[];
  network?: string;
  restart_policy?: string;
}

// 容器API
export default {
  // 获取容器列表
  list: (all: boolean = false) =>
    request.get<Container[]>('/container', { params: { all } }),

  // 创建容器
  create: (options: CreateContainerOptions) =>
    request.post<{ id: string }>('/container', options),

  // 启动容器
  start: (id: string) =>
    request.post('/container/start', { id }),

  // 停止容器
  stop: (id: string, timeout: number = 10) =>
    request.post('/container/stop', { id, timeout }),

  // 重启容器
  restart: (id: string, timeout: number = 10) =>
    request.post('/container/restart', { id, timeout }),

  // 删除容器
  remove: (id: string, force: boolean = false, volumes: boolean = false) =>
    request.delete('/container', { data: { id, force, volumes } }),

  // 获取容器日志
  logs: (id: string, tail: number = 100) =>
    request.get<{ logs: string }>(`/container/${id}/logs`, { params: { tail } }),

  // 获取容器统计信息
  stats: (id: string) =>
    request.get(`/container/${id}/stats`),

  // 获取镜像列表
  listImages: () =>
    request.get<Image[]>('/container/image'),

  // 拉取镜像
  pullImage: (name: string, tag: string = 'latest') =>
    request.post('/container/image/pull', { name, tag }),

  // 删除镜像
  removeImage: (id: string, force: boolean = false) =>
    request.delete('/container/image', { data: { id, force } }),

  // 获取网络列表
  listNetworks: () =>
    request.get<Network[]>('/container/network'),

  // 创建网络
  createNetwork: (name: string, driver: string = 'bridge') =>
    request.post<{ id: string }>('/container/network', { name, driver }),

  // 删除网络
  removeNetwork: (id: string) =>
    request.delete('/container/network', { data: { id } }),

  // 获取数据卷列表
  listVolumes: () =>
    request.get<Volume[]>('/container/volume'),

  // 创建数据卷
  createVolume: (name: string) =>
    request.post<{ name: string }>('/container/volume', { name }),

  // 删除数据卷
  removeVolume: (name: string, force: boolean = false) =>
    request.delete('/container/volume', { data: { name, force } }),

  // 容器暂停/恢复/强制停止
  pause: (id: string) =>
    request.post('/container/pause', { id }),

  unpause: (id: string) =>
    request.post('/container/unpause', { id }),

  kill: (id: string) =>
    request.post('/container/kill', { id }),

  // 容器重命名
  rename: (id: string, name: string) =>
    request.post('/container/rename', { id, name }),

  // 清理操作
  pruneContainers: () =>
    request.post('/container/prune'),

  pruneImages: () =>
    request.post('/container/image/prune'),

  pruneNetworks: () =>
    request.post('/container/network/prune'),

  pruneVolumes: () =>
    request.post('/container/volume/prune'),

  // Compose 相关
  listCompose: () =>
    request.get('/container/compose'),

  createCompose: (data: any) =>
    request.post('/container/compose', data),

  updateCompose: (name: string, data: any) =>
    request.put(`/container/compose/${name}`, data),

  removeCompose: (name: string) =>
    request.delete(`/container/compose/${name}`),

  upCompose: (name: string, force: boolean = false) =>
    request.post(`/container/compose/${name}/up`, { force }),

  downCompose: (name: string) =>
    request.post(`/container/compose/${name}/down`),
};
