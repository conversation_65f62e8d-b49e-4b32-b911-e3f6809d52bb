import request from '../request';

// 登录参数
interface LoginParams {
  username: string;
  password: string;
  remember_me?: boolean;
}

// 登录响应
interface LoginResponse {
  token: string;
  expires_at: string;
  user_info: {
    id: number;
    username: string;
    email: string;
    is_admin: boolean;
  };
}

// 用户信息
export interface UserInfo {
  id: number;
  username: string;
  email: string;
  is_admin: boolean;
  last_login: string | null;
  created_at: string;
}

// 登录
export function login(data: LoginParams) {
  return request<LoginResponse>({
    url: '/auth/login',
    method: 'post',
    data,
  });
}

// 登出
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post',
  });
}

// 获取用户信息
export function getProfile() {
  return request<UserInfo>({
    url: '/auth/profile',
    method: 'get',
  });
}
