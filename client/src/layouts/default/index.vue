<template>
  <t-layout class="layout">
    <t-header class="header">
      <div class="logo">
        <img src="@/assets/logo.png" alt="Logo" />
        <h1>服务器运维面板</h1>
      </div>
      <div class="header-operations">
        <t-dropdown :options="userMenuOptions" @click="handleUserMenuClick">
          <t-button variant="text">
            <template #icon>
              <t-avatar size="small">{{ userStore.userInfo?.username?.charAt(0) || 'U' }}</t-avatar>
            </template>
            {{ userStore.userInfo?.username || '用户' }}
            <template #suffix>
              <t-icon name="chevron-down" />
            </template>
          </t-button>
        </t-dropdown>
      </div>
    </t-header>

    <t-layout>
      <t-aside class="aside">
        <t-menu theme="light" :value="activeMenu" @change="handleMenuChange">
          <t-menu-item value="dashboard" :icon="() => h(resolveComponent('t-icon'), { name: 'dashboard' })">仪表盘</t-menu-item>
          <t-menu-item value="website" :icon="() => h(resolveComponent('t-icon'), { name: 'internet' })">网站管理</t-menu-item>
          <t-menu-item value="database" :icon="() => h(resolveComponent('t-icon'), { name: 'server' })">数据库管理</t-menu-item>
          <t-menu-item value="file" :icon="() => h(resolveComponent('t-icon'), { name: 'folder' })">文件管理</t-menu-item>
          <t-menu-item value="cron" :icon="() => h(resolveComponent('t-icon'), { name: 'time' })">定时任务</t-menu-item>
          <t-menu-item value="system" :icon="() => h(resolveComponent('t-icon'), { name: 'setting' })">系统管理</t-menu-item>
          <t-submenu value="container" :icon="() => h(resolveComponent('t-icon'), { name: 'code' })">
            <template #title>容器管理</template>
            <t-menu-item value="container-list">容器列表</t-menu-item>
            <t-menu-item value="container-image">镜像管理</t-menu-item>
            <t-menu-item value="container-volume">数据卷</t-menu-item>
            <t-menu-item value="container-network">网络管理</t-menu-item>
            <t-menu-item value="container-compose">编排管理</t-menu-item>
          </t-submenu>
          <t-menu-item value="terminal" :icon="() => h(resolveComponent('t-icon'), { name: 'desktop' })">终端</t-menu-item>
        </t-menu>
      </t-aside>

      <t-content class="content">
        <router-view />
      </t-content>
    </t-layout>
  </t-layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h, resolveComponent } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { MessagePlugin } from 'tdesign-vue-next';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 当前激活的菜单项
const activeMenu = computed(() => {
  const routeName = route.name?.toString().toLowerCase() || 'dashboard';

  // 如果是容器管理页面，根据tab参数确定激活的子菜单
  if (routeName === 'container') {
    const tab = route.query.tab as string;
    if (tab) {
      return `container-${tab}`;
    }
    return 'container-list'; // 默认激活容器列表
  }

  return routeName;
});

// 用户菜单选项
const userMenuOptions = [
  {
    content: '个人设置',
    value: 'settings',
  },
  {
    content: '退出登录',
    value: 'logout',
  },
];

// 处理菜单点击
const handleMenuChange = (value: string) => {
  // 处理容器管理子菜单
  if (value.startsWith('container-')) {
    const tabName = value.replace('container-', '');
    router.push({
      name: 'Container',
      query: { tab: tabName }
    });
    return;
  }

  // 处理容器管理主菜单（默认跳转到容器列表）
  if (value === 'container') {
    router.push({
      name: 'Container',
      query: { tab: 'list' }
    });
    return;
  }

  // 处理其他菜单项
  const routeName = value.charAt(0).toUpperCase() + value.slice(1);
  router.push({ name: routeName });
};

// 处理用户菜单点击
const handleUserMenuClick = async (data: { value: string }) => {
  if (data.value === 'logout') {
    try {
      await userStore.logout();
      MessagePlugin.success('已退出登录');
      router.push('/login');
    } catch (error) {
      console.error('退出登录失败', error);
    }
  } else if (data.value === 'settings') {
    // TODO: 跳转到个人设置页面
    MessagePlugin.info('个人设置功能开发中');
  }
};

// 组件挂载时获取用户信息
onMounted(async () => {
  if (userStore.token && !userStore.userInfo) {
    try {
      await userStore.fetchUserInfo();
    } catch (error) {
      console.error('获取用户信息失败', error);
    }
  }
});
</script>

<style lang="less" scoped>
.layout {
  height: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 64px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 10;

  .logo {
    display: flex;
    align-items: center;

    img {
      height: 32px;
      margin-right: 12px;
    }

    h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
    }
  }
}

.aside {
  width: 232px;
  border-right: 1px solid #e7e7e7;
  background-color: #fff;
}

.content {
  padding: 16px;
  background-color: #f5f5f5;
  overflow-y: auto;
}
</style>
