import { defineStore } from 'pinia';
import { login, logout, getProfile, UserInfo } from '@/api/modules/auth';

interface UserState {
  token: string;
  userInfo: {
    id: number;
    username: string;
    email: string;
    isAdmin: boolean;
  } | null;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: localStorage.getItem('token') || '',
    userInfo: null,
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    isAdmin: (state) => state.userInfo?.isAdmin || false,
  },

  actions: {
    // 设置令牌
    setToken(token: string) {
      this.token = token;
      localStorage.setItem('token', token);
    },

    // 清除令牌
    clearToken() {
      this.token = '';
      localStorage.removeItem('token');
    },

    // 设置用户信息
    setUserInfo(userInfo: UserState['userInfo']) {
      this.userInfo = userInfo;
    },

    // 登录
    async login(username: string, password: string, rememberMe: boolean = false) {
      try {
        const res = await login({ username, password, remember_me: rememberMe });
        this.setToken(res.data.token);

        // 设置用户信息
        if (res.data.user_info) {
          this.setUserInfo({
            id: res.data.user_info.id,
            username: res.data.user_info.username,
            email: res.data.user_info.email,
            isAdmin: res.data.user_info.is_admin,
          });
        } else {
          // 如果登录响应中没有用户信息，则获取用户信息
          await this.fetchUserInfo();
        }

        return res;
      } catch (error) {
        this.clearToken();
        throw error;
      }
    },

    // 获取用户信息
    async fetchUserInfo() {
      if (!this.token) return;

      try {
        const res = await getProfile();
        this.setUserInfo({
          id: res.data.id,
          username: res.data.username,
          email: res.data.email,
          isAdmin: res.data.is_admin,
        });
        return res;
      } catch (error) {
        console.error('获取用户信息失败', error);
      }
    },

    // 登出
    async logout() {
      try {
        if (this.token) {
          await logout();
        }
      } finally {
        this.clearToken();
        this.userInfo = null;
      }
    },
  },
});
