// 全局样式

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 布局样式
.page-container {
  padding: 16px;
  height: 100%;
  box-sizing: border-box;
}

.card-container {
  background-color: #fff;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

// 表单样式
.form-container {
  max-width: 800px;
  margin: 0 auto;
}

// 操作按钮样式
.action-bar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 表格操作列样式
.table-operations {
  .t-button {
    margin-right: 8px;
    &:last-child {
      margin-right: 0;
    }
  }
}
