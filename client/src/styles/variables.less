// 颜色变量
@primary-color: #0052d9;
@success-color: #00a870;
@warning-color: #ed7b2f;
@error-color: #e34d59;

// 文字颜色
@text-color-primary: rgba(0, 0, 0, 0.9);
@text-color-secondary: rgba(0, 0, 0, 0.6);
@text-color-placeholder: rgba(0, 0, 0, 0.4);
@text-color-disabled: rgba(0, 0, 0, 0.26);

// 背景颜色
@bg-color-page: #f5f5f5;
@bg-color-container: #fff;
@bg-color-component: #f5f5f5;

// 边框颜色
@border-color: #e7e7e7;

// 字体大小
@font-size-xs: 10px;
@font-size-s: 12px;
@font-size-base: 14px;
@font-size-l: 16px;
@font-size-xl: 18px;
@font-size-xxl: 20px;

// 间距
@spacing-xs: 4px;
@spacing-s: 8px;
@spacing-m: 16px;
@spacing-l: 24px;
@spacing-xl: 32px;
@spacing-xxl: 48px;

// 圆角
@border-radius-small: 2px;
@border-radius-default: 4px;
@border-radius-large: 8px;
@border-radius-extra-large: 16px;

// 阴影
@shadow-1: 0 1px 2px rgba(0, 0, 0, 0.05);
@shadow-2: 0 2px 4px rgba(0, 0, 0, 0.05);
@shadow-3: 0 4px 8px rgba(0, 0, 0, 0.05);
@shadow-4: 0 8px 16px rgba(0, 0, 0, 0.05);
