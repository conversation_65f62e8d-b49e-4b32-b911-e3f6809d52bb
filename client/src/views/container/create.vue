<template>
  <t-dialog
    v-model:visible="visible"
    header="创建容器"
    :confirm-btn="{ content: '创建', theme: 'primary' }"
    :cancel-btn="{ content: '取消' }"
    @confirm="handleSubmit"
    @close="handleClose"
    width="900px"
    top="5vh"
  >
    <t-form ref="form" :data="formData" :rules="rules" label-width="140px">
      <!-- 基本信息 -->
      <t-form-item label="容器名称" name="name">
        <t-input v-model="formData.name" placeholder="请输入容器名称" />
      </t-form-item>
      
      <t-form-item label="镜像" name="image">
        <t-input v-model="formData.image" placeholder="请输入镜像名称，例如：nginx:latest" />
      </t-form-item>

      <!-- 端口配置 -->
      <t-form-item label="端口配置">
        <t-radio-group v-model="formData.publish_all_ports">
          <t-radio :value="false">映射指定端口</t-radio>
          <t-radio :value="true">暴露所有端口</t-radio>
        </t-radio-group>
      </t-form-item>

      <t-form-item v-if="!formData.publish_all_ports" label="端口映射">
        <t-space direction="vertical" style="width: 100%">
          <div class="port-mapping-header">
            <span>主机IP</span>
            <span>主机端口(起)</span>
            <span>主机端口(止)</span>
            <span>容器端口(起)</span>
            <span>容器端口(止)</span>
            <span>协议</span>
            <span>操作</span>
          </div>
          <t-space v-for="(port, index) in formData.ports" :key="index" align="center" class="port-mapping-row">
            <t-input v-model="port.host" placeholder="可选" style="width: 100px" />
            <t-input-number v-model="port.host_start" :min="1" :max="65535" style="width: 100px" />
            <t-input-number v-model="port.host_end" :min="1" :max="65535" style="width: 100px" />
            <t-input-number v-model="port.container_start" :min="1" :max="65535" style="width: 100px" />
            <t-input-number v-model="port.container_end" :min="1" :max="65535" style="width: 100px" />
            <t-select v-model="port.protocol" style="width: 80px">
              <t-option value="tcp" label="TCP" />
              <t-option value="udp" label="UDP" />
            </t-select>
            <t-button theme="danger" variant="text" @click="removePort(index)">
              <template #icon>
                <t-icon name="close" />
              </template>
            </t-button>
          </t-space>
          <t-button theme="primary" variant="text" @click="addPort">
            <template #icon>
              <t-icon name="add" />
            </template>
            添加端口映射
          </t-button>
        </t-space>
      </t-form-item>

      <!-- 网络配置 -->
      <t-form-item label="网络" name="network">
        <t-select v-model="formData.network" :options="networkOptions" placeholder="选择网络" />
      </t-form-item>

      <!-- 卷挂载 -->
      <t-form-item label="卷挂载">
        <t-space direction="vertical" style="width: 100%">
          <div class="volume-mapping-header">
            <span>主机目录</span>
            <span>容器目录</span>
            <span>权限</span>
            <span>操作</span>
          </div>
          <t-space v-for="(volume, index) in formData.volumes" :key="index" align="center" class="volume-mapping-row">
            <t-input v-model="volume.host" placeholder="/host/path" style="width: 200px" />
            <t-input v-model="volume.container" placeholder="/container/path" style="width: 200px" />
            <t-select v-model="volume.mode" style="width: 100px">
              <t-option value="rw" label="读写" />
              <t-option value="ro" label="只读" />
            </t-select>
            <t-button theme="danger" variant="text" @click="removeVolume(index)">
              <template #icon>
                <t-icon name="close" />
              </template>
            </t-button>
          </t-space>
          <t-button theme="primary" variant="text" @click="addVolume">
            <template #icon>
              <t-icon name="add" />
            </template>
            添加卷挂载
          </t-button>
        </t-space>
      </t-form-item>

      <!-- 命令和入口点 -->
      <t-form-item label="启动命令">
        <t-space direction="vertical" style="width: 100%">
          <t-space v-for="(cmd, index) in formData.command" :key="index" align="center">
            <t-input v-model="formData.command[index]" placeholder="命令参数" style="width: 300px" />
            <t-button theme="danger" variant="text" @click="removeCommand(index)">
              <template #icon>
                <t-icon name="close" />
              </template>
            </t-button>
          </t-space>
          <t-button theme="primary" variant="text" @click="addCommand">
            <template #icon>
              <t-icon name="add" />
            </template>
            添加命令参数
          </t-button>
        </t-space>
      </t-form-item>

      <t-form-item label="入口点">
        <t-space direction="vertical" style="width: 100%">
          <t-space v-for="(entry, index) in formData.entrypoint" :key="index" align="center">
            <t-input v-model="formData.entrypoint[index]" placeholder="入口点参数" style="width: 300px" />
            <t-button theme="danger" variant="text" @click="removeEntrypoint(index)">
              <template #icon>
                <t-icon name="close" />
              </template>
            </t-button>
          </t-space>
          <t-button theme="primary" variant="text" @click="addEntrypoint">
            <template #icon>
              <t-icon name="add" />
            </template>
            添加入口点参数
          </t-button>
        </t-space>
      </t-form-item>

      <!-- 资源限制 -->
      <div class="resource-limits">
        <t-row :gutter="16">
          <t-col :span="8">
            <t-form-item label="内存限制(MB)" name="memory">
              <t-input-number v-model="formData.memory" :min="0" placeholder="0表示无限制" />
            </t-form-item>
          </t-col>
          <t-col :span="8">
            <t-form-item label="CPU限制" name="cpus">
              <t-input-number v-model="formData.cpus" :min="0" :step="0.1" placeholder="0表示无限制" />
            </t-form-item>
          </t-col>
          <t-col :span="8">
            <t-form-item label="CPU权重" name="cpu_shares">
              <t-input-number v-model="formData.cpu_shares" :min="1" :max="1024" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>

      <!-- 高级选项 -->
      <div class="advanced-options">
        <t-row :gutter="16">
          <t-col :span="6">
            <t-form-item label="TTY (-t)">
              <t-switch v-model="formData.tty" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="STDIN (-i)">
              <t-switch v-model="formData.open_stdin" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="自动删除">
              <t-switch v-model="formData.auto_remove" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="特权模式">
              <t-switch v-model="formData.privileged" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>

      <!-- 重启策略 -->
      <t-form-item label="重启策略" name="restart_policy">
        <t-select v-model="formData.restart_policy" :options="restartPolicyOptions" />
      </t-form-item>

      <!-- 环境变量 -->
      <t-form-item label="环境变量">
        <t-space direction="vertical" style="width: 100%">
          <t-space v-for="(env, index) in formData.env" :key="index" align="center">
            <t-input v-model="env.key" placeholder="变量名" style="width: 180px" />
            <span>=</span>
            <t-input v-model="env.value" placeholder="变量值" style="width: 250px" />
            <t-button theme="danger" variant="text" @click="removeEnv(index)">
              <template #icon>
                <t-icon name="close" />
              </template>
            </t-button>
          </t-space>
          <t-button theme="primary" variant="text" @click="addEnv">
            <template #icon>
              <t-icon name="add" />
            </template>
            添加环境变量
          </t-button>
        </t-space>
      </t-form-item>

      <!-- 标签 -->
      <t-form-item label="标签">
        <t-space direction="vertical" style="width: 100%">
          <t-space v-for="(label, index) in formData.labels" :key="index" align="center">
            <t-input v-model="label.key" placeholder="标签名" style="width: 180px" />
            <span>=</span>
            <t-input v-model="label.value" placeholder="标签值" style="width: 250px" />
            <t-button theme="danger" variant="text" @click="removeLabel(index)">
              <template #icon>
                <t-icon name="close" />
              </template>
            </t-button>
          </t-space>
          <t-button theme="primary" variant="text" @click="addLabel">
            <template #icon>
              <t-icon name="add" />
            </template>
            添加标签
          </t-button>
        </t-space>
      </t-form-item>
    </t-form>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import containerApi from '@/api/modules/container';

// Props
interface Props {
  visible: boolean;
  imagePreset?: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  imagePreset: '',
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 状态
const loading = ref(false);
const networkOptions = ref<Array<{ label: string; value: string }>>([]);

// 表单数据
const formData = reactive({
  name: '',
  image: props.imagePreset || '',
  publish_all_ports: false,
  ports: [
    {
      host: '',
      host_start: 80,
      host_end: 80,
      container_start: 80,
      container_end: 80,
      protocol: 'tcp',
    },
  ],
  network: '',
  volumes: [
    {
      host: '/www',
      container: '/www',
      mode: 'rw',
    },
  ],
  command: [] as string[],
  entrypoint: [] as string[],
  memory: 0,
  cpus: 0,
  cpu_shares: 1024,
  tty: false,
  open_stdin: false,
  auto_remove: false,
  privileged: false,
  restart_policy: 'unless-stopped',
  env: [] as Array<{ key: string; value: string }>,
  labels: [] as Array<{ key: string; value: string }>,
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入容器名称', type: 'error' }],
  image: [{ required: true, message: '请输入镜像名称', type: 'error' }],
};

// 重启策略选项
const restartPolicyOptions = [
  { label: '不自动重启', value: 'no' },
  { label: '总是重启', value: 'always' },
  { label: '失败时重启', value: 'on-failure' },
  { label: '除非手动停止', value: 'unless-stopped' },
];

// 端口映射操作
const addPort = () => {
  formData.ports.push({
    host: '',
    host_start: 80,
    host_end: 80,
    container_start: 80,
    container_end: 80,
    protocol: 'tcp',
  });
};

const removePort = (index: number) => {
  formData.ports.splice(index, 1);
};

// 卷挂载操作
const addVolume = () => {
  formData.volumes.push({
    host: '/www',
    container: '/www',
    mode: 'rw',
  });
};

const removeVolume = (index: number) => {
  formData.volumes.splice(index, 1);
};

// 命令操作
const addCommand = () => {
  formData.command.push('');
};

const removeCommand = (index: number) => {
  formData.command.splice(index, 1);
};

// 入口点操作
const addEntrypoint = () => {
  formData.entrypoint.push('');
};

const removeEntrypoint = (index: number) => {
  formData.entrypoint.splice(index, 1);
};

// 环境变量操作
const addEnv = () => {
  formData.env.push({ key: '', value: '' });
};

const removeEnv = (index: number) => {
  formData.env.splice(index, 1);
};

// 标签操作
const addLabel = () => {
  formData.labels.push({ key: '', value: '' });
};

const removeLabel = (index: number) => {
  formData.labels.splice(index, 1);
};

// 获取网络列表
const fetchNetworks = async () => {
  try {
    const res = await containerApi.listNetworks();
    networkOptions.value = (res.data || []).map((network: any) => ({
      label: network.name,
      value: network.id,
    }));
    if (networkOptions.value.length > 0) {
      formData.network = networkOptions.value[0].value;
    }
  } catch (error) {
    console.error('获取网络列表失败:', error);
  }
};

// 提交表单
const handleSubmit = async () => {
  loading.value = true;
  try {
    // 处理表单数据
    const submitData = {
      ...formData,
      env: formData.env.reduce((acc, item) => {
        if (item.key && item.value) {
          acc[item.key] = item.value;
        }
        return acc;
      }, {} as Record<string, string>),
      labels: formData.labels.reduce((acc, item) => {
        if (item.key && item.value) {
          acc[item.key] = item.value;
        }
        return acc;
      }, {} as Record<string, string>),
    };

    await containerApi.create(submitData);
    MessagePlugin.success('创建容器成功');
    emit('success');
    handleClose();
  } catch (error) {
    console.error('创建容器失败:', error);
    MessagePlugin.error('创建容器失败');
  } finally {
    loading.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
};

// 组件挂载时获取网络列表
onMounted(() => {
  fetchNetworks();
});
</script>

<style lang="less" scoped>
.port-mapping-header,
.volume-mapping-header {
  display: grid;
  grid-template-columns: 100px 100px 100px 100px 100px 80px 60px;
  gap: 8px;
  font-weight: 500;
  color: #666;
  margin-bottom: 8px;
  
  span {
    text-align: center;
  }
}

.volume-mapping-header {
  grid-template-columns: 200px 200px 100px 60px;
}

.port-mapping-row,
.volume-mapping-row {
  width: 100%;
}

.resource-limits,
.advanced-options {
  margin: 16px 0;
}

:deep(.t-form-item__label) {
  font-weight: 500;
}
</style>
