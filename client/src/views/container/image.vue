<template>
  <div class="container-image-container page-container">
    <t-card title="镜像管理">
      <template #actions>
        <t-space>
          <t-button theme="primary" @click="handlePullImage">
            <template #icon>
              <t-icon name="download" />
            </template>
            拉取镜像
          </t-button>
          <t-button theme="default" @click="fetchImages">
            <template #icon>
              <t-icon name="refresh" />
            </template>
            刷新
          </t-button>
          <t-button theme="primary" variant="outline" @click="handlePrune">
            <template #icon>
              <t-icon name="delete" />
            </template>
            清理镜像
          </t-button>
        </t-space>
      </template>

      <t-table
        :data="images"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @page-change="onPageChange"
        stripe
        hover
        row-key="id"
      >
        <template #repository="{ row }">
          {{ row.repository }}:{{ row.tag }}
        </template>

        <template #created="{ row }">
          {{ formatDate(row.created) }}
        </template>

        <template #operation="{ row }">
          <t-space>
            <t-tooltip content="创建容器">
              <t-button variant="text" theme="primary" @click="handleCreateContainer(row)">
                <template #icon>
                  <t-icon name="play-circle" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip content="删除镜像">
              <t-button variant="text" theme="danger" @click="handleRemoveImage(row)">
                <template #icon>
                  <t-icon name="delete" />
                </template>
              </t-button>
            </t-tooltip>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 拉取镜像对话框 -->
    <t-dialog
      v-model:visible="pullDialogVisible"
      header="拉取镜像"
      :confirm-btn="{ content: '拉取', theme: 'primary' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="confirmPullImage"
    >
      <t-form ref="form" :data="pullFormData" :rules="pullRules" label-width="100px">
        <t-form-item label="镜像名称" name="name">
          <t-input v-model="pullFormData.name" placeholder="请输入镜像名称，例如：nginx" />
        </t-form-item>
        <t-form-item label="标签" name="tag">
          <t-input v-model="pullFormData.tag" placeholder="请输入标签，默认为latest" />
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 创建容器对话框 -->
    <t-dialog
      v-model:visible="createDialogVisible"
      header="创建容器"
      :confirm-btn="{ content: '创建', theme: 'primary' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="confirmCreateContainer"
      width="700px"
    >
      <t-form ref="form" :data="formData" :rules="rules" label-width="120px">
        <t-form-item label="容器名称" name="name">
          <t-input v-model="formData.name" placeholder="请输入容器名称" />
        </t-form-item>
        <t-form-item label="镜像" name="image">
          <t-input v-model="formData.image" placeholder="请输入镜像名称，例如：nginx:latest" disabled />
        </t-form-item>
        <t-form-item label="命令" name="command">
          <t-input v-model="formData.command" placeholder="可选，容器启动命令" />
        </t-form-item>
        <t-form-item label="端口映射">
          <t-space direction="vertical" style="width: 100%">
            <t-space v-for="(port, index) in formData.ports" :key="index" align="center">
              <t-input v-model="port.ip" placeholder="IP (可选)" style="width: 120px" />
              <span>:</span>
              <t-input-number v-model="port.public_port" placeholder="外部端口" :min="1" :max="65535" style="width: 120px" />
              <span>-></span>
              <t-input-number v-model="port.private_port" placeholder="内部端口" :min="1" :max="65535" style="width: 120px" />
              <t-select v-model="port.type" style="width: 100px">
                <t-option value="tcp" label="TCP" />
                <t-option value="udp" label="UDP" />
              </t-select>
              <t-button theme="danger" variant="text" @click="removePort(index)">
                <template #icon>
                  <t-icon name="close" />
                </template>
              </t-button>
            </t-space>
            <t-button theme="primary" variant="text" @click="addPort">
              <template #icon>
                <t-icon name="add" />
              </template>
              添加端口映射
            </t-button>
          </t-space>
        </t-form-item>
        <t-form-item label="环境变量">
          <t-space direction="vertical" style="width: 100%">
            <t-space v-for="(value, key, index) in formData.env" :key="index" align="center">
              <t-input v-model="envKeys[index]" placeholder="变量名" style="width: 180px" />
              <span>=</span>
              <t-input v-model="envValues[index]" placeholder="变量值" style="width: 250px" />
              <t-button theme="danger" variant="text" @click="removeEnv(index)">
                <template #icon>
                  <t-icon name="close" />
                </template>
              </t-button>
            </t-space>
            <t-button theme="primary" variant="text" @click="addEnv">
              <template #icon>
                <t-icon name="add" />
              </template>
              添加环境变量
            </t-button>
          </t-space>
        </t-form-item>
        <t-form-item label="重启策略" name="restart_policy">
          <t-select v-model="formData.restart_policy">
            <t-option value="no" label="不自动重启" />
            <t-option value="always" label="总是重启" />
            <t-option value="on-failure" label="失败时重启" />
            <t-option value="unless-stopped" label="除非手动停止" />
          </t-select>
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import containerApi, { Image, Port } from '@/api/modules/container';
import dayjs from 'dayjs';

// 状态
const loading = ref(false);
const images = ref<Image[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
});

// 对话框状态
const pullDialogVisible = ref(false);
const createDialogVisible = ref(false);

// 拉取镜像表单数据
const pullFormData = reactive({
  name: '',
  tag: 'latest',
});

// 拉取镜像表单验证规则
const pullRules = {
  name: [{ required: true, message: '请输入镜像名称', type: 'error' }],
};

// 创建容器表单数据
const formData = reactive({
  name: '',
  image: '',
  command: '',
  ports: [] as Port[],
  env: {} as Record<string, string>,
  restart_policy: 'unless-stopped',
});

// 环境变量临时存储
const envKeys = ref<string[]>([]);
const envValues = ref<string[]>([]);

// 创建容器表单验证规则
const rules = {
  name: [{ required: true, message: '请输入容器名称', type: 'error' }],
  image: [{ required: true, message: '请输入镜像名称', type: 'error' }],
};

// 表格列定义
const columns = [
  { colKey: 'repository', title: '镜像', width: 300, cell: 'repository' },
  { colKey: 'id', title: 'ID', width: 200 },
  { colKey: 'created', title: '创建时间', width: 180, cell: 'created' },
  { colKey: 'size', title: '大小', width: 120 },
  { colKey: 'operation', title: '操作', width: 150, fixed: 'right', cell: 'operation' },
];

// 获取镜像列表
const fetchImages = async () => {
  loading.value = true;
  try {
    const res = await containerApi.listImages();
    images.value = res.data || [];
    pagination.total = images.value.length;
  } catch (error) {
    console.error('获取镜像列表失败:', error);
    MessagePlugin.error('获取镜像列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
};

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 处理拉取镜像
const handlePullImage = () => {
  pullFormData.name = '';
  pullFormData.tag = 'latest';
  pullDialogVisible.value = true;
};

// 确认拉取镜像
const confirmPullImage = async () => {
  try {
    await containerApi.pullImage(pullFormData.name, pullFormData.tag);
    MessagePlugin.success('拉取镜像任务已提交，请稍后刷新查看');
    pullDialogVisible.value = false;
    setTimeout(() => {
      fetchImages();
    }, 5000); // 5秒后自动刷新
  } catch (error) {
    console.error('拉取镜像失败:', error);
    MessagePlugin.error('拉取镜像失败');
  }
};

// 处理删除镜像
const handleRemoveImage = (image: Image) => {
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除镜像 "${image.repository}:${image.tag}" 吗？`,
    confirmBtn: {
      content: '删除',
      theme: 'danger',
    },
    onConfirm: async () => {
      try {
        await containerApi.removeImage(image.id);
        MessagePlugin.success('删除镜像成功');
        fetchImages();
      } catch (error) {
        console.error('删除镜像失败:', error);
        MessagePlugin.error('删除镜像失败');
      }
    },
  });
};

// 添加端口映射
const addPort = () => {
  formData.ports.push({
    ip: '',
    private_port: 0,
    public_port: 0,
    type: 'tcp',
  });
};

// 移除端口映射
const removePort = (index: number) => {
  formData.ports.splice(index, 1);
};

// 添加环境变量
const addEnv = () => {
  envKeys.value.push('');
  envValues.value.push('');
};

// 移除环境变量
const removeEnv = (index: number) => {
  envKeys.value.splice(index, 1);
  envValues.value.splice(index, 1);
};

// 处理创建容器
const handleCreateContainer = (image: Image) => {
  // 重置表单
  formData.name = '';
  formData.image = `${image.repository}:${image.tag}`;
  formData.command = '';
  formData.ports = [];
  formData.env = {};
  formData.restart_policy = 'unless-stopped';
  envKeys.value = [];
  envValues.value = [];

  createDialogVisible.value = true;
};

// 确认创建容器
const confirmCreateContainer = async () => {
  // 处理环境变量
  formData.env = {};
  envKeys.value.forEach((key, index) => {
    if (key && envValues.value[index]) {
      formData.env[key] = envValues.value[index];
    }
  });

  try {
    await containerApi.create(formData);
    MessagePlugin.success('创建容器成功');
    createDialogVisible.value = false;
  } catch (error) {
    console.error('创建容器失败:', error);
    MessagePlugin.error('创建容器失败');
  }
};

// 处理清理镜像
const handlePrune = () => {
  DialogPlugin.confirm({
    header: '确认清理',
    body: '确定要清理所有未使用的镜像吗？此操作不可逆。',
    confirmBtn: {
      content: '清理',
      theme: 'danger',
    },
    onConfirm: async () => {
      try {
        await containerApi.pruneImages();
        MessagePlugin.success('清理镜像成功');
        fetchImages();
      } catch (error) {
        console.error('清理镜像失败:', error);
        MessagePlugin.error('清理镜像失败');
      }
    },
  });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchImages();
});
</script>

<style lang="less" scoped>
.container-image-container {
  // 自定义样式
}
</style>
