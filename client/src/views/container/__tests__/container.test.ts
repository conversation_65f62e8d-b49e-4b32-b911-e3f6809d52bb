import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import ContainerIndex from '../index.vue';
import ContainerList from '../list.vue';
import ContainerCreate from '../create.vue';

// Mock TDesign components
vi.mock('tdesign-vue-next', () => ({
  MessagePlugin: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
  },
  DialogPlugin: {
    confirm: vi.fn(),
  },
}));

// Mock API
vi.mock('@/api/modules/container', () => ({
  default: {
    list: vi.fn().mockResolvedValue({ data: [], total: 0 }),
    create: vi.fn().mockResolvedValue({}),
    start: vi.fn().mockResolvedValue({}),
    stop: vi.fn().mockResolvedValue({}),
    restart: vi.fn().mockResolvedValue({}),
    remove: vi.fn().mockResolvedValue({}),
    logs: vi.fn().mockResolvedValue({ data: 'test logs' }),
    stats: vi.fn().mockResolvedValue({ data: {} }),
    listImages: vi.fn().mockResolvedValue({ data: [] }),
    listNetworks: vi.fn().mockResolvedValue({ data: [] }),
    listVolumes: vi.fn().mockResolvedValue({ data: [] }),
    listCompose: vi.fn().mockResolvedValue({ data: [] }),
    pause: vi.fn().mockResolvedValue({}),
    unpause: vi.fn().mockResolvedValue({}),
    kill: vi.fn().mockResolvedValue({}),
    rename: vi.fn().mockResolvedValue({}),
    pruneContainers: vi.fn().mockResolvedValue({}),
    pruneImages: vi.fn().mockResolvedValue({}),
    pruneNetworks: vi.fn().mockResolvedValue({}),
    pruneVolumes: vi.fn().mockResolvedValue({}),
  },
}));

describe('Container Module', () => {
  describe('ContainerIndex', () => {
    it('should render all tabs correctly', () => {
      const wrapper = mount(ContainerIndex);
      
      expect(wrapper.find('[label="容器列表"]').exists()).toBe(true);
      expect(wrapper.find('[label="镜像管理"]').exists()).toBe(true);
      expect(wrapper.find('[label="数据卷"]').exists()).toBe(true);
      expect(wrapper.find('[label="网络管理"]').exists()).toBe(true);
      expect(wrapper.find('[label="编排管理"]').exists()).toBe(true);
    });

    it('should switch tabs correctly', async () => {
      const wrapper = mount(ContainerIndex);
      
      // 默认应该是容器列表标签页
      expect(wrapper.vm.activeTab).toBe('list');
      
      // 切换到镜像管理
      await wrapper.setData({ activeTab: 'image' });
      expect(wrapper.vm.activeTab).toBe('image');
    });
  });

  describe('ContainerList', () => {
    let wrapper: any;

    beforeEach(() => {
      wrapper = mount(ContainerList);
    });

    it('should render container list table', () => {
      expect(wrapper.find('.container-list-container').exists()).toBe(true);
      expect(wrapper.find('t-table').exists()).toBe(true);
    });

    it('should show bulk actions when containers are selected', async () => {
      // 模拟选择容器
      await wrapper.setData({ selectedRowKeys: ['container1', 'container2'] });
      await nextTick();
      
      expect(wrapper.find('.bulk-actions').exists()).toBe(true);
      expect(wrapper.text()).toContain('已选择 2 个容器');
    });

    it('should handle create container action', async () => {
      const createButton = wrapper.find('[data-testid="create-container-btn"]');
      if (createButton.exists()) {
        await createButton.trigger('click');
        expect(wrapper.vm.createDialogVisible).toBe(true);
      }
    });
  });

  describe('ContainerCreate', () => {
    let wrapper: any;

    beforeEach(() => {
      wrapper = mount(ContainerCreate, {
        props: {
          visible: true,
        },
      });
    });

    it('should render create form correctly', () => {
      expect(wrapper.find('t-form').exists()).toBe(true);
      expect(wrapper.find('[name="name"]').exists()).toBe(true);
      expect(wrapper.find('[name="image"]').exists()).toBe(true);
    });

    it('should validate required fields', async () => {
      const form = wrapper.findComponent({ name: 't-form' });
      
      // 尝试提交空表单
      await wrapper.vm.handleSubmit();
      
      // 应该显示验证错误
      expect(wrapper.vm.formData.name).toBe('');
      expect(wrapper.vm.formData.image).toBe('');
    });

    it('should add and remove port mappings', async () => {
      const initialPortsCount = wrapper.vm.formData.ports.length;
      
      // 添加端口映射
      await wrapper.vm.addPort();
      expect(wrapper.vm.formData.ports.length).toBe(initialPortsCount + 1);
      
      // 删除端口映射
      await wrapper.vm.removePort(0);
      expect(wrapper.vm.formData.ports.length).toBe(initialPortsCount);
    });

    it('should add and remove environment variables', async () => {
      const initialEnvCount = wrapper.vm.formData.env.length;
      
      // 添加环境变量
      await wrapper.vm.addEnv();
      expect(wrapper.vm.formData.env.length).toBe(initialEnvCount + 1);
      
      // 删除环境变量
      if (wrapper.vm.formData.env.length > 0) {
        await wrapper.vm.removeEnv(0);
        expect(wrapper.vm.formData.env.length).toBe(initialEnvCount);
      }
    });
  });

  describe('API Integration', () => {
    it('should call correct API endpoints', async () => {
      const containerApi = await import('@/api/modules/container');
      
      // 测试容器列表API
      await containerApi.default.list();
      expect(containerApi.default.list).toHaveBeenCalled();
      
      // 测试容器创建API
      const createData = {
        name: 'test-container',
        image: 'nginx:latest',
      };
      await containerApi.default.create(createData);
      expect(containerApi.default.create).toHaveBeenCalledWith(createData);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const containerApi = await import('@/api/modules/container');
      
      // Mock API error
      containerApi.default.list.mockRejectedValueOnce(new Error('API Error'));
      
      const wrapper = mount(ContainerList);
      
      // 应该处理错误而不崩溃
      await wrapper.vm.fetchContainers();
      expect(wrapper.vm.loading).toBe(false);
    });
  });
});

// 集成测试
describe('Container Module Integration', () => {
  it('should work together as a complete system', async () => {
    const wrapper = mount(ContainerIndex);
    
    // 验证所有子组件都能正常渲染
    expect(wrapper.findComponent(ContainerList).exists()).toBe(true);
    
    // 验证标签页切换功能
    await wrapper.setData({ activeTab: 'image' });
    expect(wrapper.vm.activeTab).toBe('image');
    
    // 验证组件间通信
    const listComponent = wrapper.findComponent(ContainerList);
    if (listComponent.exists()) {
      // 模拟创建容器成功
      await listComponent.vm.handleCreateSuccess();
      // 应该刷新容器列表
      expect(listComponent.vm.fetchContainers).toBeDefined();
    }
  });
});
