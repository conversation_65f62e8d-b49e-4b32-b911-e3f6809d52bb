<template>
  <div class="container-index page-container">
    <t-card>
      <t-tabs v-model="activeTab" placement="top">
        <t-tab-panel value="list" label="容器列表">
          <container-list />
        </t-tab-panel>
        <t-tab-panel value="image" label="镜像管理">
          <container-image />
        </t-tab-panel>
        <t-tab-panel value="volume" label="数据卷">
          <container-volume />
        </t-tab-panel>
        <t-tab-panel value="network" label="网络管理">
          <container-network />
        </t-tab-panel>
        <t-tab-panel value="compose" label="编排管理">
          <container-compose />
        </t-tab-panel>
      </t-tabs>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ContainerList from './list.vue';
import ContainerImage from './image.vue';
import ContainerVolume from './volume.vue';
import ContainerNetwork from './network.vue';
import ContainerCompose from './compose.vue';

const route = useRoute();
const router = useRouter();

// 当前激活的标签页
const activeTab = ref('list');

// 监听路由查询参数变化
watch(
  () => route.query.tab,
  (newTab) => {
    if (newTab && typeof newTab === 'string') {
      activeTab.value = newTab;
    }
  },
  { immediate: true }
);

// 监听标签页变化，更新URL
watch(activeTab, (newTab) => {
  if (newTab !== route.query.tab) {
    router.replace({
      name: 'Container',
      query: { ...route.query, tab: newTab }
    });
  }
});

// 组件挂载时设置默认标签页
onMounted(() => {
  if (!route.query.tab) {
    activeTab.value = 'list';
  }
});
</script>

<style lang="less" scoped>
.container-index {
  .t-tabs {
    :deep(.t-tabs__header) {
      margin-bottom: 20px;
    }

    :deep(.t-tabs__content) {
      padding: 0;
    }
  }
}
</style>
