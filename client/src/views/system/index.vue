<template>
  <div class="system-container page-container">
    <t-tabs v-model="activeTab">
      <t-tab-panel value="info" label="系统信息">
        <t-card>
          <template #actions>
            <t-button theme="primary" variant="text" @click="fetchSystemInfo">
              <template #icon>
                <t-icon name="refresh" />
              </template>
              刷新
            </t-button>
          </template>

          <t-loading :loading="systemInfoLoading">
            <div class="system-info-container" v-if="systemInfo">
              <div class="system-info-section">
                <h3>基本信息</h3>
                <t-row :gutter="[16, 16]">
                  <t-col :span="6">
                    <t-card title="主机名" :bordered="false" class="info-card">
                      <div class="info-value">{{ systemInfo.hostname }}</div>
                    </t-card>
                  </t-col>
                  <t-col :span="6">
                    <t-card title="操作系统" :bordered="false" class="info-card">
                      <div class="info-value">{{ systemInfo.os }}</div>
                    </t-card>
                  </t-col>
                  <t-col :span="6">
                    <t-card title="内核版本" :bordered="false" class="info-card">
                      <div class="info-value">{{ systemInfo.kernel }}</div>
                    </t-card>
                  </t-col>
                  <t-col :span="6">
                    <t-card title="架构" :bordered="false" class="info-card">
                      <div class="info-value">{{ systemInfo.arch }}</div>
                    </t-card>
                  </t-col>
                </t-row>
              </div>

              <div class="system-info-section">
                <h3>资源使用</h3>
                <t-row :gutter="[16, 16]">
                  <t-col :span="8">
                    <t-card title="CPU使用率" :bordered="false" class="info-card">
                      <t-progress
                        :percentage="systemStatus?.cpu_usage || 0"
                        :color="{ from: '#0052D9', to: '#0052D9' }"
                        :track-color="'#E7E7E7'"
                        :stroke-width="8"
                      />
                      <div class="info-detail">
                        <div>型号: {{ systemInfo.cpu.model }}</div>
                        <div>核心数: {{ systemInfo.cpu.cores }}</div>
                        <div>线程数: {{ systemInfo.cpu.threads }}</div>
                      </div>
                    </t-card>
                  </t-col>
                  <t-col :span="8">
                    <t-card title="内存使用率" :bordered="false" class="info-card">
                      <t-progress
                        :percentage="systemStatus?.memory_usage || 0"
                        :color="{ from: '#0052D9', to: '#0052D9' }"
                        :track-color="'#E7E7E7'"
                        :stroke-width="8"
                      />
                      <div class="info-detail">
                        <div>总内存: {{ formatBytes(systemInfo.memory.total) }}</div>
                        <div>已用: {{ formatBytes(systemInfo.memory.used) }}</div>
                        <div>可用: {{ formatBytes(systemInfo.memory.available) }}</div>
                      </div>
                    </t-card>
                  </t-col>
                  <t-col :span="8">
                    <t-card title="磁盘使用率" :bordered="false" class="info-card">
                      <t-progress
                        :percentage="systemStatus?.disk_usage || 0"
                        :color="{ from: '#0052D9', to: '#0052D9' }"
                        :track-color="'#E7E7E7'"
                        :stroke-width="8"
                      />
                      <div class="info-detail">
                        <div>总空间: {{ formatBytes(systemInfo.disk.total) }}</div>
                        <div>已用: {{ formatBytes(systemInfo.disk.used) }}</div>
                        <div>可用: {{ formatBytes(systemInfo.disk.free) }}</div>
                      </div>
                    </t-card>
                  </t-col>
                </t-row>
              </div>

              <div class="system-info-section">
                <h3>系统负载</h3>
                <t-row :gutter="[16, 16]">
                  <t-col :span="8">
                    <t-card title="1分钟负载" :bordered="false" class="info-card">
                      <div class="info-value">{{ systemStatus?.load_average?.[0] || 0 }}</div>
                    </t-card>
                  </t-col>
                  <t-col :span="8">
                    <t-card title="5分钟负载" :bordered="false" class="info-card">
                      <div class="info-value">{{ systemStatus?.load_average?.[1] || 0 }}</div>
                    </t-card>
                  </t-col>
                  <t-col :span="8">
                    <t-card title="15分钟负载" :bordered="false" class="info-card">
                      <div class="info-value">{{ systemStatus?.load_average?.[2] || 0 }}</div>
                    </t-card>
                  </t-col>
                </t-row>
              </div>

              <div class="system-info-section">
                <h3>网络信息</h3>
                <t-table
                  :data="networkInfo"
                  :columns="networkColumns"
                  :pagination="{ pageSize: 5 }"
                  row-key="interface"
                  size="small"
                >
                  <template #rx_bytes="{ row }">
                    {{ formatBytes(row.rx_bytes) }}
                  </template>
                  <template #tx_bytes="{ row }">
                    {{ formatBytes(row.tx_bytes) }}
                  </template>
                </t-table>
              </div>
            </div>
          </t-loading>
        </t-card>
      </t-tab-panel>

      <t-tab-panel value="services" label="服务管理">
        <t-card>
          <template #actions>
            <t-input
              v-model="serviceFilter"
              placeholder="搜索服务"
              clearable
              style="width: 200px"
            >
              <template #suffix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
            <t-button theme="primary" variant="text" @click="fetchServices">
              <template #icon>
                <t-icon name="refresh" />
              </template>
              刷新
            </t-button>
          </template>

          <t-table
            :data="filteredServices"
            :columns="serviceColumns"
            :loading="servicesLoading"
            :pagination="{ pageSize: 10 }"
            row-key="name"
          >
            <template #status="{ row }">
              <t-tag :theme="getServiceStatusTheme(row.activeState)">
                {{ row.activeState }}
              </t-tag>
            </template>

            <template #op="{ row }">
              <t-space>
                <t-button
                  v-if="row.activeState !== 'active'"
                  theme="success"
                  variant="text"
                  @click="handleServiceAction('start', row)"
                >
                  启动
                </t-button>
                <t-button
                  v-if="row.activeState === 'active'"
                  theme="warning"
                  variant="text"
                  @click="handleServiceAction('stop', row)"
                >
                  停止
                </t-button>
                <t-button
                  theme="primary"
                  variant="text"
                  @click="handleServiceAction('restart', row)"
                >
                  重启
                </t-button>
              </t-space>
            </template>
          </t-table>
        </t-card>
      </t-tab-panel>

      <t-tab-panel value="firewall" label="防火墙管理">
        <t-card>
          <template #actions>
            <t-button theme="primary" @click="handleAddFirewallRule">
              <template #icon>
                <t-icon name="add" />
              </template>
              添加规则
            </t-button>
          </template>

          <t-table
            :data="firewallRules"
            :columns="firewallColumns"
            :loading="firewallLoading"
            row-key="id"
          >
            <template #status="{ row }">
              <t-tag :theme="row.enabled ? 'success' : 'danger'">
                {{ row.enabled ? '启用' : '禁用' }}
              </t-tag>
            </template>

            <template #op="{ row }">
              <t-space>
                <t-button theme="primary" variant="text" @click="handleEditFirewallRule(row)">
                  编辑
                </t-button>
                <t-button theme="danger" variant="text" @click="handleDeleteFirewallRule(row)">
                  删除
                </t-button>
              </t-space>
            </template>
          </t-table>
        </t-card>
      </t-tab-panel>

      <t-tab-panel value="settings" label="系统设置">
        <t-card>
          <t-form label-width="120px">
            <t-form-item label="面板端口">
              <t-input-number v-model="settings.port" :min="1" :max="65535" />
            </t-form-item>

            <t-form-item label="面板访问地址">
              <t-select v-model="settings.address">
                <t-option value="0.0.0.0" label="所有地址 (0.0.0.0)" />
                <t-option value="127.0.0.1" label="本地地址 (127.0.0.1)" />
              </t-select>
            </t-form-item>

            <t-form-item label="日志级别">
              <t-select v-model="settings.logLevel">
                <t-option value="debug" label="调试 (Debug)" />
                <t-option value="info" label="信息 (Info)" />
                <t-option value="warn" label="警告 (Warn)" />
                <t-option value="error" label="错误 (Error)" />
              </t-select>
            </t-form-item>

            <t-form-item label="自动备份">
              <t-switch v-model="settings.autoBackup" />
            </t-form-item>

            <t-form-item label="备份保留天数" v-if="settings.autoBackup">
              <t-input-number v-model="settings.backupDays" :min="1" :max="365" />
            </t-form-item>

            <t-form-item>
              <t-button theme="primary" @click="saveSettings">保存设置</t-button>
            </t-form-item>
          </t-form>
        </t-card>
      </t-tab-panel>
    </t-tabs>

    <!-- 防火墙规则对话框 -->
    <t-dialog
      v-model:visible="firewallDialogVisible"
      :header="firewallFormMode === 'add' ? '添加防火墙规则' : '编辑防火墙规则'"
      width="600px"
      :confirm-btn="{ content: '保存', theme: 'primary' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="saveFirewallRule"
    >
      <t-form
        ref="firewallForm"
        :data="firewallFormData"
        :rules="firewallFormRules"
        label-width="100px"
      >
        <t-form-item label="规则名称" name="name">
          <t-input v-model="firewallFormData.name" placeholder="请输入规则名称" />
        </t-form-item>

        <t-form-item label="端口" name="port">
          <t-input v-model="firewallFormData.port" placeholder="请输入端口，例如：80,443,8080-8090" />
        </t-form-item>

        <t-form-item label="协议" name="protocol">
          <t-radio-group v-model="firewallFormData.protocol">
            <t-radio value="tcp">TCP</t-radio>
            <t-radio value="udp">UDP</t-radio>
            <t-radio value="tcp,udp">TCP/UDP</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="来源IP" name="source">
          <t-input v-model="firewallFormData.source" placeholder="请输入来源IP，留空表示任意" />
        </t-form-item>

        <t-form-item label="状态">
          <t-switch v-model="firewallFormData.enabled" />
        </t-form-item>

        <t-form-item label="描述" name="description">
          <t-textarea
            v-model="firewallFormData.description"
            placeholder="请输入描述信息"
            :autosize="{ minRows: 2, maxRows: 5 }"
          />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { getSystemInfo, getSystemStatus, getServices, startService, stopService, restartService } from '@/api/modules/system';
import type { SystemInfo, NetworkInfo, SystemStatus } from '@/types/system';

// 当前激活的标签页
const activeTab = ref('info');

// 系统信息
const systemInfo = ref<SystemInfo | null>(null);
const systemStatus = ref<SystemStatus | null>(null);
const networkInfo = ref<NetworkInfo[]>([]);
const systemInfoLoading = ref(false);

// 网络信息表格列定义
const networkColumns = [
  { colKey: 'interface', title: '网卡', width: '120' },
  { colKey: 'rx_bytes', title: '接收流量', width: '120', cell: 'rx_bytes' },
  { colKey: 'tx_bytes', title: '发送流量', width: '120', cell: 'tx_bytes' },
  { colKey: 'rx_packets', title: '接收包数', width: '120' },
  { colKey: 'tx_packets', title: '发送包数', width: '120' },
];

// 格式化字节数
const formatBytes = (bytes: number, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

// 获取系统信息
const fetchSystemInfo = async () => {
  systemInfoLoading.value = true;
  try {
    const res = await getSystemInfo();
    systemInfo.value = res.data.system;
    networkInfo.value = res.data.network;

    // 获取系统状态
    const statusRes = await getSystemStatus();
    systemStatus.value = statusRes.data;
  } catch (error: any) {
    console.error('获取系统信息失败', error);
    MessagePlugin.error('获取系统信息失败: ' + (error.message || '未知错误'));
  } finally {
    systemInfoLoading.value = false;
  }
};

// 服务管理
const services = ref<any[]>([]);
const servicesLoading = ref(false);
const serviceFilter = ref('');

// 服务表格列定义
const serviceColumns = [
  { colKey: 'name', title: '服务名称', width: '200' },
  { colKey: 'description', title: '描述', ellipsis: true },
  { colKey: 'status', title: '状态', width: '120', cell: 'status' },
  { colKey: 'op', title: '操作', width: '200', fixed: 'right', cell: 'op' },
];

// 过滤后的服务列表
const filteredServices = computed(() => {
  if (!serviceFilter.value) return services.value;

  return services.value.filter(service =>
    service.name.toLowerCase().includes(serviceFilter.value.toLowerCase()) ||
    service.description.toLowerCase().includes(serviceFilter.value.toLowerCase())
  );
});

// 获取服务列表
const fetchServices = async () => {
  servicesLoading.value = true;
  try {
    const res = await getServices();
    services.value = res.data;
    servicesLoading.value = false;
  } catch (error: any) {
    console.error('获取服务列表失败', error);
    MessagePlugin.error('获取服务列表失败: ' + (error.message || '未知错误'));
    servicesLoading.value = false;
  }
};

// 处理服务操作
const handleServiceAction = async (action: string, service: any) => {
  try {
    if (action === 'start') {
      await startService(service.name);
      MessagePlugin.success(`服务 ${service.name} 已启动`);
    } else if (action === 'stop') {
      await stopService(service.name);
      MessagePlugin.success(`服务 ${service.name} 已停止`);
    } else if (action === 'restart') {
      await restartService(service.name);
      MessagePlugin.success(`服务 ${service.name} 已重启`);
    }

    // 刷新服务列表
    await fetchServices();
  } catch (error: any) {
    console.error(`服务操作失败: ${action}`, error);
    MessagePlugin.error(`服务操作失败: ${error.message || '未知错误'}`);
  }
};

// 获取服务状态主题
const getServiceStatusTheme = (status: string) => {
  if (status === 'active') return 'success';
  if (status === 'inactive') return 'danger';
  return 'warning';
};

// 防火墙管理
const firewallRules = ref([]);
const firewallLoading = ref(false);
const firewallDialogVisible = ref(false);
const firewallFormMode = ref('add');
const firewallFormData = reactive({
  id: 0,
  name: '',
  port: '',
  protocol: 'tcp',
  source: '',
  enabled: true,
  description: '',
});

// 防火墙表格列定义
const firewallColumns = [
  { colKey: 'name', title: '规则名称', width: '150' },
  { colKey: 'port', title: '端口', width: '150' },
  { colKey: 'protocol', title: '协议', width: '100' },
  { colKey: 'source', title: '来源IP', width: '150' },
  { colKey: 'status', title: '状态', width: '100', cell: 'status' },
  { colKey: 'description', title: '描述', ellipsis: true },
  { colKey: 'op', title: '操作', width: '150', fixed: 'right', cell: 'op' },
];

// 防火墙表单验证规则
const firewallFormRules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
  protocol: [{ required: true, message: '请选择协议', trigger: 'change' }],
};

// 获取防火墙规则
const fetchFirewallRules = () => {
  firewallLoading.value = true;
  // TODO: 实现API调用
  // 模拟数据
  setTimeout(() => {
    firewallRules.value = [
      {
        id: 1,
        name: 'HTTP',
        port: '80',
        protocol: 'tcp',
        source: '',
        enabled: true,
        description: 'HTTP服务',
      },
      {
        id: 2,
        name: 'HTTPS',
        port: '443',
        protocol: 'tcp',
        source: '',
        enabled: true,
        description: 'HTTPS服务',
      },
      {
        id: 3,
        name: 'SSH',
        port: '22',
        protocol: 'tcp',
        source: '',
        enabled: true,
        description: 'SSH服务',
      },
    ];
    firewallLoading.value = false;
  }, 500);
};

// 添加防火墙规则
const handleAddFirewallRule = () => {
  firewallFormMode.value = 'add';
  resetFirewallForm();
  firewallDialogVisible.value = true;
};

// 编辑防火墙规则
const handleEditFirewallRule = (row: any) => {
  firewallFormMode.value = 'edit';
  Object.assign(firewallFormData, row);
  firewallDialogVisible.value = true;
};

// 删除防火墙规则
const handleDeleteFirewallRule = (row: any) => {
  MessagePlugin.warning('删除防火墙规则功能开发中');
};

// 保存防火墙规则
const saveFirewallRule = () => {
  // TODO: 实现API调用
  MessagePlugin.success(`${firewallFormMode.value === 'add' ? '添加' : '编辑'}防火墙规则成功`);
  firewallDialogVisible.value = false;
  fetchFirewallRules();
};

// 重置防火墙表单
const resetFirewallForm = () => {
  firewallFormData.id = 0;
  firewallFormData.name = '';
  firewallFormData.port = '';
  firewallFormData.protocol = 'tcp';
  firewallFormData.source = '';
  firewallFormData.enabled = true;
  firewallFormData.description = '';
};

// 系统设置
const settings = reactive({
  port: 8080,
  address: '0.0.0.0',
  logLevel: 'info',
  autoBackup: true,
  backupDays: 7,
});

// 保存系统设置
const saveSettings = () => {
  // TODO: 实现API调用
  MessagePlugin.success('保存设置成功');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchSystemInfo();
  fetchServices();
  fetchFirewallRules();
});
</script>

<style lang="less" scoped>
.system-container {
  .system-info-container {
    .system-info-section {
      margin-bottom: 24px;

      h3 {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 500;
      }

      .info-card {
        height: 100%;

        .info-value {
          font-size: 24px;
          font-weight: 500;
          text-align: center;
          margin-top: 8px;
        }

        .info-detail {
          margin-top: 16px;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.6);
        }
      }
    }
  }
}
</style>
