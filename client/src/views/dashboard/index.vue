<template>
  <div class="dashboard-container page-container">
    <t-loading :loading="loading">
      <!-- 面板信息头部 -->
      <div class="panel-header-section">
        <t-card hover-shadow>
          <div class="panel-header">
            <div class="panel-info">
              <h2>Linux运维面板</h2>
              <p class="panel-version">版本: {{ systemInfo?.panel_version || '1.0.0' }}</p>
            </div>
            <div class="panel-actions">
              <t-button theme="primary" @click="refreshData">
                <template #icon>
                  <t-icon name="refresh" />
                </template>
                刷新
              </t-button>
            </div>
          </div>

          <!-- 统计信息 -->
          <t-row :gutter="16" class="stats-row">
            <t-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ countInfo.website }}</div>
                <div class="stat-label">网站</div>
              </div>
            </t-col>
            <t-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ countInfo.database }}</div>
                <div class="stat-label">数据库</div>
              </div>
            </t-col>
            <t-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ countInfo.ftp }}</div>
                <div class="stat-label">FTP</div>
              </div>
            </t-col>
            <t-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ countInfo.cron }}</div>
                <div class="stat-label">定时任务</div>
              </div>
            </t-col>
          </t-row>
        </t-card>
      </div>

      <!-- 资源概览 -->
      <div class="resource-overview-section">
        <t-card title="资源概览" hover-shadow>
          <t-row :gutter="24" v-if="realtime">
            <!-- 系统负载 -->
            <t-col :span="6">
              <div class="resource-item">
                <div class="resource-header">
                  <t-icon name="chart-bubble" size="20px" />
                  <span>系统负载</span>
                </div>
                <div class="resource-progress">
                  <t-progress
                    :percentage="Math.round(getLoadPercentage())"
                    :color="getLoadColor()"
                    size="large"
                    :label="false"
                  />
                </div>
                <div class="resource-detail">
                  <div>{{ getLoadStatus() }}</div>
                  <div class="load-values">
                    <span>1分钟: {{ realtime.load?.load1?.toFixed(2) || '0.00' }}</span>
                    <span>5分钟: {{ realtime.load?.load5?.toFixed(2) || '0.00' }}</span>
                    <span>15分钟: {{ realtime.load?.load15?.toFixed(2) || '0.00' }}</span>
                  </div>
                </div>
              </div>
            </t-col>

            <!-- CPU使用率 -->
            <t-col :span="6">
              <div class="resource-item">
                <div class="resource-header">
                  <t-icon name="cpu" size="20px" />
                  <span>CPU</span>
                </div>
                <div class="resource-progress">
                  <t-progress
                    :percentage="realtime.percent || 0"
                    :color="getCpuColor(realtime.percent)"
                    size="large"
                    :label="false"
                  />
                </div>
                <div class="resource-detail">
                  <div>{{ realtime.percent?.toFixed(1) || 0 }}%</div>
                  <div>{{ cores }} 核心</div>
                </div>
              </div>
            </t-col>

            <!-- 内存使用率 -->
            <t-col :span="6">
              <div class="resource-item">
                <div class="resource-header">
                  <t-icon name="memory" size="20px" />
                  <span>内存</span>
                </div>
                <div class="resource-progress">
                  <t-progress
                    :percentage="realtime.mem?.usedPercent || 0"
                    :color="getMemoryColor(realtime.mem?.usedPercent)"
                    size="large"
                    :label="false"
                  />
                </div>
                <div class="resource-detail">
                  <div>{{ realtime.mem?.usedPercent?.toFixed(1) || 0 }}%</div>
                  <div>{{ formatBytes(realtime.mem?.total) }}</div>
                </div>
              </div>
            </t-col>

            <!-- 磁盘使用率 -->
            <t-col :span="6" v-if="realtime.disk_usage && realtime.disk_usage.length > 0">
              <div class="resource-item">
                <div class="resource-header">
                  <t-icon name="root-list" size="20px" />
                  <span>{{ realtime.disk_usage[0].path }}</span>
                </div>
                <div class="resource-progress">
                  <t-progress
                    :percentage="realtime.disk_usage[0].usedPercent || 0"
                    :color="getDiskColor(realtime.disk_usage[0].usedPercent)"
                    size="large"
                    :label="false"
                  />
                </div>
                <div class="resource-detail">
                  <div>{{ realtime.disk_usage[0].usedPercent?.toFixed(1) || 0 }}%</div>
                  <div>{{ formatBytes(realtime.disk_usage[0].used) }} / {{ formatBytes(realtime.disk_usage[0].total) }}</div>
                </div>
              </div>
            </t-col>
          </t-row>
          <t-skeleton v-else :loading="true" :row-col="[{ width: '100%', height: '200px' }]" />
        </t-card>
      </div>

      <!-- 主要内容区域 -->
      <t-row :gutter="16">
        <!-- 左侧：系统信息和服务状态 -->
        <t-col :span="12">
          <!-- 系统信息 -->
          <div class="system-info-section">
            <t-card title="系统信息" hover-shadow>
              <t-table :data="systemInfoData" :columns="systemInfoColumns" :pagination="false" size="medium">
              </t-table>
            </t-card>
          </div>

          <!-- 服务状态 -->
          <div class="services-section">
            <t-card title="服务状态" hover-shadow>
              <template #actions>
                <t-button theme="primary" variant="text" @click="fetchServices">
                  <template #icon>
                    <t-icon name="refresh" />
                  </template>
                  刷新
                </t-button>
              </template>

              <t-table
                :data="services"
                :columns="serviceColumns"
                :loading="servicesLoading"
                :pagination="{ pageSize: 5 }"
                row-key="name"
                size="medium"
              >
                <template #status="{ row }">
                  <t-tag :theme="getServiceStatusTheme(row.activeState)">
                    {{ row.activeState }}
                  </t-tag>
                </template>

                <template #op="{ row }">
                  <t-space>
                    <t-button
                      v-if="row.activeState !== 'active'"
                      theme="success"
                      variant="text"
                      @click="handleServiceAction('start', row)"
                    >
                      启动
                    </t-button>
                    <t-button
                      v-if="row.activeState === 'active'"
                      theme="warning"
                      variant="text"
                      @click="handleServiceAction('stop', row)"
                    >
                      停止
                    </t-button>
                    <t-button
                      theme="primary"
                      variant="text"
                      @click="handleServiceAction('restart', row)"
                    >
                      重启
                    </t-button>
                  </t-space>
                </template>
              </t-table>
            </t-card>
          </div>
        </t-col>

        <!-- 右侧：实时监控图表 -->
        <t-col :span="12">
          <div class="monitoring-section">
            <t-card title="实时监控" hover-shadow>
              <div class="chart-controls">
                <t-radio-group v-model="chartType" variant="default-filled">
                  <t-radio-button value="net">网络</t-radio-button>
                  <t-radio-button value="disk">磁盘</t-radio-button>
                </t-radio-group>

                <t-select
                  v-model="unitType"
                  :options="units"
                  placeholder="选择单位"
                  style="width: 80px; margin-left: 16px;"
                  @change="clearCurrent"
                />
              </div>

              <!-- 实时数据标签 -->
              <div class="realtime-tags" v-if="chartType === 'net'">
                <t-tag>总发送: {{ formatBytes(total.netBytesSent) }}</t-tag>
                <t-tag>总接收: {{ formatBytes(total.netBytesRecv) }}</t-tag>
                <t-tag>实时发送: {{ formatBytes(current.netBytesSent) }}/s</t-tag>
                <t-tag>实时接收: {{ formatBytes(current.netBytesRecv) }}/s</t-tag>
              </div>

              <div class="realtime-tags" v-if="chartType === 'disk'">
                <t-tag>读取: {{ formatBytes(total.diskReadBytes) }}</t-tag>
                <t-tag>写入: {{ formatBytes(total.diskWriteBytes) }}</t-tag>
                <t-tag>实时读写: {{ formatBytes(current.diskRWBytes) }}/s</t-tag>
                <t-tag>读写延迟: {{ current.diskRWTime }}ms</t-tag>
              </div>

              <!-- 图表容器 -->
              <div class="chart-container" ref="chartContainer">
                <div v-if="!chartData.length" class="chart-placeholder">
                  <t-icon name="chart-line" size="48px" />
                  <p>正在收集数据...</p>
                </div>
              </div>
            </t-card>
          </div>
        </t-col>
      </t-row>
    </t-loading>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, reactive } from 'vue';
import { getSystemInfo, getServices, startService, stopService, restartService, getRealtime, getCountInfo } from '@/api/modules/system';
import { MessagePlugin } from 'tdesign-vue-next';
import type { SystemInfo } from '@/types/system';

// 系统信息
const systemInfo = ref<SystemInfo | null>(null);
const loading = ref(true);

// 实时数据
const realtime = ref<any>(null);
const cores = ref(0);

// 统计信息
const countInfo = reactive({
  website: 0,
  database: 0,
  ftp: 0,
  cron: 0
});

// 服务列表
const services = ref([]);
const servicesLoading = ref(false);

// 图表相关
const chartType = ref('net');
const unitType = ref('KB');
const chartData = ref([]);
const chartContainer = ref();

// 实时数据存储
const total = reactive({
  diskReadBytes: 0,
  diskWriteBytes: 0,
  diskRWBytes: 0,
  diskRWTime: 0,
  netBytesSent: 0,
  netBytesRecv: 0
});

const current = reactive({
  diskReadBytes: 0,
  diskWriteBytes: 0,
  diskRWBytes: 0,
  diskRWTime: 0,
  netBytesSent: 0,
  netBytesRecv: 0,
  time: 0
});

// 单位选项
const units = [
  { label: 'B', value: 'B' },
  { label: 'KB', value: 'KB' },
  { label: 'MB', value: 'MB' },
  { label: 'GB', value: 'GB' }
];

// 服务表格列定义
const serviceColumns = [
  { colKey: 'name', title: '服务名称', width: '200' },
  { colKey: 'description', title: '描述', ellipsis: true },
  { colKey: 'status', title: '状态', width: '120', cell: 'status' },
  { colKey: 'op', title: '操作', width: '160', fixed: 'right', cell: 'op' },
];

// 系统信息表格列定义
const systemInfoColumns = [
  { colKey: 'label', title: '项目', width: '120' },
  { colKey: 'value', title: '值', ellipsis: true },
];

// 系统信息数据
const systemInfoData = computed(() => {
  if (!systemInfo.value) return [];

  return [
    { label: '主机名', value: systemInfo.value.hostname || '未知' },
    { label: '操作系统', value: systemInfo.value.os || '未知' },
    { label: '内核版本', value: systemInfo.value.kernel || '未知' },
    { label: '系统架构', value: systemInfo.value.arch || '未知' },
    { label: '运行时间', value: formatUptime(systemInfo.value.uptime) },
    { label: '系统时间', value: formatDateTime(systemInfo.value.time) },
  ];
});

// 获取系统信息
const fetchSystemInfo = async () => {
  try {
    const res = await getSystemInfo();
    systemInfo.value = res.data.system;

    // 计算CPU核心数
    if (systemInfo.value?.cpu?.cores) {
      cores.value = systemInfo.value.cpu.cores;
    }
  } catch (error: any) {
    console.error('获取系统信息失败', error);
    MessagePlugin.error('获取系统信息失败');
  }
};

// 获取实时数据
const fetchRealtime = async () => {
  try {
    // 尝试调用真实API，如果失败则使用模拟数据
    try {
      const res = await getRealtime();
      realtime.value = res.data;
    } catch (apiError) {
      // API调用失败，使用模拟数据
      const mockData = {
        percent: Math.random() * 100,
        load: {
          load1: Math.random() * 4,
          load5: Math.random() * 4,
          load15: Math.random() * 4
        },
        mem: {
          total: 8589934592, // 8GB
          used: Math.random() * 8589934592,
          usedPercent: Math.random() * 100
        },
        disk_usage: [{
          path: '/',
          total: 107374182400, // 100GB
          used: Math.random() * 107374182400,
          usedPercent: Math.random() * 100
        }],
        net: [],
        disk_io: [],
        time: new Date().toISOString()
      };

      realtime.value = mockData;
    }

  } catch (error: any) {
    console.error('获取实时数据失败', error);
  }
};

// 获取统计信息
const fetchCountInfo = async () => {
  try {
    // 尝试调用真实API，如果失败则使用模拟数据
    try {
      const res = await getCountInfo();
      Object.assign(countInfo, res.data);
    } catch (apiError) {
      // API调用失败，使用模拟数据
      countInfo.website = Math.floor(Math.random() * 10);
      countInfo.database = Math.floor(Math.random() * 5);
      countInfo.ftp = Math.floor(Math.random() * 3);
      countInfo.cron = Math.floor(Math.random() * 8);
    }
  } catch (error: any) {
    console.error('获取统计信息失败', error);
  }
};

// 获取服务列表
const fetchServices = async () => {
  servicesLoading.value = true;
  try {
    const res = await getServices();
    services.value = res.data;
  } catch (error: any) {
    console.error('获取服务列表失败', error);
    MessagePlugin.error('获取服务列表失败');
  } finally {
    servicesLoading.value = false;
  }
};

// 刷新数据
const refreshData = async () => {
  loading.value = true;
  await Promise.all([fetchSystemInfo(), fetchRealtime(), fetchCountInfo(), fetchServices()]);
  loading.value = false;
};

// 处理服务操作
const handleServiceAction = async (action: string, service: any) => {
  try {
    if (action === 'start') {
      await startService(service.name);
      MessagePlugin.success(`服务 ${service.name} 已启动`);
    } else if (action === 'stop') {
      await stopService(service.name);
      MessagePlugin.success(`服务 ${service.name} 已停止`);
    } else if (action === 'restart') {
      await restartService(service.name);
      MessagePlugin.success(`服务 ${service.name} 已重启`);
    }

    // 刷新服务列表
    await fetchServices();
  } catch (error: any) {
    console.error(`服务操作失败: ${action}`, error);
    MessagePlugin.error(`服务操作失败: ${error.message || '未知错误'}`);
  }
};

// 格式化字节数
const formatBytes = (bytes: number | undefined) => {
  if (bytes === undefined) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let i = 0;
  let size = bytes;

  while (size >= 1024 && i < units.length - 1) {
    size /= 1024;
    i++;
  }

  return `${size.toFixed(2)} ${units[i]}`;
};

// 格式化运行时间
const formatUptime = (seconds: number | undefined) => {
  if (seconds === undefined) return '未知';

  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  const parts = [];
  if (days > 0) parts.push(`${days} 天`);
  if (hours > 0) parts.push(`${hours} 小时`);
  if (minutes > 0) parts.push(`${minutes} 分钟`);

  return parts.join(' ') || '刚刚启动';
};

// 格式化日期时间
const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '未知';

  const date = new Date(dateTime);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

// 获取负载百分比
const getLoadPercentage = () => {
  if (!realtime.value?.load?.load1 || cores.value === 0) return 0;
  return (realtime.value.load.load1 / cores.value) * 100;
};

// 获取负载状态
const getLoadStatus = () => {
  const percentage = getLoadPercentage();
  if (percentage >= 90) return '运行阻塞';
  if (percentage >= 80) return '运行缓慢';
  if (percentage >= 70) return '运行正常';
  return '运行流畅';
};

// 获取负载颜色
const getLoadColor = () => {
  const percentage = getLoadPercentage();
  if (percentage >= 90) return '#e34d59';
  if (percentage >= 80) return '#ed7b2f';
  if (percentage >= 70) return '#0052d9';
  return '#00a870';
};

// 获取CPU颜色
const getCpuColor = (usage: number | undefined) => {
  if (usage === undefined) return '';
  if (usage < 60) return '#00a870';
  if (usage < 80) return '#ed7b2f';
  return '#e34d59';
};

// 获取内存颜色
const getMemoryColor = (usage: number | undefined) => {
  if (usage === undefined) return '';
  if (usage < 70) return '#00a870';
  if (usage < 90) return '#ed7b2f';
  return '#e34d59';
};

// 获取磁盘颜色
const getDiskColor = (usage: number | undefined) => {
  if (usage === undefined) return '';
  if (usage < 80) return '#00a870';
  if (usage < 90) return '#ed7b2f';
  return '#e34d59';
};

// 获取服务状态主题
const getServiceStatusTheme = (status: string) => {
  if (status === 'active') return 'success';
  if (status === 'inactive') return 'danger';
  return 'warning';
};

// 清除当前数据
const clearCurrent = () => {
  total.netBytesSent = 0;
  total.netBytesRecv = 0;
  total.diskReadBytes = 0;
  total.diskWriteBytes = 0;
  total.diskRWBytes = 0;
  total.diskRWTime = 0;
  chartData.value = [];
};

// 定时器
let realtimeInterval: any = null;

// 组件挂载时获取数据
onMounted(async () => {
  await refreshData();

  // 启动实时数据更新
  realtimeInterval = setInterval(() => {
    fetchRealtime();
  }, 3000);
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (realtimeInterval) {
    clearInterval(realtimeInterval);
  }
});
</script>

<style lang="less" scoped>
.dashboard-container {
  .panel-header-section {
    margin-bottom: 16px;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .panel-info {
        h2 {
          margin: 0;
          font-size: 24px;
          font-weight: 600;
          color: #1f2937;
        }

        .panel-version {
          margin: 4px 0 0 0;
          color: #6b7280;
          font-size: 14px;
        }
      }
    }

    .stats-row {
      border-top: 1px solid #f3f4f6;
      padding-top: 20px;

      .stat-item {
        text-align: center;

        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #6b7280;
        }
      }
    }
  }

  .resource-overview-section {
    margin-bottom: 16px;

    .resource-item {
      text-align: center;
      padding: 16px;

      .resource-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        font-weight: 600;
        color: #374151;

        .t-icon {
          margin-right: 8px;
        }
      }

      .resource-progress {
        margin-bottom: 12px;
      }

      .resource-detail {
        font-size: 14px;
        color: #6b7280;

        .load-values {
          display: flex;
          flex-direction: column;
          gap: 4px;
          margin-top: 8px;

          span {
            font-size: 12px;
          }
        }
      }
    }
  }

  .system-info-section {
    margin-bottom: 16px;
  }

  .services-section {
    margin-bottom: 16px;
  }

  .monitoring-section {
    .chart-controls {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }

    .realtime-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 16px;
    }

    .chart-container {
      height: 400px;
      position: relative;

      .chart-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #9ca3af;

        .t-icon {
          margin-bottom: 12px;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    .panel-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .stats-row {
      .stat-item {
        margin-bottom: 16px;
      }
    }

    .resource-overview-section {
      .t-row {
        .t-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style>
