import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import { useUserStore } from '@/store/modules/user';

// 路由配置
const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
    },
  },
  {
    path: '/',
    component: () => import('@/layouts/default/index.vue'),
    redirect: '/dashboard',
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'dashboard',
        },
      },
      {
        path: 'website',
        name: 'Website',
        component: () => import('@/views/website/index.vue'),
        meta: {
          title: '网站管理',
          icon: 'internet',
        },
      },
      {
        path: 'database',
        name: 'Database',
        component: () => import('@/views/database/index.vue'),
        meta: {
          title: '数据库管理',
          icon: 'server',
        },
      },
      {
        path: 'file',
        name: 'File',
        component: () => import('@/views/file/index.vue'),
        meta: {
          title: '文件管理',
          icon: 'folder',
        },
      },
      {
        path: 'cron',
        name: 'Cron',
        component: () => import('@/views/cron/index.vue'),
        meta: {
          title: '定时任务',
          icon: 'time',
        },
      },
      {
        path: 'system',
        name: 'System',
        component: () => import('@/views/system/index.vue'),
        meta: {
          title: '系统管理',
          icon: 'setting',
        },
      },
      {
        path: 'terminal',
        name: 'Terminal',
        component: () => import('@/views/terminal/index.vue'),
        meta: {
          title: '终端',
          icon: 'console',
        },
      },
      {
        path: 'container',
        name: 'Container',
        component: () => import('@/views/container/index.vue'),
        meta: {
          title: '容器管理',
          icon: 'code',
        },
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '404',
      requiresAuth: false,
    },
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title ? to.meta.title + ' - ' : ''}服务器运维面板`;

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    const userStore = useUserStore();
    if (!userStore.token) {
      // 未登录，重定向到登录页
      next({ name: 'Login', query: { redirect: to.fullPath } });
      return;
    }
  }

  next();
});

export default router;
