import { createApp } from 'vue';
import TDesign from 'tdesign-vue-next';
import { createPinia } from 'pinia';
import router from './router';
import App from './App.vue';
import { useUserStore } from './store/modules/user';

// 引入样式
import 'tdesign-vue-next/es/style/index.css';
import './styles/global.less';

// 开发环境下引入路由测试工具
if (import.meta.env.DEV) {
  import('./utils/route-test');
}

const app = createApp(App);
const pinia = createPinia();

// 使用插件
app.use(TDesign);
app.use(pinia);
app.use(router);

// 初始化应用
const initApp = async () => {
  // 获取用户状态
  const userStore = useUserStore(pinia);

  // 如果有令牌，获取用户信息
  if (userStore.token) {
    try {
      await userStore.fetchUserInfo();
    } catch (error) {
      console.error('初始化用户信息失败', error);
    }
  }

  // 挂载应用
  app.mount('#app');
};

// 启动应用
initApp();
