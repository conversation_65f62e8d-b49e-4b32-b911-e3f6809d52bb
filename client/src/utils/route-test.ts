// 路由配置测试工具
import router from '@/router';

export interface RouteTestResult {
  path: string;
  name: string;
  exists: boolean;
  error?: string;
}

/**
 * 测试路由是否存在
 */
export function testRoute(name: string, params?: Record<string, any>): RouteTestResult {
  try {
    const route = router.resolve({ name, params });
    
    return {
      path: route.path,
      name: name,
      exists: route.name !== undefined,
      error: route.name === undefined ? `Route "${name}" not found` : undefined
    };
  } catch (error) {
    return {
      path: '',
      name: name,
      exists: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * 测试容器管理相关路由
 */
export function testContainerRoutes(): RouteTestResult[] {
  const routes = [
    'Container',
    'Dashboard',
    'Website',
    'Database',
    'File',
    'Cron',
    'System',
    'Terminal'
  ];

  return routes.map(route => testRoute(route));
}

/**
 * 测试容器管理URL生成
 */
export function testContainerUrls(): Array<{ description: string; url: string; valid: boolean }> {
  const tests = [
    {
      description: '容器管理主页面',
      url: router.resolve({ name: 'Container' }).href,
      valid: true
    },
    {
      description: '容器列表页面',
      url: router.resolve({ name: 'Container', query: { tab: 'list' } }).href,
      valid: true
    },
    {
      description: '镜像管理页面',
      url: router.resolve({ name: 'Container', query: { tab: 'image' } }).href,
      valid: true
    },
    {
      description: '数据卷管理页面',
      url: router.resolve({ name: 'Container', query: { tab: 'volume' } }).href,
      valid: true
    },
    {
      description: '网络管理页面',
      url: router.resolve({ name: 'Container', query: { tab: 'network' } }).href,
      valid: true
    },
    {
      description: '编排管理页面',
      url: router.resolve({ name: 'Container', query: { tab: 'compose' } }).href,
      valid: true
    }
  ];

  return tests;
}

/**
 * 运行完整的路由测试
 */
export function runRouteTests(): {
  routes: RouteTestResult[];
  urls: Array<{ description: string; url: string; valid: boolean }>;
  summary: {
    totalRoutes: number;
    validRoutes: number;
    invalidRoutes: number;
    success: boolean;
  };
} {
  const routes = testContainerRoutes();
  const urls = testContainerUrls();
  
  const validRoutes = routes.filter(r => r.exists).length;
  const invalidRoutes = routes.filter(r => !r.exists).length;
  
  return {
    routes,
    urls,
    summary: {
      totalRoutes: routes.length,
      validRoutes,
      invalidRoutes,
      success: invalidRoutes === 0
    }
  };
}

/**
 * 在控制台输出测试结果
 */
export function logTestResults(): void {
  const results = runRouteTests();
  
  console.group('🔍 路由配置测试结果');
  
  console.group('📍 路由存在性测试');
  results.routes.forEach(route => {
    if (route.exists) {
      console.log(`✅ ${route.name} → ${route.path}`);
    } else {
      console.error(`❌ ${route.name} → ${route.error}`);
    }
  });
  console.groupEnd();
  
  console.group('🔗 URL生成测试');
  results.urls.forEach(test => {
    console.log(`${test.valid ? '✅' : '❌'} ${test.description} → ${test.url}`);
  });
  console.groupEnd();
  
  console.group('📊 测试总结');
  console.log(`总路由数: ${results.summary.totalRoutes}`);
  console.log(`有效路由: ${results.summary.validRoutes}`);
  console.log(`无效路由: ${results.summary.invalidRoutes}`);
  console.log(`测试结果: ${results.summary.success ? '✅ 通过' : '❌ 失败'}`);
  console.groupEnd();
  
  console.groupEnd();
}

// 开发环境下自动运行测试
if (import.meta.env.DEV) {
  // 延迟执行，确保路由已初始化
  setTimeout(() => {
    logTestResults();
  }, 1000);
}
