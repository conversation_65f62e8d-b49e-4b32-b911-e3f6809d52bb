// 系统信息类型定义
export interface SystemInfo {
  hostname: string;
  os: string;
  kernel: string;
  arch: string;
  cpu: CPUInfo;
  memory: MemoryInfo;
  disk: DiskInfo;
  uptime: number;
  load_avg: number[];
  time: string;
}

// CPU信息
export interface CPUInfo {
  model: string;
  cores: number;
  threads: number;
  usage: number;
}

// 内存信息
export interface MemoryInfo {
  total: number;
  used: number;
  free: number;
  available: number;
  swap_total: number;
  swap_used: number;
  swap_free: number;
  usage: number;
}

// 磁盘信息
export interface DiskInfo {
  total: number;
  used: number;
  free: number;
  usage: number;
}

// 网络信息
export interface NetworkInfo {
  interface: string;
  rx_bytes: number;
  tx_bytes: number;
  rx_packets: number;
  tx_packets: number;
}

// 系统状态
export interface SystemStatus {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  load_average: number[];
  network: NetworkInfo[];
}
