{"name": "panel-client", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.2", "pinia": "^2.1.7", "tdesign-vue-next": "^1.8.1", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.0", "less": "^4.2.0", "typescript": "^5.3.2", "vite": "^5.0.2", "vue-tsc": "^1.8.22"}}