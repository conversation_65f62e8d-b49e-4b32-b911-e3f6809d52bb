# `go test -c` 生成的二进制文件
*.test

# go coverage 工具
*.out
*.prof
*.cgo1.go
*.cgo2.c
_cgo_defun.c
_cgo_gotypes.go
_cgo_export.*

# 编译文件
*.com
*.class
*.dll
*.exe
*.o
*.so

# 压缩包
# Git 自带压缩,如果这些压缩包里有代码,建议解压后 commit
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 日志文件和数据库及配置
*.log
*.sqlite
*.db
config.yml

# 临时文件
tmp/
.tmp/

# 系统生成文件
.DS_Store
.DS_Store?
.AppleDouble
.LSOverride
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
.TemporaryItems
.fseventsd
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# IDE 和编辑器
.idea/
/go_build_*
out/
.vscode/
.vscode/settings.json
*.sublime*
__debug_bin
.project

# node_modules
node_modules/

server/cmd/server/__debug_bin*