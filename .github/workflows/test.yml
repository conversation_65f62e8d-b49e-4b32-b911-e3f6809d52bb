name: Test
on:
  push:
    branches:
      - main
  pull_request:
jobs:
  unit:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          cache: true
          go-version: 'stable'
      - name: Install dependencies
        run: sudo apt-get install -y curl jq
      - name: Set up environment
        run: |
          cp config.example.yml config.yml
      - name: Run tests
        run: sudo go test -v -coverprofile="coverage.out" ./...
      - name: Upload coverage report to Codecov
        uses: codecov/codecov-action@v5
        with:
          file: ./coverage.out
          token: ${{ secrets.CODECOV }}
