name: L10n
on:
  push:
    branches:
      - main
jobs:
  xgotext:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          cache: true
          go-version: 'stable'
      - name: Install xgotext
        run: |
          go install github.com/leonelquinteros/gotext/cli/xgotext@latest
      - name: Generate pot files
        run: |
          ~/go/bin/xgotext -default backend -pkg-tree ./cmd/web -out ./pkg/embed/locales
          git pull
      - uses: stefanzweifel/git-auto-commit-action@v5
        name: Commit changes
        with:
          commit_message: "chore(l10n): update backend pot files"
  vue3-gettext:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: true
          package_json_file: web/package.json
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'pnpm'
          cache-dependency-path: web/pnpm-lock.yaml
      - name: Generate pot files and merge po files
        working-directory: web
        run: |
          pnpm run gettext:extract
          git pull
      - uses: stefanzweifel/git-auto-commit-action@v5
        name: Commit changes
        with:
          commit_message: "chore(l10n): update frontend pot files"
