name: Release
on:
  push:
    tags:
      - 'v*'
permissions:
  contents: write
jobs:
  goreleaser:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          cache: true
          go-version: 'stable'
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: true
          package_json_file: web/package.json
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'pnpm'
          cache-dependency-path: web/pnpm-lock.yaml
      - name: Build frontend
        working-directory: web
        run: |
          cp .env.production .env
          cp settings/proxy-config.example.ts settings/proxy-config.ts
          pnpm run gettext:compile
          pnpm build
      - name: Set environment variables
        run: |
          echo "GOVERSION=$(go version | cut -d' ' -f3)" >> $GITHUB_ENV
          echo "HOSTNAME=$(hostname)" >> $GITHUB_ENV
      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          version: latest
          args: release --clean
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
