name: Issue Auto Lock
on:
  schedule:
    - cron: "*/5 * * * *"
  workflow_dispatch:
  issues:
    types: [ opened ]
permissions:
  issues: write
  contents: read
jobs:
  issue-lock:
    runs-on: ubuntu-latest
    steps:
      - name: Set GH_REPO
        run: echo "GH_REPO=${{ github.repository }}" >> $GITHUB_ENV
      - name: Auto Lock Issue
        uses: devhaozi/issue-auto-lock@v1
        with:
          gh_repo: ${{ github.repository }}
          gh_token: ${{ secrets.GITHUB_TOKEN }}
          issue_labels: "⭐ No Star"
