name: Mockery
on:
  push:
    branches:
      - main
jobs:
  mockery:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          cache: true
          go-version: 'stable'
      - name: Install Mockery
        run: |
          go install github.com/vektra/mockery/v2@latest
      - name: Generate Mocks
        run: |
          ~/go/bin/mockery
          git pull
      - uses: stefanzweifel/git-auto-commit-action@v5
        name: Commit changes
        with:
          commit_message: "chore: update mocks"
