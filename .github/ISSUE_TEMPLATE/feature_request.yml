name: ✨ 功能请求 (Feature Request)
description: 为这个项目提出一个想法 (Suggest an idea for this project)
type: ✨ Feature

body:
  - type: markdown
    attributes:
      value: |
        **请仅使用 简体中文 或 英文 进行填写**
        **Please only use Simplified Chinese or English to fill in**
  - type: checkboxes
    attributes:
      label: 在提问之前 (Before Asking)
      description: |
        提问之前，先回答几个小问题。
        Before asking questions, answer a few quick questions.
      options:
        - label: 我已经搜索了全部 Issues 和 Commits (I have searched all Issues and Commits)
          required: true
        - label: 它们当中没有我将要提交的新功能 (None of them have new feature that I'm going to submit)
          required: true
  - type: textarea
    id: feature
    attributes:
      label: 描述功能 (Describe Feature)
      description: |
        简明概要地描述您的新功能，以及它将解决什么问题。
        Briefly describe your new feature and what problem it will solve.
    validations:
      required: true
  - type: textarea
    id: workflow
    attributes:
      label: 工作流程 (WorkFlow)
      description: |
        请向我们提供有关该功能的实现流程。
        Please provide us with the implementation flow for this feature.
      value: |
        1. 新建文件 ....
        2. 添加函数 ....
        3. ...
    validations:
      required: true
  - type: textarea
    id: others
    attributes:
      label: 还有别的吗 (Anything Else)
      description: |
        运行环境？浏览器？软件版本？相关的配置？链接？参考资料？
        Environment? Browser? Software version? Related configuration? Link? References?
        任何能让我们对该功能的实现有更多帮助的东西。
        Anything that can help us more with the implementation of this feature.
    validations:
      required: false
