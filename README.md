# 服务器运维面板

一个轻量级、高效的服务器管理工具，专注于提供核心的服务器运维功能，采用现代化的技术栈开发。

## 技术栈

### 后端
- **语言**: Go
- **HTTP框架**: Hertz (字节跳动开源的高性能HTTP框架)
- **ORM**: GORM
- **数据库**: SQLite

### 前端
- **框架**: Vue 3 + Vite
- **UI组件库**: TDesign-Vue (腾讯开源的企业级设计系统)
- **状态管理**: Pinia
- **HTTP客户端**: Axios

## 核心功能

- **系统监控**: CPU、内存、磁盘、网络等实时监控
- **服务管理**: 基于systemctl的服务启停、状态查看
- **网站管理**: 多站点、多域名、SSL证书、Nginx配置
- **数据库管理**: MySQL/PostgreSQL数据库创建、用户管理
- **文件管理**: 文件浏览、上传下载、权限修改
- **定时任务**: Cron任务创建和管理
- **安全管理**: 用户认证、防火墙配置

## 快速开始

### 环境要求

- Go 1.20+
- Node.js 18+
- npm 9+

### 后端开发

1. 进入后端目录
   ```bash
   cd server
   ```

2. 安装依赖
   ```bash
   go mod tidy
   ```

3. 运行开发服务器
   ```bash
   go run cmd/server/main.go
   ```

### 前端开发

1. 进入前端目录
   ```bash
   cd client
   ```

2. 安装依赖
   ```bash
   npm install
   ```

3. 运行开发服务器
   ```bash
   npm run dev
   ```

## 部署指南

### 构建

1. 构建后端
   ```bash
   cd server
   go build -o panel cmd/server/main.go
   ```

2. 构建前端
   ```bash
   cd client
   npm run build
   ```

### 部署

1. 将编译好的后端二进制文件和前端静态文件部署到服务器
2. 配置服务器环境
3. 启动服务
   ```bash
   ./panel
   ```

## 项目结构

### 后端目录结构

```
server/
├── biz/                  # 业务逻辑层
│   ├── handler/          # 请求处理器
│   ├── model/            # 数据模型
│   ├── repo/             # 数据仓库接口
│   └── service/          # 业务服务
├── cmd/                  # 入口点
│   ├── server/           # 服务器入口
│   └── cli/              # 命令行工具
├── config/               # 配置文件
├── idl/                  # 接口定义文件
├── infra/                # 基础设施
│   ├── database/         # 数据库连接
│   ├── logger/           # 日志组件
│   └── middleware/       # 中间件
├── pkg/                  # 公共包
│   ├── constant/         # 常量定义
│   ├── firewall/         # 防火墙操作
│   ├── io/               # IO操作
│   ├── nginx/            # Nginx操作
│   ├── os/               # 操作系统相关
│   ├── shell/            # Shell命令执行
│   ├── systemctl/        # 系统服务管理
│   └── utils/            # 工具函数
└── router/               # 路由定义
```

### 前端目录结构

```
client/
├── public/               # 静态资源
├── src/
│   ├── api/              # API请求
│   │   ├── modules/      # 按模块划分的API
│   │   └── request.ts    # 请求封装
│   ├── assets/           # 静态资源
│   ├── components/       # 公共组件
│   ├── composables/      # 组合式API
│   ├── config/           # 配置文件
│   ├── layouts/          # 布局组件
│   ├── router/           # 路由配置
│   ├── store/            # Pinia状态管理
│   ├── styles/           # 样式文件
│   ├── utils/            # 工具函数
│   ├── views/            # 页面视图
│   ├── App.vue           # 根组件
│   └── main.ts           # 入口文件
├── index.html            # HTML模板
├── tsconfig.json         # TypeScript配置
└── vite.config.ts        # Vite配置
```

## 许可证

本项目采用 MIT 许可证
